import 'package:firebase_auth/firebase_auth.dart';

class AuthService {
  // Singleton instance
  static final AuthService _instance = AuthService._internal();

  // Private constructor
  AuthService._internal();

  // Factory constructor
  factory AuthService() => _instance;

  // FirebaseAuth instance
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  /// Login with Custom Token
  Future<User?> loginWithCustomToken(String customToken) async {
    User? user = getCurrentUser();
    if (user == null) {
      try {
        UserCredential userCredential =
            await _firebaseAuth.signInWithCustomToken(customToken);
        return userCredential.user;
      } catch (e) {
        print("Error logging in with custom token: $e");
        return null;
      }
    }
    return null;
  }

  /// Logout
  Future<void> logout() async {
    User? user = getCurrentUser();

    if (user != null) {
      try {
        await _firebaseAuth.signOut();
        print("User logged out successfully");
      } catch (e) {
        print("Error logging out: $e");
      }
    }
  }

  /// Get current user
  User? getCurrentUser() {
    return _firebaseAuth.currentUser;
  }
}
