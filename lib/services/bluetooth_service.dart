import 'dart:async';
import 'dart:io';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:SAiWELL/utils/permission_handler_util.dart';
import 'package:SAiWELL/utils/flutter_toast.dart';
import 'package:app_settings/app_settings.dart';

class BluetoothService {
  static final BluetoothService _singleton = BluetoothService._internal();

  factory BluetoothService() {
    return _singleton;
  }

  BluetoothService._internal();

  static final flutterReactiveBle = FlutterReactiveBle();

  bool checkIsBluetoothReady() {
    return ["ready"].contains(flutterReactiveBle.status.name);
  }

  Future<bool> checkPermission() async {
    final permissionUtil = PermissionHandlerUtil();
    
    if (Platform.isIOS) {
      // iOS doesn't need explicit Bluetooth permissions
      return true;
    } else {
      // Check all required Bluetooth permissions at once
      final permissionsGranted = await permissionUtil.checkMultiplePermissions(
        [
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.locationWhenInUse,
        ],
        title: 'Bluetooth Permissions Required',
        message: 'This app needs bluetooth and location access to connect to nearby devices. Please enable these permissions.',
      );
      
      if (!permissionsGranted) {
        CustomToast.error("Bluetooth and Location permissions are required");
      }
      
      return permissionsGranted;
    }
  }

  Future<void> openBluetoothSettings() async {
    await AppSettings.openAppSettings(type: AppSettingsType.bluetooth);
  }
}
