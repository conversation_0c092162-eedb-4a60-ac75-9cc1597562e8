import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:record/record.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:SAiWELL/utils/permission_handler_util.dart';
import 'package:SAiWELL/utils/stream_utils.dart';

import '../models/health_datatype.dart';

class AudioRecordingService extends GetxController {
  // Singleton pattern implementation
  static final AudioRecordingService _instance =
      AudioRecordingService._internal();
  factory AudioRecordingService() => _instance;
  AudioRecordingService._internal();

  // Recorder and player instances
  final AudioRecorder _audioRecorder = AudioRecorder();
  PlayerController playerController = PlayerController();
  RecorderController wavefromController = RecorderController();

  // State management
  Rx<RecordingState> recordingState = RecordingState.initial.obs;
  Rx<PlaybackState> playbackState = (PlaybackState.initial).obs;
  final ValueNotifier<Duration> recordingDuration =
      ValueNotifier(Duration.zero);
  final ValueNotifier<Duration> playbackPosition = ValueNotifier(Duration.zero);

  // Private tracking variables
  String? _currentRecordingPath;
  Timer? _durationTimer;
  StreamSubscription<PlayerState>? _playbackStateSubscription;

  // Improved time tracking variables
  Duration _accumulatedDuration = Duration.zero;
  DateTime? _recordingStartTime;
  DateTime? _pauseTime;

  // Getters
  String? get currentRecordingPath => _currentRecordingPath;
  AudioRecorder get audioRecorder => _audioRecorder;
  RecorderController get recorderController => wavefromController;

  Future<String> _generateUniqueRecordingPath() async {
    final directory = Platform.isIOS
        ? await getApplicationDocumentsDirectory()
        : await getTemporaryDirectory();
    final timestamp = DateTime.now().toUtc().millisecondsSinceEpoch;
    String uid = await prefsService.getUid();
    return '${directory.path}/${uid}_$timestamp.wav';
    //   return '${directory.path}/voice_$timestamp.wav';
  }

  // Recording Methods
  Future<bool> checkMicrophonePermission() async {
    final permissionUtil = PermissionHandlerUtil();
    return permissionUtil.checkMicrophonePermission();
  }

  Future<bool> startRecording() async {
    try {
      if (recordingState.value == RecordingState.recording ||
          recordingState.value == RecordingState.paused) {
        debugPrint('Recording already in progress');
        return false;
      }

      _accumulatedDuration = Duration.zero;
      _recordingStartTime = null;
      _pauseTime = null;

      _currentRecordingPath = await _generateUniqueRecordingPath();

      // Configure recording
      const config = RecordConfig(
        encoder: AudioEncoder.wav,
        sampleRate: 48000,
        bitRate: 320000,
        numChannels: 1,
      );

      await _audioRecorder.start(
        config,
        path: _currentRecordingPath!,
      );

      // Initialize waveform capturing
      wavefromController = RecorderController();
      wavefromController.record();

      _startDurationTracking();
      recordingState.value = RecordingState.recording;
      return true;
    } catch (e) {
      debugPrint('Error starting recording: $e');
      _resetRecordingState();
      return false;
    }
  }

  void _startDurationTracking() {
    _durationTimer?.cancel();
    _recordingStartTime = DateTime.now();

    _durationTimer = Timer.periodic(const Duration(milliseconds: 100), (_) {
      if (_recordingStartTime != null) {
        final currentDuration = DateTime.now().difference(_recordingStartTime!);
        recordingDuration.value = _accumulatedDuration + currentDuration;

        // Update waveform data in real-time
        wavefromController.refresh();
      }
    });
  }

  Future<String?> pauseRecording() async {
    try {
      if (recordingState.value != RecordingState.recording) {
        debugPrint('Cannot pause: Not currently recording');
        return null;
      }

      await _audioRecorder.pause();
      wavefromController.pause();
      _durationTimer?.cancel();

      if (_recordingStartTime != null) {
        _accumulatedDuration += DateTime.now().difference(_recordingStartTime!);
      }

      _pauseTime = DateTime.now();
      _recordingStartTime = null;

      recordingState.value = RecordingState.paused;
      return _currentRecordingPath;
    } catch (e) {
      debugPrint('Error pausing recording: $e');
      _resetRecordingState();
      return null;
    }
  }

  Future<String?> stopRecording() async {
    try {
      if (recordingState.value == RecordingState.initial) {
        debugPrint('No active recording to stop');
        return null;
      }

      // Cancel the timer first to prevent further updates
      _durationTimer?.cancel();

      // Calculate final duration only if we're currently recording
      if (recordingState.value == RecordingState.recording &&
          _recordingStartTime != null) {
        _accumulatedDuration += DateTime.now().difference(_recordingStartTime!);
      }

      final path = await _audioRecorder.stop();
      wavefromController.stop();

      recordingDuration.value = _accumulatedDuration;
      recordingState.value = RecordingState.stopped;

      _recordingStartTime = null;
      _pauseTime = null;

      return path;
    } catch (e) {
      debugPrint('Error stopping recording: $e');
      _resetRecordingState();
      return null;
    }
  }

  Future<String?> resumeRecording() async {
    try {
      if (recordingState.value != RecordingState.paused) {
        debugPrint('Cannot resume: Not in paused state');
        return null;
      }

      await _audioRecorder.resume();
      wavefromController.record();
      _recordingStartTime = DateTime.now();

      _startDurationTracking();
      recordingState.value = RecordingState.recording;

      return _currentRecordingPath;
    } catch (e) {
      debugPrint('Error resuming recording: $e');
      _resetRecordingState();
      return null;
    }
  }

  Future<void> resetRecording() async {
    try {
      // Stop recording if in progress
      if (recordingState.value == RecordingState.recording ||
          recordingState.value == RecordingState.paused) {
        await _audioRecorder.stop();
      }

      // Reset all states and tracking variables
      _resetRecordingState();

      // Delete the current recording file if it exists
      if (_currentRecordingPath != null) {
        try {
          final file = File(_currentRecordingPath!);
          if (file.existsSync()) {
            await file.delete();
          }
        } catch (e) {
          debugPrint('Error deleting recording file during reset: $e');
        }
      }

      // Clear the current recording path
      _currentRecordingPath = null;

      // Do NOT dispose the recorder here since we want to use it again
    } catch (e) {
      debugPrint('Error in resetRecording: $e');
    }
  }

  Future<void> startPlayback() async {
    try {
      if (_currentRecordingPath == null) {
        debugPrint("_currentRecordingPath is null");
        return;
      }

      await _playbackStateSubscription?.cancel();

      // Add listener for playback state
      _playbackStateSubscription =
          playerController.onPlayerStateChanged.listenSafely((state) {
        playbackState.value = _convertPlaybackState(state);
        if (state == PlayerState.stopped) {
          playbackPosition.value = Duration.zero;
        }
      });

      // Add listener for playback position
      playerController.onCurrentDurationChanged.listenSafely((duration) {
        playbackPosition.value = Duration(milliseconds: duration);
      });

      // Start playback
      await playerController.startPlayer();
      playbackState.value = PlaybackState.playing;
    } catch (e) {
      debugPrint('Error starting playback: $e');
      playbackState.value = PlaybackState.initial;
    }
  }

  Future<void> pausePlayback() async {
    try {
      await playerController.pausePlayer();
      playbackState.value = PlaybackState.paused;
    } catch (e) {
      debugPrint('Error pausing playback: $e');
    }
  }

  Future<void> resumePlayback() async {
    try {
      await playerController.startPlayer();
      playbackState.value = PlaybackState.playing;
    } catch (e) {
      debugPrint('Error resuming playback: $e');
    }
  }

  Future<void> stopPlayback() async {
    try {
      await playerController.stopPlayer();
      playbackState.value = PlaybackState.initial;
      playbackPosition.value = Duration.zero;
    } catch (e) {
      debugPrint('Error stopping playback: $e');
    }
  }

  PlaybackState _convertPlaybackState(PlayerState state) {
    switch (state) {
      case PlayerState.stopped:
        return PlaybackState.stopped;
      case PlayerState.playing:
        return PlaybackState.playing;
      case PlayerState.paused:
        return PlaybackState.paused;
      default:
        return PlaybackState.initial;
    }
  }

  // Utility method to format duration
  String formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  // Comprehensive reset method
  Future<void> reset() async {
    try {
      // Stop any ongoing recording or playback
      if (recordingState.value != RecordingState.initial) {
        try {
          await _audioRecorder.stop();
        } catch (e) {
          debugPrint('Error stopping recording: $e');
        }
      }

      try {
        await playerController.stopPlayer();
      } catch (e) {
        debugPrint('Error stopping player: $e');
      }

      // Reset all states
      recordingState.value = RecordingState.initial;
      playbackState.value = PlaybackState.initial;
      recordingDuration.value = Duration.zero;
      playbackPosition.value = Duration.zero;

      // Reset duration tracking
      _durationTimer?.cancel();
      _accumulatedDuration = Duration.zero;
      _recordingStartTime = null;
      _pauseTime = null;

      // Clean up current recording file
      if (_currentRecordingPath != null) {
        try {
          final file = File(_currentRecordingPath!);
          if (file.existsSync()) {
            file.deleteSync();
          }
        } catch (e) {
          debugPrint('Error deleting recording file: $e');
        }
      }
      // Clear the current recording path
      _currentRecordingPath = null;
    } catch (e) {
      debugPrint('Error during complete reset: $e');
    }
  }

  Future<void> resetPlayback() async {
    try {
      await playerController.stopPlayer();
      playbackState.value = PlaybackState.initial;

      if (_currentRecordingPath != null) {
        try {
          final file = File(_currentRecordingPath!);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          debugPrint('Error deleting prepared audio file: $e');
        }
        _currentRecordingPath = null;
      }

      // Reset playback position
      playbackPosition.value = Duration.zero;

      debugPrint('Playback state reset successfully');
    } catch (e) {
      debugPrint('Error resetting playback: $e');
    }
  }

  void _resetRecordingState() {
    recordingState.value = RecordingState.initial;
    recordingDuration.value = Duration.zero;
    _accumulatedDuration = Duration.zero;
    _recordingStartTime = null;
    _pauseTime = null;
    _durationTimer?.cancel();
  }

  @override
  void dispose() {
    super.dispose();
    _audioRecorder.dispose();
    wavefromController.dispose();
    _playbackStateSubscription?.cancel();
    recordingState.value = RecordingState.initial;
    recordingDuration.dispose();
    playbackPosition.dispose();
  }
}

enum RecordingState { initial, recording, paused, stopped }

enum PlaybackState { initial, playing, paused, stopped }
