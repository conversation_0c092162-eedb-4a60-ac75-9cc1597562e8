import 'dart:async';

import 'package:SAiWELL/common_controllers/global_controller.dart';
import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/models/db_enums.dart';
import 'package:SAiWELL/models/temp_firebase_model.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';
import '../models/sleep_firebase_model.dart';
import '../models/step_firebase_v2_model.dart';
import 'firestore_service.dart';
import 'native_communicator.dart';

class RingDataCollector {
  NativeCommunicator nativeCommunicator = NativeCommunicator();
  FirebaseFirestore firestore = FirebaseFirestore.instanceFor(
      app: Firebase.app(), databaseId: fbDatabaseId); 
  PrefsService prefsService = PrefsService();
  GlobalController globalController = Get.find<GlobalController>();

  Future<Map<String, dynamic>?> getLatestDataV2({
    required V2CollectionNames collectionNameV2,
  }) async {
    try {
      String deviceDbName = DBCollectionName.deviceData.getValue();
      String uid = await prefsService.getUid();
      String collectionName = collectionNameV2.name;
      
      if (uid.isEmpty) {
        print('ERROR: User ID is empty, cannot fetch data from Firebase');
        return null;
      }

      if (collectionNameV2 == V2CollectionNames.saiwellRingStepV2) {
        return await getAggregatedStepsData(
          deviceDbName: deviceDbName,
          uid: uid,
          collectionName: collectionName,
        );
      } else if (collectionNameV2 == V2CollectionNames.saiwellRingSleepV2) {
        return await getAggregatedSleepData(
          deviceDbName: deviceDbName,
          uid: uid,
          collectionName: collectionName,
        );
      } else {
        // Get data for today only
        final now = DateTime.now();
        final todayMidnight = DateTime(
          now.year,
          now.month,
          now.day,
          0,
          0,
          0,
        );

        final startTimestamp = todayMidnight.millisecondsSinceEpoch ~/ 1000;
        final endTimestamp = now.millisecondsSinceEpoch ~/ 1000;
        
        try {
          final QuerySnapshot snapshot = await firestore
              .collection(deviceDbName)
              .doc(uid)
              .collection(collectionName)
              .where(FieldPath.documentId,
                  isGreaterThanOrEqualTo: startTimestamp.toString())
              .where(FieldPath.documentId,
                  isLessThanOrEqualTo: endTimestamp.toString())
              .orderBy(FieldPath.documentId, descending: true)
              .limit(1)
              .get();
          
          if (snapshot.docs.isEmpty) {
            // Return null if no data for today
            return null;
          }
          
          return snapshot.docs.first.data() as Map<String, dynamic>;
        } catch (e) {
          print('Error executing Firebase query for $collectionName: $e');
          return null;
        }
      }
    } catch (e) {
      print('Error in getLatestDataV2 for ${collectionNameV2.name}: $e');
      return null;
    }
  }

  Future<Map<String, dynamic>?> getAggregatedStepsData({
    required String deviceDbName,
    required String uid,
    required String collectionName,
  }) async {
    final now = DateTime.now();
    final todayMidnight = DateTime(
      now.year,
      now.month,
      now.day,
      0,
      0,
      0,
    );

    // Convert to seconds since epoch
    final startTimestamp = todayMidnight.millisecondsSinceEpoch ~/ 1000;
    final endTimestamp = now.millisecondsSinceEpoch ~/ 1000;

    try {
      final QuerySnapshot snapshot = await firestore
          .collection(deviceDbName)
          .doc(uid)
          .collection(collectionName)
          .where(FieldPath.documentId,
              isGreaterThanOrEqualTo: startTimestamp.toString())
          .where(FieldPath.documentId,
              isLessThanOrEqualTo: endTimestamp.toString())
          .get();

      if (snapshot.docs.isEmpty) {
        // Return null if no data for today
        return null;
      }

      // Process today's documents
      Map<String, dynamic> combinedData = {
        'calories': 0,
        'distance': 0,
        'step': 0,
      };

      num? earliestStartTime;
      num? latestEndTime;

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final stepsData = StepFirebaseV2Model.fromJson(data);
        final timestamp = int.parse(doc.id);

        // Update timestamps
        if (earliestStartTime == null || timestamp < earliestStartTime) {
          earliestStartTime = timestamp;
        }
        if (latestEndTime == null || timestamp > latestEndTime) {
          latestEndTime = timestamp;
        }

        // Aggregate values
        combinedData['calories'] =
            (combinedData['calories'] ?? 0) + (stepsData.calories ?? 0);
        combinedData['distance'] =
            (combinedData['distance'] ?? 0) + (stepsData.distance ?? 0);
        combinedData['step'] = 
            (combinedData['step'] ?? 0) + (stepsData.step ?? 0);
      }

      // Add timestamp information and convert distance
      combinedData['startTimeStamp'] = earliestStartTime;
      combinedData['endTimeStamp'] = latestEndTime;
      combinedData['distance'] = combinedData['distance'] * 0.00062;
      
      // Add vitalCollectedTimestamp for consistency with date checking
      combinedData['vitalCollectedTimestamp'] = Timestamp.fromDate(now);
      
      return combinedData;
    } catch (e) {
      print("Error fetching steps data: $e");
      return null;
    }
  }

  Future<Map<String, dynamic>?> getAggregatedSleepData({
    required String deviceDbName,
    required String uid,
    required String collectionName,
  }) async {
    final now = DateTime.now();
    final yesterday9PM = DateTime(
      now.year,
      now.month,
      now.day - 1,
      21,
      0,
      0,
    );

    // Convert to seconds since epoch
    final startTimestamp = yesterday9PM.millisecondsSinceEpoch ~/ 1000;
    final endTimestamp = now.millisecondsSinceEpoch ~/ 1000;

    final QuerySnapshot snapshot = await firestore
        .collection(deviceDbName)
        .doc(uid)
        .collection(collectionName)
        .where(FieldPath.documentId,
            isGreaterThanOrEqualTo: startTimestamp.toString())
        .where(FieldPath.documentId,
            isLessThanOrEqualTo: endTimestamp.toString())
        .get();

    if (snapshot.docs.isEmpty) {
      return null;
    }

    // Combine all sleep data
    Map<String, dynamic> combinedData = {
      'remSleepMinutes': 0,
      'deepSleepMinutes': 0,
      'lightSleepCount': 0,
      'lightSleepMinutes': 0,
      'deepSleepCount': 0,
      'awakeSleepMinutes': 0,
      'sleepDetailDatas': <Map<String, dynamic>>[],
    };

    num? earliestStartTime;
    num? latestEndTime;

    for (var doc in snapshot.docs) {
      final data = doc.data() as Map<String, dynamic>;
      final sleepData = SleepFirebaseModel.fromJson(data);

      // Update start and end timestamps
      if (earliestStartTime == null ||
          sleepData.startTimeStamp! < earliestStartTime) {
        earliestStartTime = sleepData.startTimeStamp;
      }
      if (latestEndTime == null || sleepData.endTimeStamp! > latestEndTime) {
        latestEndTime = sleepData.endTimeStamp;
      }
      // Combine numeric values
      combinedData['remSleepMinutes'] = (combinedData['remSleepMinutes'] ?? 0) +
          (sleepData.remSleepMinutes ?? 0);
      combinedData['deepSleepMinutes'] =
          (combinedData['deepSleepMinutes'] ?? 0) +
              (sleepData.deepSleepMinutes ?? 0);
      combinedData['lightSleepCount'] = (combinedData['lightSleepCount'] ?? 0) +
          (sleepData.lightSleepCount ?? 0);
      combinedData['lightSleepMinutes'] =
          (combinedData['lightSleepMinutes'] ?? 0) +
              (sleepData.lightSleepMinutes ?? 0);
      combinedData['deepSleepCount'] = (combinedData['deepSleepCount'] ?? 0) +
          (sleepData.deepSleepCount ?? 0);
      combinedData['awakeSleepMinutes'] =
          (combinedData['awakeSleepMinutes'] ?? 0) +
              (sleepData.awakeSleepMinutes ?? 0);

      if (sleepData.sleepDetailDatas != null) {
        combinedData['sleepDetailDatas'].addAll(sleepData.sleepDetailDatas!
            .map((detail) => detail.toJson())
            .toList());
      }
    }

    // Add start and end timestamps to combined data
    combinedData['startTimeStamp'] = earliestStartTime;
    combinedData['endTimeStamp'] = latestEndTime;
    
    // Add vitalCollectedTimestamp for consistency with other data types
    combinedData['vitalCollectedTimestamp'] = Timestamp.fromDate(now);

    return combinedData;
  }

  Future<int> getLastSyncedAtTime() async {
    String uid = await prefsService.getUid();
    DocumentSnapshot userDoc = await firestore
        .collection(DBCollectionName.deviceDataMeta.getValue())
        .doc(uid)
        .get();

    int milliseconds = 0;

    if (userDoc.exists) {
      var data = userDoc.data() as Map<String, dynamic>?;
      milliseconds = (data?['saiwellRingLastUpdated'] as Timestamp)
          .toDate()
          .millisecondsSinceEpoch;
    }
    return milliseconds;
  }

  Future<Map<String, dynamic>> getTemperatureDifferenceForToday() async {
    try {
      String deviceDbName = DBCollectionName.deviceData.getValue();
      String uid = await prefsService.getUid();
      String collectionName = V2CollectionNames.saiwellRingTemperatureV2.name;
      
      if (uid.isEmpty) {
        return {'difference': 0.0, 'timestamp': null};
      }

      // Get data for today only
      final now = DateTime.now();
      final todayMidnight = DateTime(
        now.year,
        now.month,
        now.day,
        0,
        0,
        0,
      );

      final startTimestamp = todayMidnight.millisecondsSinceEpoch ~/ 1000;
      final endTimestamp = now.millisecondsSinceEpoch ~/ 1000;
      
      try {
        // Get the last 2 readings for today
        final QuerySnapshot snapshot = await firestore
            .collection(deviceDbName)
            .doc(uid)
            .collection(collectionName)
            .where(FieldPath.documentId,
                isGreaterThanOrEqualTo: startTimestamp.toString())
            .where(FieldPath.documentId,
                isLessThanOrEqualTo: endTimestamp.toString())
            .orderBy(FieldPath.documentId, descending: true)
            .limit(2)
            .get();
        
        if (snapshot.docs.isEmpty) {
          return {'difference': 0.0, 'timestamp': null};
        }
        
        // If there's only one reading, return 0 difference
        if (snapshot.docs.length == 1) {
          final data = snapshot.docs.first.data() as Map<String, dynamic>;
          final model = TempFirebaseModel.fromJson(data);
          final convertedTemp = _convertToFahrenheit(model.temperature ?? 0);
          
          return {
            'difference': 0.0,
            'timestamp': model.vitalCollectedTimestamp
          };
        }
        
        // Calculate difference between two readings
        final latestData = snapshot.docs[0].data() as Map<String, dynamic>;
        final previousData = snapshot.docs[1].data() as Map<String, dynamic>;
        
        final latestReading = TempFirebaseModel.fromJson(latestData);
        final previousReading = TempFirebaseModel.fromJson(previousData);
        
        final latestTemp = _convertToFahrenheit(latestReading.temperature ?? 0);
        final previousTemp = _convertToFahrenheit(previousReading.temperature ?? 0);
        
        return {
          'difference': latestTemp - previousTemp,
          'timestamp': latestReading.vitalCollectedTimestamp
        };
      } catch (e) {
        print('Error executing Firebase query for temperature difference: $e');
        return {'difference': 0.0, 'timestamp': null};
      }
    } catch (e) {
      print('Error in getTemperatureDifferenceForToday: $e');
      return {'difference': 0.0, 'timestamp': null};
    }
  }

  num _convertToFahrenheit(num celsius) {
    return num.parse(((celsius * 1.8) + 32).toStringAsFixed(2));
  }
}
