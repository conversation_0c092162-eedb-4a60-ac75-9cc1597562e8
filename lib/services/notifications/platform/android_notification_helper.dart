import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:SAiWELL/services/prefs_service.dart';

/// Android-specific notification helper for handling permissions
class AndroidNotificationHelper {
  static const MethodChannel _channel = MethodChannel('saiwell/android_notifications');
  static bool _shouldShowRetryOnResume = false;

  // Core permission checking methods
  static Future<bool> isBatteryOptimizationDisabled() async {
    if (!Platform.isAndroid) return true;
    try {
      return await _channel.invokeMethod('isBatteryOptimizationDisabled');
    } catch (e) {
      debugPrint('Error checking battery optimization: $e');
      return false;
    }
  }

  static Future<Map<String, bool>> checkAllRequirements() async {
    if (!Platform.isAndroid) {
      return {'notifications': true, 'batteryOptimization': true, 'exactAlarm': true};
    }

    // Check exact alarm permission for Android 12+
    bool exactAlarmGranted = true;
    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt >= 31) {
        exactAlarmGranted = await Permission.scheduleExactAlarm.isGranted;
      }
    } catch (e) {
      debugPrint('Error checking exact alarm permission: $e');
    }

    return {
      'notifications': await Permission.notification.isGranted,
      'batteryOptimization': await isBatteryOptimizationDisabled(),
      'exactAlarm': exactAlarmGranted,
    };
  }



  // Main setup method - shows unified dialog
  static Future<void> setupAndroidPermissions() async {
    if (!Platform.isAndroid) return;
    _showUnifiedPermissionDialog();
  }

  // Simple permission dialog - only shows what needs to be configured
  static Future<void> _showUnifiedPermissionDialog() async {
    final requirements = await checkAllRequirements();
    final allGranted = requirements.values.every((granted) => granted);

    // If all permissions are granted, don't show any dialog
    if (allGranted) return;

    // Show what needs to be done
    List<String> missingPermissions = [];
    if (!requirements['notifications']!) missingPermissions.add('• Allow notifications');
    if (!requirements['batteryOptimization']!) missingPermissions.add('• Set battery to "No restrictions"');
    if (!requirements['exactAlarm']!) missingPermissions.add('• Allow "Alarms & reminders"');

    Get.dialog(
      AlertDialog(
        title: const Text('Setup Required'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Please configure these in Android Settings:'),
            const SizedBox(height: 12),
            ...missingPermissions.map((item) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(item),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              _shouldShowRetryOnResume = true;
              await _openCorrectSettings(requirements);
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  // Open the most relevant settings page based on what's missing
  static Future<void> _openCorrectSettings(Map<String, bool> requirements) async {
    // Priority: Battery optimization > Exact alarms > General app settings
    if (!requirements['batteryOptimization']!) {
      // Try to open battery optimization settings directly
      try {
        await _channel.invokeMethod('requestDisableBatteryOptimization');
      } catch (e) {
        debugPrint('Error opening battery settings: $e');
        // Fallback to general app settings
        await openAppSettings();
      }
    } else {
      // For notifications or fallback, open general app settings
      await openAppSettings();
    }
  }

  // Simple retry dialog when returning from settings
  static void _showRetryDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Check Permissions'),
        content: const Text('Tap "Retry" to check if permissions are configured.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              _showUnifiedPermissionDialog();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }



  // Direct setup - no post-login dialog, just show unified dialog
  static Future<void> showPostLoginNotificationDialog() async {
    if (!Platform.isAndroid) return;
    _showUnifiedPermissionDialog();
  }

  // Method to call when app resumes (call this from your app lifecycle)
  static Future<void> onAppResumed() async {
    if (_shouldShowRetryOnResume) {
      _shouldShowRetryOnResume = false;
      // Small delay to ensure app is fully resumed
      await Future.delayed(const Duration(milliseconds: 500));
      _showRetryDialog();
    }
  }

  // Utility methods
  static Future<void> resetDialogPreference() async {
    final prefsService = PrefsService();
    await prefsService.setNotificationPermissionDialogShown(false);
  }

  // Simple permission status - just shows the unified dialog
  static Future<void> showPermissionStatus() async {
    if (!Platform.isAndroid) return;
    _showUnifiedPermissionDialog();
  }

}
