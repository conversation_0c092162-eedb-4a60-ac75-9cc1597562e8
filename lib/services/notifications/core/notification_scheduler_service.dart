import 'dart:io';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:timezone/timezone.dart' as tz;
import 'package:SAiWELL/services/firebase_remote_config_service.dart';

class NotificationSchedulerService {
  static final FlutterLocalNotificationsPlugin
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  // Notification channel for scheduled reminders
  static const String _channelId = 'scheduled_reminders';
  static const String _channelName = 'Scheduled Reminders';
  static const String _channelDescription = 'Regular health check reminders';

  /// Initialize the notification scheduler service
  static Future<void> initialize() async {
    try {
      // Initialize timezone data
      tz_data.initializeTimeZones();

      await _requestNotificationPermissions();
      await _createTestNotificationChannel();

      // Check and request exact alarm permission for Android 12+
      if (Platform.isAndroid) {
        final canScheduleExact = await canScheduleExactAlarms();


        if (!canScheduleExact) {
          final granted = await requestExactAlarmPermission();
          if (!granted) {
            debugPrint(
                'WARNING: Exact alarm permission denied. Scheduled notifications may not work reliably when app is killed.');
          }
        }
      }

    } catch (e) {
      debugPrint('Error initializing NotificationSchedulerService: $e');
    }
  }

  /// Request basic notification permissions (no battery optimization)
  static Future<void> _requestNotificationPermissions() async {
    if (Platform.isAndroid) {
      // Only request basic notification permission for Android 13+
      // Note: Battery optimization is handled separately by user choice
      // Using inexact alarms to avoid requiring exact alarm permissions
      try {
        final notificationStatus = await Permission.notification.request();
      } catch (e) {
        debugPrint('Error requesting notification permission: $e');
      }
    } else if (Platform.isIOS) {
      // Request notification permissions for iOS
      final bool? result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    }
  }

  /// Create notification channel for Android
  static Future<void> _createTestNotificationChannel() async {
    if (Platform.isAndroid) {
      // Create production notification channel
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        _channelId,
        _channelName,
        description: _channelDescription,
        importance: Importance
            .max, // Changed from high to max to match notification settings
        playSound: true,
        enableVibration: true,
      );

      // Create test notification channel
      const AndroidNotificationChannel testChannel = AndroidNotificationChannel(
        'test_channel',
        'Test Notifications',
        description: 'Test notifications to verify background delivery',
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
      );

      final androidPlugin = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      await androidPlugin?.createNotificationChannel(channel);
      await androidPlugin?.createNotificationChannel(testChannel);

      debugPrint('Notification channels created: $_channelId and test_channel');
    }
  }

  /// Schedule all hourly notifications (every hour from 8 AM to 8 PM)
  /// This method schedules notifications regardless of ring connection status
  /// Ring connection check should be done at the caller level (NotificationManager)
  static Future<void> scheduleRingNotifications() async {
    try {
      if (Platform.isAndroid) {
        final notificationsEnabled = await areNotificationsEnabled();
        final canScheduleExact = await canScheduleExactAlarms();
        if (!notificationsEnabled) {
          debugPrint(
              'ERROR: Notification permission not granted. Cannot schedule notifications.');
          return;
        }
        if (!canScheduleExact) {
          debugPrint(
              'WARNING: Exact alarm permission not granted. Notifications may not work when app is killed.');
        }
      }
      final schedules =
          FirebaseRemoteConfigService().getRingNotificationSchedules();
      int notificationId = 2000;
      for (final schedule in schedules) {
        await _scheduleNotification(
          id: notificationId++,
          hour: schedule['hour'] ?? 8,
          min: schedule['min'] ?? 0,
          sec: schedule['sec'] ?? 0,
          title: 'SAiWELL Health Reminder',
          body: schedule['content'] ?? 'Time for your ring check!',
          channelId: _channelId,
          channelName: _channelName,
          channelDescription: _channelDescription,
        );
      }
    } catch (e) {
      debugPrint('Error scheduling ring notifications: $e');
    }
  }

  /// Convert DateTime to TZDateTime (using local timezone)
  static tz.TZDateTime _convertToTZDateTime(DateTime dateTime) {
    // Use local timezone
    return tz.TZDateTime.from(dateTime, tz.local);
  }

  /// Cancel a specific notification by ID
  static Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
    } catch (e) {
      debugPrint('Error cancelling notification $id: $e');
    }
  }

  /// Get list of pending notifications
  static Future<List<PendingNotificationRequest>>
      getPendingNotifications() async {
    try {
      return await _flutterLocalNotificationsPlugin
          .pendingNotificationRequests();
    } catch (e) {
      debugPrint('Error getting pending notifications: $e');
      return [];
    }
  }

  /// Check if notifications are enabled
  static Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      return await Permission.notification.isGranted;
    } else if (Platform.isIOS) {
      final bool? result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      return result ?? false;
    }
    return false;
  }

  /// Check if exact alarm permission is granted (Android 12+)
  static Future<bool> canScheduleExactAlarms() async {
    if (!Platform.isAndroid) return true;

    try {
      // For Android 12+ (API 31+), check if exact alarm permission is granted
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt >= 31) {
        return await Permission.scheduleExactAlarm.isGranted;
      }
      return true; // For older Android versions, no permission needed
    } catch (e) {
      debugPrint('Error checking exact alarm permission: $e');
      return false;
    }
  }

  /// Request exact alarm permission if needed (Android 12+)
  static Future<bool> requestExactAlarmPermission() async {
    if (!Platform.isAndroid) return true;

    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt >= 31) {
        final status = await Permission.scheduleExactAlarm.request();
        return status.isGranted;
      }
      return true; // For older Android versions, no permission needed
    } catch (e) {
      debugPrint('Error requesting exact alarm permission: $e');
      return false;
    }
  }

  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  static Future<void> scheduleVoiceNotifications() async {
    try {
      final prefsService = PrefsService();
      final lastRecordingTimestamp =
          await prefsService.getLastVoiceRecordingTimestamp();
      if (lastRecordingTimestamp != null) {
        final lastRecordingDate =
            DateTime.fromMillisecondsSinceEpoch(lastRecordingTimestamp);
        if (_isSameDay(lastRecordingDate, DateTime.now())) {
          debugPrint(
              'Voice recording already done today, skipping notifications.');
          return;
        }
      }
      // Cancel all previous voice notifications
      for (int i = 1000; i < 1100; i++) {
        await cancelNotification(i);
      }
      final schedules =
          FirebaseRemoteConfigService().getVoiceNotificationSchedules();
      int notificationId = 1000;
      for (final schedule in schedules) {
        await _scheduleNotification(
          id: notificationId++,
          hour: schedule['hour'] ?? 12,
          min: schedule['min'] ?? 0,
          sec: schedule['sec'] ?? 0,
          title: 'Voice Recording Reminder',
          body: schedule['content'] ??
              "It's time to record your voice for health assessment.",
          channelId: 'voice_recording_reminders',
          channelName: 'Voice Recording Reminders',
          channelDescription: 'Hourly voice recording reminders',
        );
      }
    } catch (e) {
      debugPrint('Error scheduling voice notifications: $e');
    }
  }

  static Future<void> scheduleVialsNotifications() async {
    try {
      final prefsService = PrefsService();
      final lastNfcScanTimestamp =
          await prefsService.getLastNfcScanTimestamp();
      if (lastNfcScanTimestamp != null) {
        final lastNfcScanDate =
            DateTime.fromMillisecondsSinceEpoch(lastNfcScanTimestamp);
        if (_isSameDay(lastNfcScanDate, DateTime.now())) {
          debugPrint(
              'NFC scan already done today, skipping vials notifications.');
          return;
        }
      }
      // Cancel all previous vials notifications
      for (int i = 3000; i < 3100; i++) {
        await cancelNotification(i);
      }
      final schedules =
          FirebaseRemoteConfigService().getVialsNotificationSchedules();
      int notificationId = 3000;
      for (final schedule in schedules) {
        await _scheduleNotification(
          id: notificationId++,
          hour: schedule['hour'] ?? 10,
          min: schedule['min'] ?? 0,
          sec: schedule['sec'] ?? 0,
          title: 'Vials Reminder',
          body: schedule['content'] ??
              "It's time to scan your vials for health assessment.",
          channelId: 'vials_reminders',
          channelName: 'Vials Reminders',
          channelDescription: 'Daily vials scanning reminders',
        );
      }
    } catch (e) {
      debugPrint('Error scheduling vials notifications: $e');
    }
  }

  static Future<void> _scheduleNotification({
    required int id,
    required int hour,
    required int min,
    required int sec,
    required String title,
    required String body,
    required String channelId,
    required String channelName,
    required String channelDescription,
  }) async {
    final DateTime now = DateTime.now();
    DateTime scheduledDate =
        DateTime(now.year, now.month, now.day, hour, min, sec);
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: Importance.max,
      priority: Priority.max,
      playSound: true,
      enableVibration: true,
      icon: '@mipmap/launcher_icon',
      color: const Color(0xff6aaa64),
      colorized: true,
      autoCancel: true,
      ongoing: false,
      showWhen: true,
      when: DateTime.now().millisecondsSinceEpoch,
      usesChronometer: false,
      category: AndroidNotificationCategory.reminder,
      visibility: NotificationVisibility.public,
      timeoutAfter: null,
      actions: const <AndroidNotificationAction>[
        AndroidNotificationAction(
          'open_app',
          'Open SAiWELL',
          showsUserInterface: true,
        ),
      ],
    );
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );
    final tzDateTime = _convertToTZDateTime(scheduledDate);
    await _flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tzDateTime,
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
    debugPrint('✓ Scheduled notification: $title at $hour:$min:$sec (ID: $id)');
  }
}
