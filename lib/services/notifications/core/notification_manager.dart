import 'package:flutter/material.dart';
import 'package:SAiWELL/services/notifications/core/notification_scheduler_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:get/get.dart';
import 'package:SAiWELL/services/user_session_service.dart';

/// A simple manager class to handle notification scheduling operations
class NotificationManager {
  static bool _isInitialized = false;

  /// Initialize the notification manager
  static Future<void> initialize() async {
    if (!_isInitialized) {
      await NotificationSchedulerService.initialize();
      _isInitialized = true;
      debugPrint('NotificationManager initialized');
    }
  }

  /// Start hourly notifications (every hour from 8 AM to 8 PM)
  /// Only schedules notifications for users who have connected to a ring at least once
  /// Can be called from app startup or when user connects to ring for first time
  static Future<void> startDailyNotifications() async {
    try {
      final prefsService = PrefsService();
      String lastMac = await prefsService.getLastConnectedDeviceMac();

      if (lastMac != "") {
        await NotificationSchedulerService.scheduleRingNotifications();
        debugPrint('Hourly notifications scheduled for ring-connected user');
      } else {
        debugPrint('Hourly notifications not scheduled - user has not connected to ring yet');
      }
    } catch (e) {
      debugPrint('Error starting daily notifications: $e');
    }
  }

  /// Start hourly voice recording notifications (every hour from 12 PM to 8 PM)
  /// Only schedules notifications for users who are eligible (programsFromWebView contains -WELL or -AUDI)
  static Future<void> startVoiceRecordingNotifications() async {
    try {
      final userSession = Get.find<UserSessionService>();
      if (userSession.isUserEligibleForVoiceRecording()) {
        await NotificationSchedulerService.scheduleVoiceNotifications();
        debugPrint('Voice recording notifications scheduled for eligible user');
      } else {
        debugPrint('Voice recording notifications not scheduled - user not eligible');
      }
    } catch (e) {
      debugPrint('Error starting voice recording notifications: $e');
    }
  }

  /// Start vials notifications
  /// Only schedules notifications for users who are eligible (programsFromWebView contains SFOT-SLIT)
  static Future<void> startVialsNotifications() async {
    try {
      final userSession = Get.find<UserSessionService>();
      if (userSession.isUserEligibleForSlit()) {
        await NotificationSchedulerService.scheduleVialsNotifications();
        debugPrint('Vials notifications scheduled for eligible user');
      } else {
        debugPrint('Vials notifications not scheduled - user not eligible');
      }
    } catch (e) {
      debugPrint('Error starting vials notifications: $e');
    }
  }
}
