import 'dart:io';
import 'dart:ui';
import 'dart:convert';

import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:SAiWELL/modules/recording/pages/recording_instruction_screen.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/services/analytics/events.dart';
import 'package:SAiWELL/utils/stream_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

class FcmService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  static final FlutterLocalNotificationsPlugin
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  HomeController homeController = Get.find<HomeController>();
  PrefsService prefsService = PrefsService();

  final List<String> _channelIds = ['General', 'Reminders'];

  Future<void> initialise() async {
    await requestPermissionOnIos();
    await configureFCM();
    await configureLocalNotifications();
    await _createNotificationChannels();
    await getFcmToken();
    handleInitialMessage();

    if (await prefsService.getLastConnectedDeviceMac() != "") {
      await subscribeToTopic();
    }
  }

  getFirebaseMessaging() {
    return _firebaseMessaging;
  }

  Future<void> requestPermissionOnIos() async {
    await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  // New reusable method to process notification data
  void processNotificationData(Map<String, dynamic> notificationData, {String? notificationTitle}) {
    try {
      if (notificationData['deepLink'] != null) {
        String url = notificationData['deepLink'];
        debugPrint('Processing notification with deep link: $url');
        
        // Log event only if title is provided
        if (notificationTitle != null) {
          LogEvents.logNotificationClickedEvent(
              notificationTitle: notificationTitle);
        }
        
        // Navigate to deep link
        homeController.sendDeepLinkToWebView(url);
      } else if (notificationData['shouldGoToRecording'] != null) {
        debugPrint('Processing notification with recording flag');
        Get.toNamed(RecordingInstructionScreen.routeName);
      }
    } catch (e) {
      debugPrint('Error processing notification data: $e');
    }
  }
  
  Future<void> configureLocalNotifications() async {
    const initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/launcher_icon');
    const initializationSettingsIOS = DarwinInitializationSettings();
    const initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    // Set up notification click handler
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification click
        _handleNotificationClick(response);
      },
    );
  }

  void _handleNotificationClick(NotificationResponse response) {
    try {
      // Parse the payload if available
      if (response.payload != null) {
        Map<String, dynamic> notificationData = jsonDecode(response.payload!);
        processNotificationData(notificationData);
      }
    } catch (e) {
      debugPrint('Error handling notification click: $e');
    }
  }

  Future<void> configureFCM() async {
    // This is only for ios
    await _firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true, // Required to display a heads up notification
      badge: true,
      sound: true,
    );

    FirebaseMessaging.onMessage.listenSafely((remoteMessage) async {
      debugPrint('**onMessages** : ${remoteMessage.data}');
      // On iOS, Firebase already displays the notification automatically
      // On Android, we need to show it manually via handleMessage
      if (!Platform.isIOS) {
        await handleMessage(remoteMessage);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listenSafely((remoteMessage) async {
      print('**onMessage opened** : ${remoteMessage.data}');
      
      if (remoteMessage.data.isNotEmpty) {
        processNotificationData(
          Map<String, dynamic>.from(remoteMessage.data),
          notificationTitle: remoteMessage.notification?.title
        );
      }
    });
  }

  Future<void> handleInitialMessage() async {
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      print("[handleInitialMessage]------NOTIFICATION clicked");
      
      if (initialMessage.data.isNotEmpty) {
        // Process with a delay to ensure app is properly initialized
        Future.delayed(const Duration(seconds: 5), () {
          processNotificationData(
            Map<String, dynamic>.from(initialMessage.data),
            notificationTitle: initialMessage.notification?.title
          );
        });
      }
    }
  }

  Future<String?> getFcmToken() async {
    String? token;
    try {
      token = await _firebaseMessaging.getToken();
      print("FCM token: $token");
      fcmToken = token;
    } catch (e) {
      debugPrint('Error grabbing fcm token: ${e.toString()}');
    }
    return token;
  }

  static Future<void> handleMessage(RemoteMessage message) async {
    ///
    ///NOTE: Sending a push message with a notification payload while the app is in the background
    ///will automatically show the notification in the system tray. This behaviour should be handled by the
    ///sender and not the client.
    ///https://stackoverflow.com/questions/37966544/how-to-disable-showing-notification-when-it-it-comes-to-the-system-tray
    ///
    Map<String, dynamic> parsedMessage = {};

    //Extract notification data
    if (message.data.isNotEmpty) {
      // Copy all data fields to preserve deepLink, shouldGoToRecording, etc.
      parsedMessage = Map<String, dynamic>.from(message.data);
      // Add title and body from notification
      parsedMessage['title'] = message.notification?.title ?? '';
      parsedMessage['body'] = message.notification?.body ?? '';
    } else if (message.notification != null) {
      parsedMessage = {
        'title': message.notification!.title ?? '',
        'body': message.notification!.body ?? '',
      };
    }

    await displayLocalNotification(parsedMessage, messageId: message.hashCode);
  }

  static int get puzzleNumber {
    final past = DateTime(2021, 6, 19);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final seed = today.difference(past);
    return seed.inDays;
  }

  static Future<void> displayLocalNotification(Map<dynamic, dynamic> message,
      {int messageId = 0}) async {
    var vibrationPattern = Int64List(2);
    vibrationPattern[0] = 0;
    vibrationPattern[1] = 500;

    const notificationChannelId = 'General';
    var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      notificationChannelId,
      notificationChannelId,
      channelDescription: notificationChannelId,
      playSound: true,
      enableVibration: true,
      vibrationPattern: vibrationPattern,
      importance: Importance.low,
      priority: Priority.low,
      visibility: NotificationVisibility.public,
      color: const Color(0xff6aaa64),
      colorized: true,
    );

    const iOSPlatformChannelSpecifics = DarwinNotificationDetails();

    var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Convert the full message to JSON string to pass as payload
    String payload = jsonEncode(message);
    
    await _flutterLocalNotificationsPlugin.show(
      messageId,
      message['title'],
      message['body'],
      platformChannelSpecifics,
      payload: payload,  // Include payload for click handling
    );
  }

  Future<void> _createNotificationChannels() async {
    for (var channelId in _channelIds) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(
              AndroidNotificationChannel(channelId, channelId));
    }
  }

  subscribeToTopic() async {
    try {
      String topic = "RING_CONNECT";
      await FirebaseMessaging.instance.subscribeToTopic(topic);
      print('Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic: $e');
    }
  }
}
