import 'package:location/location.dart';
Location location = Location();
Future<LocationData> determineLocation() async {
  

  bool serviceEnabled;
  PermissionStatus permissionGranted;

  serviceEnabled = await location.serviceEnabled();
  if (!serviceEnabled) {
    serviceEnabled = await location.requestService();
    if (!serviceEnabled) {
      String msg = 'Location services are disabled.';
      // CustomToast.error(msg);
      return Future.error(msg);
    }
  }

  permissionGranted = await location.hasPermission();
  if (permissionGranted == PermissionStatus.denied) {
    permissionGranted = await location.requestPermission();
    if (permissionGranted != PermissionStatus.granted) {
      String msg = 'Location permissions are denied';
      // CustomToast.error(msg);
      return Future.error(msg);
    }
  } else if (permissionGranted == PermissionStatus.deniedForever) {
    String msg =
        'Location permissions are permanently denied, we cannot request permissions.';
    // CustomToast.error(msg);
    return Future.error(msg);
  }
  return await location.getLocation();
}

Stream<LocationData> subscribeForLocationChange() {
  location.changeSettings(distanceFilter: 500);
  return location.onLocationChanged;
}