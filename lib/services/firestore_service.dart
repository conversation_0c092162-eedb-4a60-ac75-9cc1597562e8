import 'package:SAiWELL/common_controllers/global_controller.dart';
import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/models/health_datatype.dart';
import 'package:SAiWELL/models/hrv_reading_model_v2.dart';
import 'package:SAiWELL/models/oxygen_reading_model_v2.dart';
import 'package:SAiWELL/models/sleep_reading_model_v2.dart';
import 'package:SAiWELL/models/steps_reading_model_v2.dart';
import 'package:SAiWELL/utils/common_methods.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'dart:io' show Platform;
import '../models/temperature_reading_model_v2.dart';

enum V2CollectionNames {
  saiwellRingTemperatureV2("saiwellRingTemperatureV2"),
  saiwellRingBloodOxygenV2("saiwellRingBloodOxygenV2"),
  saiwellRingHrvV2("saiwellRingHrvV2"),
  saiwellRingSleepV2("saiwellRingSleepV2"),
  saiwellRingStepV2("saiwellRingStepV2");

  final String name;

  const V2CollectionNames(this.name);
}

class FirestoreService {
  FirebaseFirestore firestore = FirebaseFirestore.instanceFor(
      app: Firebase.app(), databaseId: fbDatabaseId);

  String healthData =
      Platform.isAndroid ? "GoogleHealthData" : "AppleHealthData";

  String testHealthData1 =
      Platform.isAndroid ? "testgoogledata1" : "testappledata1";

  String testHealthData =
      Platform.isAndroid ? "testgoogledata" : "testappledata";

  String v2CollectionName = "DeviceData";

  Future<void> storeFcmToken(String uid, String fcmToken) async {
    try {
      DocumentReference userDocRef =
          firestore.collection("FCMDetails").doc(uid);

      final docSnapshot = await userDocRef.get();
      final now = Timestamp.now();

      if (docSnapshot.exists) {
        await userDocRef.set({
          'fcmToken': fcmToken,
          'lastUpdated': now,
        }, SetOptions(merge: true));
      } else {
        await userDocRef.set({
          'created': now,
          'lastUpdated': now,
          'fcmToken': fcmToken,
        });
      }
    } catch (e) {
      // Keep error log for FCM token storage
      debugPrint(
          "=== Error occurred while saving FCM Token for user: $uid ===");
      debugPrint(e.toString());
    }
  }

  // Future<void> storeTestAmbeeApiCallTime(String uid) async {
  //   try {
  //     DocumentReference userDocRef =
  //         firestore.collection("ambeeDataLastRecorded").doc(uid);
  //
  //     final docSnapshot = await userDocRef.get();
  //     final now = Timestamp.now();
  //
  //     if (docSnapshot.exists) {
  //       await userDocRef.set({
  //         'calledAt': now,
  //         'lastUpdated': now,
  //       }, SetOptions(merge: true));
  //     } else {
  //       await userDocRef.set({
  //         'created': now,
  //         'lastUpdated': now,
  //         'calledAt': now,
  //       });
  //     }
  //
  //     debugPrint(
  //         "=== [storeTestAmbeeApiCallTime] data saved successfully for user: $uid ===");
  //   } catch (e) {
  //     print(e.toString());
  //   }
  // }

  Future<void> storeHealthData(String uid, String setterCollectionName,
      Map<String, dynamic> data, WriteBatch batch) async {
    try {
      DocumentReference userDocRef = firestore
          .collection("flutterTestHealthData")
          .doc(uid)
          .collection(setterCollectionName)
          .doc('${data['startTimestamp']}');

      final docSnapshot = await userDocRef.get();
      final now = Timestamp.now();

      if (!docSnapshot.exists) {
        batch.set(userDocRef, {
          ...data,
          'created': now,
          'lastUpdated': now,
        });
      }

    } catch (e) {
      // Keep error log for health data storage
      debugPrint('Error storing health data: $e');
    }
  }

  Future<bool> storeHealthDataWithDevicesAndSources(
    String uid,
    String setterCollectionName,
    Map<String, dynamic> data,
    WriteBatch batch,
    bool updateTimestamp,
  ) async {
    try {
      DocumentReference userDocRef = firestore
          .collection(healthData)
          .doc(uid)
          .collection(setterCollectionName)
          .doc('${data['startTimestamp']}');

      final docSnapshot = await userDocRef.get();
      final now = Timestamp.now();
      if (!docSnapshot.exists) {
        var dataToStore = {
          ...data,
          'created': now,
        };
        // if (updateTimestamp) {
        //   dataToStore['${setterCollectionName}_LastUpdated'] = now;
        // }

        batch.set(userDocRef, dataToStore);
        return true;
      }
      return false;
    } catch (e) {
      // Keep error log for health data check
      debugPrint('Error checking health data: $e');
      return false;
    }
  }

  Future<void> storeHealthDataWithDevicesAndSourcesInfo(
    String uid,
    Map<String, dynamic> data,
    WriteBatch batch,
    bool updateTimestamp,
    Set<String> dataTypes, // New parameter
  ) async {
    try {
      DocumentReference userDocRef = firestore.collection(healthData).doc(uid);
      final docSnapshot = await userDocRef.get();
      final now = Timestamp.now();
      var dataToStore = {...data};
      for (String typeName in dataTypes) {
        dataToStore['${typeName}_LastUpdated'] = now;
      }

      if (updateTimestamp) {
        dataToStore['lastUpdated'] = now;
      }

      if (docSnapshot.exists) {
        batch.set(userDocRef, dataToStore, SetOptions(merge: true));
      } else {
        dataToStore['created'] = now;
        batch.set(userDocRef, dataToStore);
      }
    } catch (e) {
      // Keep error log for batch storage
      debugPrint('Error in batch storage: $e');
    }
  }

  void storeCombinedHealthData1(String uid, String collection,
      Map<String, dynamic> data, WriteBatch batch, int seconds) {
    try {
      DocumentReference userDocRef = firestore
          .collection(testHealthData1)
          .doc(uid)
          .collection(collection)
          .doc('$seconds');

      userDocRef.get().then((docSnapshot) {
        final now = Timestamp.now();

        if (!docSnapshot.exists) {
          batch.set(userDocRef, {
            ...data,
            'created': now,
            'lastUpdated': now,
          });
        }
      });

    } catch (e) {
      // Keep error log for combined health data storage
      debugPrint('Error storing combined health data: $e');
    }
  }

  Future<Timestamp> getLastSyncedTimestampForCombinedData(String uid) async {
    Timestamp timestamp = Timestamp.now();
    try {
      DocumentReference userDocRef =
          firestore.collection(testHealthData).doc(uid);

      DocumentSnapshot doc = await userDocRef.get();
      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        if (data['lastSyncedOn'] != null) {
          timestamp = data['lastSyncedOn'];
        } else {
          timestamp = CommonMethods.getTodaysMidnightTimeStamp();
        }
      } else {
        timestamp = CommonMethods.getTodaysMidnightTimeStamp();
      }
    } catch (e) {
      // Keep error log for timestamp retrieval
      debugPrint('Error getting last synced timestamp: $e');
    }
    return timestamp;
  }

  Future<Timestamp> updateLastSyncedTimestampForCombinedHealthData1(
      String uid, Timestamp timestamp) async {
    try {
      DocumentReference userDocRef =
          firestore.collection(testHealthData1).doc(uid);

      final docSnapshot = await userDocRef.get();

      if (docSnapshot.exists) {
        await userDocRef.set({
          'lastSyncedOn': timestamp,
          'lastUpdated': Timestamp.now(),
        }, SetOptions(merge: true));
      } else {
        await userDocRef.set({
          'created': Timestamp.now(),
          'lastUpdated': Timestamp.now(),
          'lastSyncedOn': timestamp,
        });
      }

    } catch (e) {
      // Keep error log for timestamp update
      debugPrint('Error updating last synced timestamp: $e');
    }
    return timestamp;
  }

  Future<void> addDeviceMetaDataV2({
    required Map<String, Timestamp> moreData,
  }) async {
    try {
      String uid = await prefsService.getUid();
      GlobalController globalController = Get.find<GlobalController>();

      if (uid.isEmpty) {
        return;
      }

      Timestamp currentTimeStamp = Timestamp.fromMillisecondsSinceEpoch(
          DateTime.now().millisecondsSinceEpoch);

      final DocumentReference parentDocRef =
          firestore.collection('DeviceDataMeta').doc(uid);

      final DocumentReference ringDocRef =
          parentDocRef.collection("SAiWELL").doc("Ring");

      DocumentSnapshot docSnapshot = await ringDocRef.get();

      Map<String, dynamic> deviceData = {
        'data': {
          'batteryPower': globalController.ringBatteryPercentage.value,
          'deviceID': globalController.deviceVersion.value,
          'deviceModel': 'V2',
          'macAddress': globalController.macId.value,
        },
        'lastUpdated': currentTimeStamp,
      };

      // Add createdAt if document doesn't exist
      if (!docSnapshot.exists) {
        deviceData['createdAt'] = currentTimeStamp;
      }

      // Merge existing data if document exists
      if (docSnapshot.exists) {
        try {
          Map<String, dynamic> existingData =
              docSnapshot.data() as Map<String, dynamic>;
          if (existingData.containsKey('data')) {
            var dataField = existingData['data'];
            // Check if data field is a Map before casting
            if (dataField is Map) {
              Map<String, dynamic> existingDataMap =
                  Map<String, dynamic>.from(dataField);
              deviceData['data'] = {...existingDataMap, ...deviceData['data']};
            } else if (dataField is String) {
              // Keep warning log for data field type
              debugPrint('Warning: data field is String instead of Map');
            }
          }
        } catch (e) {
          // Keep error log for document data processing
          debugPrint('Error processing document data: $e');
        }
      }

      // Add data from moreData safely
      try {
        moreData.forEach((key, value) {
          deviceData[key] = value;
        });
      } catch (e) {
        // Keep error log for additional data processing
        debugPrint('Error processing additional data: $e');
      }

      try {
        WriteBatch batch = firestore.batch();

        batch.set(ringDocRef, deviceData, SetOptions(merge: true));
        batch.set(parentDocRef, {'saiwellRingLastUpdated': currentTimeStamp},
            SetOptions(merge: true));

        await batch.commit();
      } catch (e) {
        // Keep error log for batch commit
        debugPrint('Error committing batch: $e');
      }
    } catch (e) {
      // Keep error log for device data addition
      debugPrint('Error adding device data: $e');
    }
  }

  Future<void> uploadHealthDataV2(
      {required String type, required String data}) async {
    try {
      // Get current user ID
      String uid = await prefsService.getUid();
      if (uid == "") {
        return;
      }
      // Get current timestamp
      String timestamp = DateTime.now().toIso8601String();
      // Upload sleep data
      await firestore
          .collection('DataFromV2')
          .doc(type)
          .collection(uid)
          .doc(timestamp)
          .set({'data': data, 'timestamp': timestamp});

    } catch (e) {
      // Keep error log for health data upload
      debugPrint('Error uploading healthDataV2: $e');
    }
  }

  /// Write vial administration data to Firebase
  /// Path: /DeviceData/uid/VialA or VialB/timestamp
  /// Document contains: { lastUpdated: timestamp }
  /// Also updates main uid document with vialALastRecorded or vialBLastRecorded
  /// Also writes to /DeviceDataMeta/uid/SFOTO/SLIT with vial tracking
  Future<void> writeVialAdministrationData({
    required String uid,
    required String vialCollection,
    required int timestamp,
  }) async {
    try {
      Timestamp firestoreTimestamp = Timestamp.fromMillisecondsSinceEpoch(timestamp);

      // Use a batch to write all documents atomically
      WriteBatch batch = firestore.batch();

      // 1. Write to the vial subcollection document
      DocumentReference vialDocRef = firestore
          .collection('DeviceData')
          .doc(uid)
          .collection(vialCollection)
          .doc(timestamp.toString());

      Map<String, dynamic> vialData = {
        'lastUpdated': firestoreTimestamp,
      };

      batch.set(vialDocRef, vialData);

      // 2. Update the main uid document with last recorded timestamp
      DocumentReference mainDocRef = firestore
          .collection('DeviceData')
          .doc(uid);

      // Determine the field name based on vial collection
      String lastRecordedField = vialCollection == 'VialA'
          ? 'vialALastRecorded'
          : 'vialBLastRecorded';

      Map<String, dynamic> mainDocData = {
        lastRecordedField: firestoreTimestamp,
      };

      batch.set(mainDocRef, mainDocData, SetOptions(merge: true));

      // 3. Write to DeviceDataMeta collection
      DocumentReference metaDocRef = firestore
          .collection('DeviceDataMeta')
          .doc(uid)
          .collection('SFOTO')
          .doc('SLIT');

      // Check if the meta document exists to set created timestamp
      DocumentSnapshot metaSnapshot = await metaDocRef.get();

      Map<String, dynamic> metaData = {
        lastRecordedField: firestoreTimestamp,
      };

      // Add created timestamp if document doesn't exist
      if (!metaSnapshot.exists) {
        metaData['created'] = firestoreTimestamp;
      }

      batch.set(metaDocRef, metaData, SetOptions(merge: true));

      // Commit the batch
      await batch.commit();
    } catch (e) {
      // Keep error log for vial administration data
      debugPrint("Error writing vial administration data to Firebase: $e");
      rethrow;
    }
  }

  Future<bool> uploadBatchDataV2({
    required dynamic dataList,
    required String uid,
    required V2CollectionNames collectionName,
  }) async {
    try {
      WriteBatch batch = firestore.batch();
      List<Future<DocumentSnapshot>> existenceChecks = [];
      List<DocWriteOperation> pendingWrites = [];
      int batchOperationsCount = 0;

      final parentDocRef = firestore.collection(v2CollectionName).doc(uid);
      Timestamp currentTimeStamp = Timestamp.fromMillisecondsSinceEpoch(
          DateTime.now().millisecondsSinceEpoch);

      void processReadings<T>({
        required List<T> readings,
        required int Function(T) getTimestamp,
        required Map<String, dynamic> Function(T, int timestamp) getData,
      }) {
        for (var reading in readings) {
          final timestamp = getTimestamp(reading);
          if (timestamp.toString().length <= 9) continue;

          final docRef = parentDocRef
              .collection(collectionName.name)
              .doc(timestamp.toString());

          existenceChecks.add(docRef.get());
          pendingWrites.add(
            DocWriteOperation(
              docRef: docRef,
              data: {
                ...getData(reading, timestamp),
                'vitalCollectedTimestamp':
                    Timestamp.fromMillisecondsSinceEpoch(timestamp * 1000),
              },
            ),
          );
        }
      }

      switch (collectionName) {
        case V2CollectionNames.saiwellRingTemperatureV2:
          processReadings<TemperatureReadingModelV2>(
            readings: dataList as List<TemperatureReadingModelV2>,
            getTimestamp: (r) => r.date.millisecondsSinceEpoch ~/ 1000,
            getData: (r, _) => {'temperature': r.temperature},
          );
          break;

        case V2CollectionNames.saiwellRingBloodOxygenV2:
          processReadings<BloodOxygenReadingModelV2>(
            readings: dataList as List<BloodOxygenReadingModelV2>,
            getTimestamp: (r) => r.date.millisecondsSinceEpoch ~/ 1000,
            getData: (r, _) => {'bloodOxygen': r.automaticSpo2Data},
          );
          break;

        case V2CollectionNames.saiwellRingHrvV2:
          processReadings<HRVReadingModelV2>(
            readings: dataList as List<HRVReadingModelV2>,
            getTimestamp: (r) => r.date.millisecondsSinceEpoch ~/ 1000,
            getData: (r, _) => {
              'diastolicBloodPressure': r.diastolicBP,
              'heartRate': r.heartRate,
              'hrv': r.hrv,
              'stress': r.stress,
              'systolicBloodPressure': r.systolicBP,
            },
          );
          break;

        case V2CollectionNames.saiwellRingSleepV2:
          processReadings<SleepReadingModelV2>(
            readings: dataList as List<SleepReadingModelV2>,
            getTimestamp: (r) => r.startTime.millisecondsSinceEpoch ~/ 1000,
            getData: (r, timestamp) {
              final endTimestamp = r.endTime.millisecondsSinceEpoch ~/ 1000;
              return {
                'deepSleepMinutes': r.deepSleepCount,
                'awakeSleepMinutes': r.awakeCount,
                'otherSleepMinutes': r.otherSleepCount,
                'remSleepMinutes': r.remSleepCount,
                'lightSleepMinutes': r.lightSleepCount,
                'totalSleepTime': r.totalSleepTime,
                'startTimeStamp': timestamp,
                'endTimeStamp': endTimestamp,
                'sleepQualityArray': r.sleepQuality,
                'sleepDetailDatas':
                    r.sleepQualityGroups.map((g) => g.toJson()).toList(),
              };
            },
          );
          break;

        case V2CollectionNames.saiwellRingStepV2:
          processReadings<StepsReadingModelV2>(
            readings: dataList as List<StepsReadingModelV2>,
            getTimestamp: (r) => r.date.millisecondsSinceEpoch ~/ 1000,
            getData: (r, _) => {
              'step': r.step,
              'calories': r.calories,
              'distance': r.distance * 1000.0,
            },
          );
          break;
      }

      final existingDocs = await Future.wait(existenceChecks);
      for (int i = 0; i < existingDocs.length; i++) {
        if (!existingDocs[i].exists) {
          batch.set(pendingWrites[i].docRef, pendingWrites[i].data);
          batch.set(parentDocRef, {'saiwellRingLastUpdated': currentTimeStamp},
              SetOptions(merge: true));
          batchOperationsCount++;
        }
      }

      return batchOperationsCount > 0
          ? await batch.commit().then((_) => true)
          : false;
    } catch (e) {
      // Keep error log for batch data upload
      debugPrint('Error uploading batch data v2: $e');
      return false;
    }
  }
}

class DocWriteOperation {
  final DocumentReference docRef;
  final Map<String, dynamic> data;

  DocWriteOperation({
    required this.docRef,
    required this.data,
  });
}
