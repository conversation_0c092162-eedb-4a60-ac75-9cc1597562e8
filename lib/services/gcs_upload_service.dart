import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:SAiWELL/constants/constant.dart';
import 'package:flutter/foundation.dart'
    show TargetPlatform, defaultTargetPlatform, debugPrint;

class GcsUploadService {
  static Future<String> getPlatformString() async {
    if (Platform.isIOS) return 'ios';
    if (Platform.isAndroid) return 'android';
    return 'unknown';
  }

  static Future<String> getAppVersion() async {
    final info = await PackageInfo.fromPlatform();
    return info.version;
  }

  static String getBucketName() {
    return isProdEnv ? 'saiwell-gtplus' : 'saiwell-gtplus-staging';
  }

  static String getVoiceBucketName() {
    return isProdEnv ? 'speech_data_bucket' : 'hydra_test_sh';
  }

  static String getVoiceBucketNameForGtPlus({bool isFromGtPlus = false}) {
    if (isFromGtPlus) {
      // If from GT-Plus, use the main bucket
      return getBucketName();
    } else {
      // If from other places, use the dedicated voice bucket
      return getVoiceBucketName();
    }
  }

  static Future<String> buildGcsPath({
    required String uid,
    required String type,
    required int timestamp,
    String? extension,
    bool isFromGtPlus = false,
  }) async {
    // Check if type already has an extension and avoid double extension
    bool typeHasExtension = type.contains('.') &&
        (type.endsWith('.csv') || type.endsWith('.wav') || type.endsWith('.jpg') ||
         type.endsWith('.jpeg') || type.endsWith('.png'));

    final ext = (extension != null && !typeHasExtension) ? '.$extension' : '';
    String fileName;
    if (type == 'speech') {
      if (isFromGtPlus) {
        // For GT-Plus recordings: timestamp_(platform)android/ios_app_version_version_speech.wav
        final platform = await getPlatformString();
        final version = await getAppVersion();
        fileName = '${timestamp}_${platform}_${version}_speech$ext';
      } else {
        // For other recordings: uid_timestamp.wav
        fileName = '${uid}_$timestamp$ext';
      }
    } else {
      final platform = await getPlatformString();
      final version = await getAppVersion();
      fileName = '${timestamp}_${platform}_${version}_$type$ext';
    }
    final folder = uid;
    final fullPath = '$folder/$fileName';
    debugPrint('[GCS] buildGcsPath: folder=$folder, fileName=$fileName, fullPath=$fullPath, isFromGtPlus=$isFromGtPlus, typeHasExtension=$typeHasExtension');
    return fullPath;
  }

  static Future<bool> uploadToGcs({
    required List<int> bytes,
    required String uid,
    required String type,
    required int timestamp,
    required String contentType,
    String? extension,
    required String credentialsAssetPath,
    String? bucketName,
    bool isFromGtPlus = false,
  }) async {
    try {
      final gcsPath = await buildGcsPath(
        uid: uid,
        type: type,
        timestamp: timestamp,
        extension: extension,
        isFromGtPlus: isFromGtPlus,
      );
      final resolvedBucketName = bucketName ?? getBucketName();
      debugPrint(
          '[GCS] Uploading to bucket: $resolvedBucketName, path: $gcsPath, contentType: $contentType, bytes: ${bytes.length}');
      final credentialsJson = await rootBundle.loadString(credentialsAssetPath);
      final credentials =
          ServiceAccountCredentials.fromJson(jsonDecode(credentialsJson));
      final httpClient = await clientViaServiceAccount(
        credentials,
        [
          StorageApi.devstorageReadWriteScope,
          StorageApi.devstorageFullControlScope
        ],
      );
      final storage = StorageApi(httpClient);
      final media = Media(
        Stream.value(bytes),
        bytes.length,
        contentType: contentType,
      );
      final response = await storage.objects.insert(
        Object(name: gcsPath),
        resolvedBucketName,
        uploadMedia: media,
      );
      debugPrint(
          '[GCS] Upload response: id=${response.id}, mediaLink=${response.mediaLink}');
      return response.id != null;
    } catch (e) {
      debugPrint('[GCS] GCS upload error: $e');
      return false;
    }
  }
}
