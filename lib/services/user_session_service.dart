import 'package:get/get.dart';

class UserSessionService extends GetxService {
  List<String>? programsFromWebView;

  bool isUserEligibleForSlit() {
    return programsFromWebView != null && programsFromWebView!.contains('SFOT-SLIT');
  }

  bool isUserEligibleForVoiceRecording() {
    if (programsFromWebView == null) return false;
    return programsFromWebView!.any((p) => p.contains('-WELL') || p.contains('-AUDI'));
  }
} 