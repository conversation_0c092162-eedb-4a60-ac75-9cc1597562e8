import 'package:get/get.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';

class UserSessionService extends GetxService {
  List<String>? programsFromWebView;

  bool isUserEligibleForSlit() {
    return programsFromWebView != null && programsFromWebView!.contains('SFOT-SLIT');
  }

  bool isUserEligibleForVoiceRecording() {
    if (programsFromWebView == null) return false;

    // Get the list of programs that should NOT show voice notifications from remote config
    final remoteConfigService = FirebaseRemoteConfigService();
    final excludedPrograms = remoteConfigService.getVoiceNotificationExcludedPrograms();

    // Check if any of the user's programs are in the exclusion list
    for (String userProgram in programsFromWebView!) {
      for (String excludedProgram in excludedPrograms) {
        if (userProgram.contains(excludedProgram)) {
          return false; // User has an excluded program, so no voice notifications
        }
      }
    }

    // If no excluded programs found, user is eligible for voice notifications
    return true;
  }
} 