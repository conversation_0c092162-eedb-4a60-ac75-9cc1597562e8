import 'dart:convert';
import 'dart:io';

import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/models/hr_monitoring_remote_config_model.dart';
import 'package:SAiWELL/models/navigation_model.dart';
import 'package:SAiWELL/modules/update_available/package_info.dart';
import 'package:SAiWELL/utils/const/app_const.dart';
import 'package:SAiWELL/utils/stream_utils.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'dart:io' show Platform;

class FirebaseRemoteConfigService {
  static final FirebaseRemoteConfigService _singleton =
      FirebaseRemoteConfigService._internal();
  final packageInfo = PackageInfoSetup();

  factory FirebaseRemoteConfigService() {
    return _singleton;
  }
  final remoteConfig = FirebaseRemoteConfig.instance;
  FirebaseRemoteConfigService._internal();

  String env = isProdEnv ? "_PRODUCTION" : "_STAGING";
  String getFirebaseRemoteConfigWithEnv(String key) {
    return key + env;
  }

  Future<void> initialize() async {
    final remoteConfig = FirebaseRemoteConfig.instance;

    // Set configuration
    await remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(seconds: 1),
      ),
    );

    createMinVersionKey();

    await remoteConfig.setDefaults({
      FirebaseRemoteConfigKey.minVersion: '0.0.1',
      getFirebaseRemoteConfigWithEnv(
        Platform.isAndroid
            ? FirebaseRemoteConfigKey.androidHealthDataTypes
            : FirebaseRemoteConfigKey.appleHealthDataTypes,
      ): Platform.isAndroid
          ? '{"androidHealthDataTypes":[]}'
          : '{"appleHealthDataTypes":[]}',
      getFirebaseRemoteConfigWithEnv(
          FirebaseRemoteConfigKey.beforeNoonAmbeeApiCallCount): 0,
      getFirebaseRemoteConfigWithEnv(
          FirebaseRemoteConfigKey.afterNoonAmbeeApiCallCount): 0,
      FirebaseRemoteConfigKey.voiceContentConfig: '{"content": ""}',
      FirebaseRemoteConfigKey.voiceInstructionsConfig: '{"instructions": []}',
      FirebaseRemoteConfigKey.ringNotificationSchedule:
          '[{"hour":8,"min":0,"sec":0,"content":"Time to check your ring!"}]',
      FirebaseRemoteConfigKey.voiceNotificationSchedule:
          '[{"hour":12,"min":0,"sec":0,"content":"Time to record your voice!"}]',
      FirebaseRemoteConfigKey.vialsNotificationSchedule:
          '[{"hour":10,"min":0,"sec":0,"content":"Time to scan your vials!"}]',
      FirebaseRemoteConfigKey.navigationConfig:
          '{"baseUrl": "","navigation": []}',
      FirebaseRemoteConfigKey.baseUrlDevConfig: '',
      FirebaseRemoteConfigKey.baseUrlProdConfig: '',
      FirebaseRemoteConfigKey.recordingDurationSeconds: 60,
      FirebaseRemoteConfigKey.recordingDurationSecondsStaging: 60,
      FirebaseRemoteConfigKey.androidMinVersion: '0.0.1',
    });

    try {
      // Attempt to fetch and activate config, but don't fail if offline
      bool updated = await remoteConfig.fetchAndActivate().timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          debugPrint("Remote config fetch timed out - using cached values");
          return false;
        },
      );
      debugPrint("Remote config updated: $updated");
    } catch (e) {
      // Catch network errors and continue with default/cached values
      debugPrint("Failed to fetch remote config: $e - using cached values");
    }

    // Wait a moment to ensure activation is complete if it succeeded
    await Future.delayed(const Duration(milliseconds: 300));

    await fetchAndSetBaseUrl();

    // Setup listener for future updates
    remoteConfig.onConfigUpdated.listenSafely((event) async {
      try {
        await remoteConfig.activate();
        await fetchAndSetBaseUrl();
      } catch (e) {
        debugPrint("Error handling remote config update: $e");
      }
    });
  }

  void createMinVersionKey() {
    String packageKey = "";

    if (Platform.isAndroid) {
      packageKey = packageInfo.packageName;
    } else if (Platform.isIOS) {
      packageKey = iosAppId;
    }

    packageKey.split(".").forEach((element) {
      FirebaseRemoteConfigKey.minVersion =
          "${getFirebaseRemoteConfigWithEnv(FirebaseRemoteConfigKey.minVersion)}_$element";
    });

    debugPrint(
        "------min version remote config setting key : ${FirebaseRemoteConfigKey.minVersion}");
  }

  String getMinRequiredVersion() =>
      remoteConfig.getString(FirebaseRemoteConfigKey.minVersion);

  String getAndroidMinRequiredVersion() {
    String androidVersion = remoteConfig.getString(
        getFirebaseRemoteConfigWithEnv(
            FirebaseRemoteConfigKey.androidMinVersion));
    return androidVersion;
  }

  List<dynamic> getHealthDataTypes() {
    String data = remoteConfig.getString(getFirebaseRemoteConfigWithEnv(
      Platform.isAndroid
          ? FirebaseRemoteConfigKey.androidHealthDataTypes
          : FirebaseRemoteConfigKey.appleHealthDataTypes,
    ));
    print("--REMOTE DATA----   $data");
    Map dataObject = jsonDecode(data);
    return Platform.isAndroid
        ? dataObject['androidHealthDataTypes']
        : dataObject['appleHealthDataTypes'] ?? [];
  }

  int getTodaysAfterNoonAmbeeApiCallCount() => remoteConfig.getInt(
        getFirebaseRemoteConfigWithEnv(
          FirebaseRemoteConfigKey.afterNoonAmbeeApiCallCount,
        ),
      );

  int getTodaysBeforeNoonAmbeeApiCallCount() => remoteConfig.getInt(
        getFirebaseRemoteConfigWithEnv(
          FirebaseRemoteConfigKey.beforeNoonAmbeeApiCallCount,
        ),
      );

  String getVoiceContentString() {
    String jsonString = remoteConfig.getString(
      FirebaseRemoteConfigKey.voiceContentConfig,
    );
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return jsonMap['content'] ?? '';
  }

  List<dynamic> getVoiceInstructionsFromConfig() {
    String jsonString = remoteConfig.getString(
      FirebaseRemoteConfigKey.voiceInstructionsConfig,
    );
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return jsonMap['instructions'] ?? [];
  }

  List<dynamic> getPPGInstructionsFromConfig() {
    String jsonString = remoteConfig.getString(
      FirebaseRemoteConfigKey.ppgInstructionsConfig,
    );
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    return jsonMap['instructions'] ?? [];
  }

  Map<String, dynamic> get instructionConfigs {
    final configString =
        remoteConfig.getString(FirebaseRemoteConfigKey.instructionsConfigs);
    return json.decode(configString) as Map<String, dynamic>;
  }

  List<HRMonitoringConfigRemoteConfigModel> _getHRMonitoringConfigs() {
    final jsonString = remoteConfig
        .getValue(FirebaseRemoteConfigKey.autoMonitoringV2Config)
        .asString();

    final dynamic parsedJson = jsonDecode(jsonString);

    final List<dynamic> jsonList = parsedJson is Map
        ? (parsedJson['hr_monitoring_configs'] as List<dynamic>)
        : parsedJson as List<dynamic>;

    return jsonList
        .map((json) => HRMonitoringConfigRemoteConfigModel.fromJson(json))
        .toList();
  }

  HRMonitoringConfigRemoteConfigModel? getHRMonitoringConfigByType(int type) {
    final configs = _getHRMonitoringConfigs();
    return configs.firstWhere(
      (config) => config.type == type,
      orElse: () => throw Exception('No config found for type $type'),
    );
  }

  NavigationModel getNavigationRoutes() {
    String jsonString = remoteConfig.getString(
      FirebaseRemoteConfigKey.navigationConfig,
    );
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    NavigationModel navigationModel = NavigationModel.fromJson(jsonMap);
    return navigationModel;
  }

  Future<void> fetchAndSetBaseUrl() async {
    String devString = remoteConfig.getString(
      FirebaseRemoteConfigKey.baseUrlDevConfig,
    );
    String prodString = remoteConfig.getString(
      FirebaseRemoteConfigKey.baseUrlProdConfig,
    );
    baseURL = isProdEnv ? prodString : devString;
    webViewUrl = "$baseURL/landing";
    ringWebViewUrl = "$baseURL/patient/devices";
  }

  int getRecordingDurationSeconds() {
    // Use production key for production environment, staging key for staging
    String keyToUse = isProdEnv
        ? FirebaseRemoteConfigKey.recordingDurationSeconds
        : FirebaseRemoteConfigKey.recordingDurationSecondsStaging;
    return remoteConfig.getInt(keyToUse);
  }

  /// Get notification schedules for ring notifications from remote config
  List<Map<String, dynamic>> getRingNotificationSchedules() {
    final String jsonString = remoteConfig
        .getString(FirebaseRemoteConfigKey.ringNotificationSchedule);
    final List<dynamic> list = json.decode(jsonString);
    return List<Map<String, dynamic>>.from(list);
  }

  /// Get notification schedules for voice notifications from remote config
  List<Map<String, dynamic>> getVoiceNotificationSchedules() {
    final String jsonString = remoteConfig
        .getString(FirebaseRemoteConfigKey.voiceNotificationSchedule);
    final List<dynamic> list = json.decode(jsonString);
    return List<Map<String, dynamic>>.from(list);
  }

  /// Get notification schedules for vials notifications from remote config
  List<Map<String, dynamic>> getVialsNotificationSchedules() {
    final String jsonString = remoteConfig
        .getString(FirebaseRemoteConfigKey.vialsNotificationSchedule);
    final List<dynamic> list = json.decode(jsonString);
    return List<Map<String, dynamic>>.from(list);
  }
}

class FirebaseRemoteConfigKey {
  static String minVersion = "MIN_VERSION";
  static String appleHealthDataTypes = "APPLE_HEALTH_TYPES";
  static String androidHealthDataTypes = "ANDROID_HEALTH_TYPES";
  static String beforeNoonAmbeeApiCallCount = "AMBEE_BEFORE_NOON_ALLOWED_COUNT";
  static String afterNoonAmbeeApiCallCount = "AMBEE_AFTER_NOON_ALLOWED_COUNT";
  static String voiceContentConfig = "VOICE_CONTENT";
  static String voiceInstructionsConfig = "VOICE_INSTRUCTIONS";
  static String ppgInstructionsConfig = "PPG_INSTRUCTIONS";
  static String navigationConfig =
      isProdEnv ? "NAVIGATION_PRODUCTION" : "NAVIGATION_STAGING";
  static String instructionsConfigs = "INSTRUCTION_CONFIGS";
  static String autoMonitoringV2Config = "AUTO_MONITORING_V2";
  static String baseUrlDevConfig = "BaseUrlDev";
  static String baseUrlProdConfig = "BaseUrlProd";
  static String recordingDurationSeconds = "PPG_RECORDING_DURATION_SECONDS";
  static String recordingDurationSecondsStaging =
      "PPG_RECORDING_DURATION_SECONDS_STAGING";
  static String androidMinVersion = "ANDROID_MIN_VERSION";
  static String ringNotificationSchedule = isProdEnv
      ? "RING_NOTIFICATION_SCHEDULE_PROD"
      : "RING_NOTIFICATION_SCHEDULE_DEV";
  static String voiceNotificationSchedule = isProdEnv
      ? "VOICE_NOTIFICATION_SCHEDULE_PROD"
      : "VOICE_NOTIFICATION_SCHEDULE_DEV";
  static String vialsNotificationSchedule = isProdEnv
      ? "VIALS_NOTIFICATION_SCHEDULE_PROD"
      : "VIALS_NOTIFICATION_SCHEDULE_DEV";
}
