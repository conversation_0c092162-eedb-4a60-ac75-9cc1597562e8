import 'dart:async';
import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../utils/stream_utils.dart';
import 'firestore_service.dart';
import 'prefs_service.dart';
import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:SAiWELL/services/user_session_service.dart';

class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();    

  final AppLinks _appLinks = AppLinks();
  final RxBool _isNfcVialAdministration = false.obs;
  bool get isNfcVialAdministration => _isNfcVialAdministration.value;

  final FirestoreService _firestoreService = FirestoreService();
  final PrefsService _prefsService = PrefsService();

  // Stream controller to notify listeners of new NFC links
  final _nfcLinkController = StreamController<bool>.broadcast();
  Stream<bool> get nfcLinkStream => _nfcLinkController.stream;

  StreamSubscription<Uri>? _linkSubscription;

  // Deduplication mechanism
  String? _lastProcessedUri;
  int? _lastProcessedTimestamp;
  static const int _cooldownPeriodMs = 5000; // 5 seconds cooldown

  String? get lastProcessedUri => _lastProcessedUri;

  Future<void> initialize() async {
    try {
      // Handle initial URI if the app was opened with one
      final Uri? initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        debugPrint("🔗 [DeepLink] Found initial URI: $initialUri");
        await _handleDeepLink(initialUri);
      }

      // Listen to app links while the app is running
      _linkSubscription = _appLinks.uriLinkStream.listenSafely((uri) async {
        debugPrint("🔗 [DeepLink] Received link: $uri");
        await _handleDeepLink(uri);
      });

      debugPrint("🔗 [DeepLink] Deep link service initialized");
    } catch (e) {
      debugPrint("🔗 [DeepLink] ❌ Error setting up deep link service: $e");
    }
  }

  Future<void> _handleDeepLink(Uri uri) async {
    // Check if this is an NFC tag link for vial administration
    // Format: saiwell://vialA or saiwell://vialB (case insensitive)
    String uriString = uri.toString();
    String uriLowerCase = uriString.toLowerCase();

    if (uriLowerCase == 'saiwell://viala' || uriLowerCase == 'saiwell://vialb') {
      debugPrint("🏷️ [NFC] NFC vial URI detected: $uriString");

      // Check if user is logged in before processing
      String uid = await _prefsService.getUid();

      if (uid.isEmpty) {
        debugPrint("🏷️ [NFC] ⚠️ User not logged in yet - setting flag for later processing");
        // Still set the flag so UI can show dialog when user logs in
        _isNfcVialAdministration.value = true;
        _nfcLinkController.addSafely(true);
        return;
      }

      // Check for duplicate processing
      int currentTimestamp = DateTime.now().millisecondsSinceEpoch;

      if (_isDuplicateNfcEvent(uriString, currentTimestamp)) {
        debugPrint("🏷️ [NFC] ⚠️ Duplicate event - skipping Firebase write and UI notification");
        // Don't set flag or notify listeners for duplicate events to prevent multiple dialogs
        return;
      }

      // Update tracking variables
      _lastProcessedUri = uriString;
      _lastProcessedTimestamp = currentTimestamp;

      _isNfcVialAdministration.value = true;

      // Extract vial identifier from URI and normalize to lowercase
      String vialId = uriString.split('/').last.toLowerCase(); // Gets 'viala' or 'vialb'
      debugPrint("🏷️ [NFC] Processing vial: '$vialId' for user: $uid");

      // Do NOT write to Firebase here. Only set NFC state and emit event.
      // Use the safe extension method to add the event
      _nfcLinkController.addSafely(true);
    } else {
      debugPrint("🔗 [DeepLink] ❌ URI not recognized: '$uriString'");
    }
  }

  /// Check if this is a duplicate NFC event based on URI and timing
  bool _isDuplicateNfcEvent(String uriString, int currentTimestamp) {
    // If no previous event, it's not a duplicate
    if (_lastProcessedUri == null || _lastProcessedTimestamp == null) {
      return false;
    }

    // If it's the same URI within the cooldown period, it's a duplicate
    if (_lastProcessedUri == uriString) {
      int timeDifference = currentTimestamp - _lastProcessedTimestamp!;
      if (timeDifference < _cooldownPeriodMs) {
        return true;
      }
    }

    return false;
  }

  Future<void> _writeVialDataToFirebase(String vialId) async {
    try {
      // Get user UID (already validated in _handleDeepLink, but double-check for safety)
      String uid = await _prefsService.getUid();

      if (uid.isEmpty) {
        debugPrint("🔥 [Firebase] ❌ Cannot write vial data: User UID is empty");
        return;
      }

      // Determine vial collection name (VialA for viala, VialB for vialb)
      String vialCollection = vialId == 'viala' ? 'VialA' : 'VialB';

      // Create timestamp (Unix timestamp in milliseconds)
      int timestamp = DateTime.now().millisecondsSinceEpoch;

      debugPrint("🔥 [Firebase] Writing vial data: $vialCollection");

      // Write to Firebase at /DeviceData/uid/VialA or VialB/timestamp
      await _firestoreService.writeVialAdministrationData(
        uid: uid,
        vialCollection: vialCollection,
        timestamp: timestamp,
      );

      debugPrint("🔥 [Firebase] ✅ Vial data written successfully");
    } catch (e) {
      debugPrint("🔥 [Firebase] ❌ Error writing vial data: $e");
    }
  }

  void resetNfcFlag() {
    _isNfcVialAdministration.value = false;

    // Reset deduplication state after a delay to allow for new scans
    Future.delayed(const Duration(milliseconds: 2500), () {
      _lastProcessedUri = null;
      _lastProcessedTimestamp = null;
    });
  }

  /// Test method to simulate NFC tag scan for debugging
  /// vialId should be 'vialA' or 'vialB' (case insensitive)
  Future<void> simulateNfcScan(String vialId) async {
    debugPrint("🧪 [Test] Simulating NFC scan for vial: $vialId");
    String testUri = "saiwell://$vialId";
    Uri uri = Uri.parse(testUri);
    await _handleDeepLink(uri);
  }

  /// Call this from HomeController when the NFC dialog is shown
  Future<void> writeVialDataToFirebase(String vialId) async {
    await _writeVialDataToFirebase(vialId);
  }

  void dispose() {
    _linkSubscription?.cancel();
    _nfcLinkController.close();
  }
}