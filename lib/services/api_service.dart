import 'dart:convert';

import 'package:SAiWELL/constants/api_urls.dart';
import 'package:SAiWELL/utils/ambee_call_checker.dart';
import 'package:SAiWELL/services/analytics/events.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class BackendApi {
  static int timeoutInSecond = 10;

  static Future<http.Response> initiateGetCall(
      ApiUrls apiUrl, Map<String, String>? headers,
      {Map? params}) async {
    String paramString = "";
    if (params != null && params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String url = "";
    url = apiUrl.getUrl() + (paramString.isNotEmpty ? "?" : "") + paramString;
    debugPrint("===== url : $url");
    http.Response response = await http
        .get(Uri.parse(url), headers: headers)
        .timeout(Duration(seconds: timeoutInSecond));
    debugPrint("===== status : ${response.statusCode}");
    debugPrint("======= response :${response.body}");
    return response;
  }

  static Future<http.Response> initiatePostCall(
    ApiUrls apiUrl, {
    Map? params,
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    Map<String, String> putRequestHeaders = {
      "Content-Type": "application/json",
    };
    if (headers != null) {
      putRequestHeaders.addAll(headers);
    }
    String paramString = "";
    if (params != null && params.keys.isNotEmpty) {
      for (int i = 0; i < params.keys.length; i++) {
        paramString += params.keys.elementAt(i) +
            "=" +
            params[params.keys.elementAt(i)].toString();
        if (i + 1 < params.keys.length) {
          paramString += "&";
        }
      }
    }
    String url =
        apiUrl.getUrl() + (paramString.isNotEmpty ? "?" : "") + paramString;
    debugPrint("===== url : $url");

    http.Response? response;
    if (body != null) {
      JsonEncoder encoder = const JsonEncoder();
      debugPrint("===== body : $body");
      response = await http
          .post(Uri.parse(url),
              headers: putRequestHeaders, body: encoder.convert(body))
          .timeout(Duration(seconds: timeoutInSecond));
    } else {
      response = await http
          .post(
            Uri.parse(url),
            headers: putRequestHeaders,
          )
          .timeout(Duration(seconds: timeoutInSecond));
    }
    debugPrint("===== status : ${response.statusCode}");
    debugPrint("======= response :${response.body}");

    return response;
  }

  static Future<bool> initiatePostAmbeeApiCall(
      String? uid, double? lat, double? long) async {
    AmbeeCallHandler handler = AmbeeCallHandler();
    if (await handler.isAmbeeCallAllowed()) {
      debugPrint("=== initiatePostAmbeeApiCall calling ambee apis ===");
      Map<String, dynamic> body = {"uid": uid, "lat": lat, "lng": long};
      Map<String, String> headers = {
        "Authorization": "Bearer 771b2687-c9ea-48ac-a52f-5f83343d96b1"
      };
      LogEvents.logAmbeeDataEvent();
      await initiatePostCall(ApiUrls.postAmbeeWeather,
          body: body, headers: headers);
      await initiatePostCall(ApiUrls.postAmbeePollen,
          body: body, headers: headers);
      await initiatePostCall(ApiUrls.postAmbeeAirQuality,
          body: body, headers: headers);

      await handler.updateAmbeeCallTiming(uid);
    } else {
      debugPrint("=== initiatePostAmbeeApiCall not required ===");
    }

    return false;
  }

  static Future<String?> getStory() async {
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer 6609e7bf-9101-48af-a995-7957ea550024"
    };

    try {
      http.Response response = await initiateGetCall(ApiUrls.getStory, headers);
      if (response.statusCode == 200) {
        Map<String, dynamic> responseBody = json.decode(response.body);
        return responseBody["content"];
      } else if (response.statusCode == 401) {
        Map<String, dynamic> responseBody = json.decode(response.body);
        debugPrint("Error: ${responseBody['message']}");
        return null;
      } else {
        debugPrint("Unhandled status code: ${response.statusCode}");
        return null;
      }
    } catch (e) {
      debugPrint("Error during API call: $e");
      return null;
    }
  }

  static Future<String?> postUidAndGetCustomToken({required String uid}) async {
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer 771b2687-c9ea-48ac-a52f-5f83343d96b1"
    };

    try {
      Map<String, dynamic> body = {
        "uid": uid,
      };
      http.Response response = await initiatePostCall(
        ApiUrls.postUidAndGetToken,
        headers: headers,
        body: body,
      );

      if (response.statusCode == 200) {
        Map<String, dynamic> responseBody = json.decode(response.body);
        return responseBody["token"];
      } else if (response.statusCode == 401) {
        Map<String, dynamic> responseBody = json.decode(response.body);
        debugPrint("Error: ${responseBody['message']}");
        return null;
      } else {
        debugPrint("Unhandled status code: ${response.statusCode}");
        return null;
      }
    } catch (e) {
      debugPrint("Error during API call: $e");
      return null;
    }
  }
}
