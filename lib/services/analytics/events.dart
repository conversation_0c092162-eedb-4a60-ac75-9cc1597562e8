import 'package:device_info_plus/device_info_plus.dart';
import '../../constants/constant.dart';
import '../../models/health_datatype.dart';
import 'analytics_service.dart';

enum EventParams {
  ringFirstConnection("RING_FIRST_CONNECTION"),
  notificationClicked("NOTIFICATION_CLICKED"),
  ringConnection("RING_CONNECTION"),
  ringConnectionConnectedDevices("RING_CONNECTION_CONNECTED_DEVICES"),
  ambeeData("AMBEE_DATA"),
  ringAutoConnect("RING_AUTO_CONNECT"),
  recordSpeech("RECORD_SPEECH"),
  registerMobile("REGISTER_MOBILE"),
  emailLoginMobile("EMAIL_LOGIN_MOBILE"),
  phoneLoginMobile("PHONE_LOGIN_MOBILE");

  final String value;
  const EventParams(this.value);

  Future<void> log(Map<String, Object>? parameters) async {
    if (isProdEnv) {
      final AnalyticsService analyticsService = AnalyticsService();
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      String deviceName = "Unknown";
      String deviceOS = "Unknown";
      try {
        if (await deviceInfoPlugin.deviceInfo is AndroidDeviceInfo) {
          final androidInfo = await deviceInfoPlugin.androidInfo;
          deviceName = "${androidInfo.manufacturer} ${androidInfo.model}";
          deviceOS = "Android";
        } else if (await deviceInfoPlugin.deviceInfo is IosDeviceInfo) {
          final iosInfo = await deviceInfoPlugin.iosInfo;
          deviceName = iosInfo.name;
          deviceOS = "iOS";
        }
      } catch (e) {
        deviceName = "Unknown";
        deviceOS = "Unknown";
      }

      String uid = await prefsService.getUid();
      int currentTimestamp = DateTime.now().millisecondsSinceEpoch;

      parameters = {
        ...(parameters ?? {}),
        "uid": uid,
        "device_name": deviceName,
        "device_os": deviceOS,
        "timestamp": currentTimestamp,
      };

      analyticsService.logEvent(
        eventName: value,
        parameters: parameters,
      );
    }
  }
}

class LogEvents {
  static Future<void> logRingFirstConnectionEvent() async {
    await EventParams.ringFirstConnection.log({});
  }

  static Future<void> logNotificationClickedEvent({
    required String notificationTitle,
  }) async {
    await EventParams.notificationClicked.log({
      "notification_title": notificationTitle,
    });
  }

  static Future<void> logRingConnectionEvent() async {
    await EventParams.ringConnection.log({});
  }

  static Future<void> logRingConnectionConnectedDevicesEvent() async {
    await EventParams.ringConnectionConnectedDevices.log({});
  }

  static Future<void> logAmbeeDataEvent() async {
    await EventParams.ambeeData.log({});
  }

  static Future<void> logRingAutoConnectEvent({
    required int batteryPercent,
  }) async {
    await EventParams.ringAutoConnect.log({
      "battery_percent": batteryPercent,
    });
  }

  static Future<void> logRecordSpeechEvent() async {
    await EventParams.recordSpeech.log({});
  }

  static Future<void> logRegisterMobileEvent() async {
    await EventParams.registerMobile.log({});
  }

  static Future<void> logEmailLoginMobileEvent() async {
    await EventParams.emailLoginMobile.log({});
  }

  static Future<void> logPhoneLoginMobileEvent() async {
    await EventParams.phoneLoginMobile.log({});
  }
}
