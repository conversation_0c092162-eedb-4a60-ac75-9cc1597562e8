import 'package:flutter/material.dart';
import 'package:SAiWELL/utils/notification_test_helper.dart';
import 'package:SAiWELL/services/notifications/core/notification_manager.dart';
import 'package:SAiWELL/services/notifications/platform/android_notification_helper.dart';

class NotificationDebugScreen extends StatefulWidget {
  static const String routeName = '/notification-debug';
  
  const NotificationDebugScreen({Key? key}) : super(key: key);

  @override
  State<NotificationDebugScreen> createState() => _NotificationDebugScreenState();
}

class _NotificationDebugScreenState extends State<NotificationDebugScreen> {
  bool _isRunning = false;
  String _output = 'Tap buttons below to run diagnostics...\n';

  void _addOutput(String text) {
    setState(() {
      _output += '$text\n';
    });
  }

  Future<void> _runWithOutput(String title, Future<void> Function() action) async {
    if (_isRunning) return;
    
    setState(() {
      _isRunning = true;
      _output += '\n=== $title ===\n';
    });

    try {
      await action();
    } catch (e) {
      _addOutput('❌ Error: $e');
    }

    setState(() {
      _isRunning = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Debug'),
        backgroundColor: Colors.blue,
      ),
      body: Column(
        children: [
          // Control buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () => _runWithOutput(
                          'FULL DIAGNOSTIC',
                          () async {
                            await NotificationTestHelper.runFullDiagnostic();
                          },
                        ),
                        child: const Text('Run Full Diagnostic'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () => _runWithOutput(
                          'CHECK STATUS',
                          () async {
                            await NotificationTestHelper.checkNotificationStatus();
                          },
                        ),
                        child: const Text('Check Status'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () => _runWithOutput(
                          'TEST NOTIFICATION',
                          () async {
                            await NotificationTestHelper.scheduleTestNotification();
                          },
                        ),
                        child: const Text('Test Notification'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () => _runWithOutput(
                          'RESTART NOTIFICATIONS',
                          () async {
                            await NotificationManager.startDailyNotifications();
                            await NotificationManager.startVoiceRecordingNotifications();
                            await NotificationManager.startVialsNotifications();
                          },
                        ),
                        child: const Text('Restart All'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () => _runWithOutput(
                          'PERMISSION DIALOG',
                          () async {
                            await AndroidNotificationHelper.showPostLoginNotificationDialog();
                          },
                        ),
                        child: const Text('Show Permissions'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () => _runWithOutput(
                          'INDIVIDUAL TEST',
                          () async {
                            await NotificationTestHelper.testIndividualNotificationTypes();
                          },
                        ),
                        child: const Text('Test Individual'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isRunning ? null : () {
                          setState(() {
                            _output = 'Output cleared...\n';
                          });
                        },
                        child: const Text('Clear Output'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Output display
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: SingleChildScrollView(
                child: SelectableText(
                  _output,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    color: Colors.green,
                  ),
                ),
              ),
            ),
          ),
          
          // Status indicator
          if (_isRunning)
            Container(
              padding: const EdgeInsets.all(16),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Running diagnostic...'),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
