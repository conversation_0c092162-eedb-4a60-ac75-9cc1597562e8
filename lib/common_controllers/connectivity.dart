import 'dart:async';
import 'dart:io';

import 'package:SAiWELL/modules/no_internet/no_internet_screen.dart';
import 'package:SAiWELL/modules/splash/splash_screen.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_snackbar.dart';
import 'package:SAiWELL/utils/stream_utils.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

enum ConnectionStatus { online, offline }

enum ConnectionType { mobile, wifi, ethernet, vpn, bluetooth, other, none }

enum NoInternetDisplayType { snackBar, fullScreen }

class ConnectivityController extends GetxController {
  bool isNoInternetDisplayVisible = false;
  bool allowDisplay = true;
  final loadingCheckConnectivity = false.obs;
  final connectionStatus = ConnectionStatus.offline.obs;
  final NoInternetDisplayType displayType;
  String? previousRoute;

  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  ConnectivityController({
    this.displayType = NoInternetDisplayType.fullScreen,
  });

  @override
  void onReady() {
    super.onReady();
    initConnectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged
        .listenSafely((List<ConnectivityResult> result) {
      _updateConnectionStatus(result);
    });
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    if (isNoInternetDisplayVisible) {
      closeSnackBar();
    }
    super.onClose();
  }

  Future<void> initConnectivity() async {
    if (!Get.isRegistered<ConnectivityController>()) return;

    try {
      final List<ConnectivityResult> result =
          await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      connectionStatus.value = ConnectionStatus.offline;
      _showNoInternetDisplay();
      debugPrint(e.toString());
    }
  }

  Future<void> checkConnectivity() async {
    loadingCheckConnectivity.value = true;
    try {
      final List<ConnectivityResult> result =
          await _connectivity.checkConnectivity();
      await _updateConnectionStatus(result);
    } catch (e) {
      connectionStatus.value = ConnectionStatus.offline;
      debugPrint(e.toString());
    } finally {
      loadingCheckConnectivity.value = false;
    }
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    if (!Get.isRegistered<ConnectivityController>()) return;

    if (result.contains(ConnectivityResult.none)) {
      connectionStatus.value = ConnectionStatus.offline;
      _showNoInternetDisplay();
    } else {
      await _checkInternetConnection();
    }
  }

  Future<void> _checkInternetConnection() async {
    if (!Get.isRegistered<ConnectivityController>()) return;

    loadingCheckConnectivity.value = true;
    try {
      final result = await InternetAddress.lookup('google.com')
          .timeout(const Duration(seconds: 5));
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        connectionStatus.value = ConnectionStatus.online;
        if (isNoInternetDisplayVisible) {
          await _closeNoInternetDisplay();
          isNoInternetDisplayVisible = false;
        }
      } else {
        _showNoInternetDisplay();
      }
    } on SocketException catch (_) {
      _showNoInternetDisplay();
    } on TimeoutException catch (_) {
      _showNoInternetDisplay();
    } finally {
      loadingCheckConnectivity.value = false;
    }
  }

  void _showNoInternetDisplay() {
    if (!Get.isRegistered<ConnectivityController>()) return;

    connectionStatus.value = ConnectionStatus.offline;
    if (!isNoInternetDisplayVisible && allowDisplay) {
      isNoInternetDisplayVisible = true;

      switch (displayType) {
        case NoInternetDisplayType.snackBar:
          reusableSnackBar(message: "No Internet", isInfinite: true);
          break;
        case NoInternetDisplayType.fullScreen:
          if (Get.currentRoute != NoInternetScreen.routeName) {
            previousRoute = Get.currentRoute;
            Get.offAllNamed(NoInternetScreen.routeName);
          }
          break;
      }
    }
  }

  Future<void> _closeNoInternetDisplay() async {
    switch (displayType) {
      case NoInternetDisplayType.snackBar:
        await closeSnackBar();
        break;
      case NoInternetDisplayType.fullScreen:
        if (Get.currentRoute == NoInternetScreen.routeName) {
          Get.offAllNamed(SplashScreen.routeName);
          previousRoute = null;
        }
        break;
    }
  }

  Future<void> closeSnackBar() async {
    try {
      if (Get.isSnackbarOpen) {
        await Get.closeCurrentSnackbar();
      }
    } catch (e) {
      debugPrint('Error closing snackbar: $e');
    }
  }

  void enableDisplay() {
    allowDisplay = true;
  }
}
