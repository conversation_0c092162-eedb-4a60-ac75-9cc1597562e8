import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:get/get.dart';
import 'package:get/state_manager.dart';

import '../models/navigation_model.dart';

enum CurrentToastStatus {
  start,
  ringConnection,
  dataSyncing,
  completed,
  notConnected,
  connecting,
  connected,
}

class GlobalController extends GetxController {
  final FirebaseRemoteConfigService _configService =
      FirebaseRemoteConfigService();

  RxBool isConnected = false.obs;
  RxString ringMacAddress = "".obs;
  RxString ringName = "".obs;
  //RxString mcuFirmwareVersion = "".obs;
  // RxString model = "".obs;
  RxString deviceVersion = "".obs;
  RxInt ringBatteryPercentage = (0).obs;
  RxString macId = "".obs;
  Rx<NavigationModel> navigationRouteModel = NavigationModel().obs;
  RxList<Navigation> navigationItems = <Navigation>[].obs;
  RxList<Navigation> moreNavigationItems = <Navigation>[].obs;
  RxList<String> navigationItemsImages = <String>[].obs;
  RxList<String> moreNavigationItemsImages = <String>[].obs;
  Rx<String> navigationBaseUrl = "".obs;
  Rx<CurrentToastStatus> currentToastStatus = CurrentToastStatus.start.obs;
  RxBool shouldShowNavbar = false.obs;

  @override
  void onInit() {
    super.onInit();
    setNavbarItems();
  }

  setNavbarItems() {
    navigationRouteModel.value = _configService.getNavigationRoutes();
    navigationRouteModel.refresh();
    navigationBaseUrl.value = navigationRouteModel.value.baseUrl ?? "";
    for (int i = 0;
        i < (navigationRouteModel.value.navigation?.length ?? 0);
        i++) {
      if (navigationRouteModel.value.navigation?[i].isForNavbar == true) {
        navigationItems.add(navigationRouteModel.value.navigation![i]);

        navigationItems.refresh();
      } else {
        moreNavigationItems.add(navigationRouteModel.value.navigation![i]);
        moreNavigationItems.refresh();
      }
    }
    navigationItemsImages.addAll([
      AppImages.icInsights,
      AppImages.icDevices,
      AppImages.icMore,
    ]);
    moreNavigationItemsImages.addAll([
      AppImages.icPayment,
      AppImages.icPayment,
      AppImages.icSupport,
      AppImages.icExplore,
    ]);
  }
}
