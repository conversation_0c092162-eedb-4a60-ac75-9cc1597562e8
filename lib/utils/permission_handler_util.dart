import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionHandlerUtil {
  // Singleton pattern
  static final PermissionHandlerUtil _instance =
      PermissionHandlerUtil._internal();
  factory PermissionHandlerUtil() => _instance;
  PermissionHandlerUtil._internal();

  /// Shows a reusable permission dialog with customizable buttons
  Future<bool> showPermissionDialog({
    required String title,
    required String message,
    required String cancelButtonText,
    required String confirmButtonText,
  }) async {
    return await showDialog<bool>(
      context: Get.context!,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelButtonText),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context, true);
            },
            child: Text(confirmButtonText),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Opens app settings and ensures dialog is closed first
  Future<void> openSettings() async {
    await openAppSettings();
  }

  /// Generic permission check method with customizable popup message
  Future<bool> checkPermission(
    Permission permission, {
    required String title,
    required String message,
    String cancelButtonText = 'Cancel',
    String settingsButtonText = 'Open Settings',
  }) async {
    final status = await permission.status;

    if (status.isGranted || status.isLimited) {
      return true;
    }

    if (status.isPermanentlyDenied) {
      // Show dialog explaining why we need permission and how to enable it
      final shouldOpenSettings = await showPermissionDialog(
        title: title,
        message: message,
        cancelButtonText: cancelButtonText,
        confirmButtonText: settingsButtonText,
      );

      if (shouldOpenSettings) {
        await openSettings();
      }
      return false;
    }

    // Request permission if not permanently denied
    final result = await permission.request();

    // If denied but not permanently, consider showing a rationale dialog
    if (result.isDenied) {
      // Show a simpler dialog explaining the need for permission
      final shouldOpenSettings = await showPermissionDialog(
        title: title,
        message: message,
        cancelButtonText: cancelButtonText,
        confirmButtonText: settingsButtonText,
      );

      if (shouldOpenSettings) {
        await openSettings();
      }
      return false;
    }

    return result.isGranted || result.isLimited;
  }

  /// Check microphone permission with standardized message
  Future<bool> checkMicrophonePermission() async {
    return checkPermission(
      Permission.microphone,
      title: 'Microphone Permission Required',
      message:
          'This app needs microphone access to record audio. Please enable it in settings.',
    );
  }

  /// Check storage permission with standardized message
  Future<bool> checkStoragePermission() async {
    return checkPermission(
      Permission.storage,
      title: 'Storage Permission Required',
      message:
          'This app needs storage access to save files. Please enable it in settings.',
    );
  }

  /// Check location permission with standardized message
  Future<bool> checkLocationPermission() async {
    return checkPermission(
      Permission.location,
      title: 'Location Permission Required',
      message:
          'This app needs location access to provide location-based services. Please enable it in settings.',
    );
  }

  /// Check bluetooth scan permission with standardized message
  Future<bool> checkBluetoothScanPermission() async {
    if (Platform.isIOS) return true; // iOS handles differently

    return checkPermission(
      Permission.bluetoothScan,
      title: 'Bluetooth Permission Required',
      message:
          'This app needs bluetooth scanning access to connect to nearby devices. Please enable it in settings.',
    );
  }

  /// Check bluetooth connect permission with standardized message
  Future<bool> checkBluetoothConnectPermission() async {
    if (Platform.isIOS) return true; // iOS handles differently

    return checkPermission(
      Permission.bluetoothConnect,
      title: 'Bluetooth Permission Required',
      message:
          'This app needs bluetooth connection access to connect to nearby devices. Please enable it in settings.',
    );
  }

  /// Check activity recognition permission with standardized message
  Future<bool> checkActivityRecognitionPermission() async {
    return checkPermission(
      Permission.activityRecognition,
      title: 'Activity Recognition Permission Required',
      message:
          'This app needs activity recognition access to track your activities. Please enable it in settings.',
    );
  }

  /// Check camera permission with standardized message
  Future<bool> checkCameraPermission() async {
    return checkPermission(
      Permission.camera,
      title: 'Camera Permission Required',
      message:
          'This app needs camera access to capture images and analyze face data. Please enable it in settings.',
    );
  }

  /// Check multiple permissions at once
  Future<bool> checkMultiplePermissions(
    List<Permission> permissions, {
    required String title,
    required String message,
  }) async {
    // Check if any permission is permanently denied
    for (var permission in permissions) {
      final status = await permission.status;
      if (status.isPermanentlyDenied) {
        final shouldOpenSettings = await showPermissionDialog(
          title: title,
          message: message,
          cancelButtonText: 'Cancel',
          confirmButtonText: 'Open Settings',
        );

        if (shouldOpenSettings) {
          await openSettings();
        }
        return false;
      }
    }

    // Request permissions for those not granted yet
    Map<Permission, PermissionStatus> statuses = await permissions.request();

    bool allGranted = true;
    for (var entry in statuses.entries) {
      if (!entry.value.isGranted && !entry.value.isLimited) {
        allGranted = false;
        break;
      }
    }

    return allGranted;
  }
}
