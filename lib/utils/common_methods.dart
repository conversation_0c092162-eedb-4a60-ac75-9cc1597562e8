import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';

class CommonMethods {
  static void closeKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  static List<Map<String, dynamic>> convertObjectListToMapList(
      List combinedDataResponse) {
    List<Map<String, dynamic>> convertedData = combinedDataResponse.map((item) {
      return convertObjectToMap(item);
    }).toList();

    return convertedData;
  }

  static Map<String, dynamic> convertObjectToMap(dynamic item) {
    return Map<String, dynamic>.from(
      (item as Map).map((key, value) {
        return MapEntry(key.toString(), value);
      }),
    );
  }

  static Timestamp getTodaysMidnightTimeStamp() {
    DateTime todayMidnight = DateTime.now();
    todayMidnight =
        DateTime(todayMidnight.year, todayMidnight.month, todayMidnight.day);
    return Timestamp.fromDate(todayMidnight);
  }

  static int getTodaysMidnightSeconds() {
    DateTime todayMidnight = DateTime.now();
    todayMidnight =
        DateTime(todayMidnight.year, todayMidnight.month, todayMidnight.day);
    return todayMidnight.millisecondsSinceEpoch ~/ 1000;
  }

  static double parseDurationToMs(String durationString) {
    try {
      final parts = durationString.split(':');
      if (parts.length != 2) return 0.0;

      final minutesParts = parts[0].split('.');
      final secondsParts = parts[1].split('.');

      final minutes = int.parse(minutesParts[0]);
      final seconds = int.parse(secondsParts[0]);
      final milliseconds = secondsParts.length > 1
          ? int.parse(secondsParts[1].padRight(2, '0').substring(0, 2))
          : 0;

      return (minutes * 60 * 1000 + seconds * 1000 + milliseconds).toDouble();
    } catch (e) {
      print('Error parsing duration: $e');
      return 0.0;
    }
  }

  static String formatMilliSecondsToMMSS(double milliseconds) {
    final duration = Duration(milliseconds: milliseconds.toInt());
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}
