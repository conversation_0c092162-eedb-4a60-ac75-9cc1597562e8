import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/services/firestore_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';

class AmbeeCallHandler {
  PrefsService service = PrefsService();
  FirestoreService firestoreService = FirestoreService();
  FirebaseRemoteConfigService firebaseRemoteConfigService =
      FirebaseRemoteConfigService();
  Future<bool> isAmbeeCallAllowed() async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    bool isAllowed = false;

    final todayNoon = today.add(const Duration(hours: 12));
    int lastCallMillis = await service.getLastAmbeeApiCalledAt();
    final lastCallTime = DateTime.fromMillisecondsSinceEpoch(lastCallMillis);
    if (lastCallTime.isBefore(today)) {
      isAllowed = true;
      await service.setTodaysAfterNoonAmbeeApiCallCount(0);
      await service.setTodaysBeforeNoonAmbeeApiCallCount(0);
    } else if (now.isBefore(todayNoon)) {
      int current = await service.getTodaysBeforeNoonAmbeeApiCallCount();
      int limit =
          firebaseRemoteConfigService.getTodaysBeforeNoonAmbeeApiCallCount();
      if (current >= limit) {
        isAllowed = false;
      } else {
        // isAllowed =
        //     !lastCallTime.isAfter(today) && !lastCallTime.isBefore(today);
        isAllowed = true;
      }
    } else {
      int current = await service.getTodaysAfterNoonAmbeeApiCallCount();
      int limit =
          firebaseRemoteConfigService.getTodaysAfterNoonAmbeeApiCallCount();
      if (current >= limit) {
        isAllowed = false;
      } else {
        // isAllowed = !lastCallTime.isAfter(todayNoon) &&
        //     !lastCallTime.isBefore(todayNoon);
        isAllowed = true;
      }
    }
    return isAllowed;
  }

  Future<void> updateAmbeeCallTiming(String? uid) async {
    service.setLastAmbeeApiCalledAt(DateTime.now().millisecondsSinceEpoch);
   // firestoreService.storeTestAmbeeApiCallTime(uid!);

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final todayNoon = today.add(const Duration(hours: 12));
    if (todayNoon.isBefore(DateTime.now())) {
      service.setTodaysAfterNoonAmbeeApiCallCount(
          await service.getTodaysAfterNoonAmbeeApiCallCount() + 1);
    } else {
      service.setTodaysBeforeNoonAmbeeApiCallCount(
          await service.getTodaysBeforeNoonAmbeeApiCallCount() + 1);
    }
  }
}
