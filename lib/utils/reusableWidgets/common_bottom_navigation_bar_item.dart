import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../const/app_colors.dart';

BottomNavigationBarItem commonBottomNavigationBarItem(
    {required String icon,
    required String label,
    required int itemIndex,
    required int currentIndex,
    required int navbarLength}) {

  bool isSelected = currentIndex >= 0 &&
      currentIndex < navbarLength &&
      itemIndex == currentIndex;

  if ((currentIndex >= navbarLength - 1) && (itemIndex >= navbarLength - 1)) {
    isSelected = true;
  }

  return BottomNavigationBarItem(
    icon: SvgPicture.asset(
      icon,
      colorFilter: ColorFilter.mode(
          isSelected ? AppColors.primaryOrange : AppColors.veryDarkTeal,
          BlendMode.srcIn),
    ),
    label: label,
  );
}
