import 'package:flutter/material.dart';
import 'package:get/get.dart';

late SnackbarController snackbarController;

reusableSnackBar({
  required String message,
  int duration = 2,
  bool isForError = true,
  bool isInfinite = false,
  String? actionLabel,
  VoidCallback? onActionPressed,
}) {
  closeSnackBar();
  snackbarController = Get.showSnackbar(
    GetSnackBar(
      borderRadius: 12,
      snackStyle: SnackStyle.FLOATING,
      duration: isInfinite ? null : Duration(seconds: duration),
      backgroundColor: isForError ? Colors.red : Colors.green.shade400,
      titleText: Text(
        isForError ? "Error" : "Success",
        style: const TextStyle(
            color: Colors.white, fontWeight: FontWeight.w700, fontSize: 17),
      ),
      messageText: Text(
        message,
        style: const TextStyle(color: Colors.white, fontSize: 14),
      ),
      mainButton: actionLabel != null && onActionPressed != null
          ? TextButton(
              onPressed: () {
                closeSnackBar();
                onActionPressed();
              },
              child: Text(
                actionLabel,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : null,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
    ),
  );
}

void closeSnackBar() {
  if (Get.isSnackbarOpen) {
    snackbarController.close();
  }
}
