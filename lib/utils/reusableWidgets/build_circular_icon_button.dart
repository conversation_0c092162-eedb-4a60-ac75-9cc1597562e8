import 'package:flutter/material.dart';

Widget buildCircularIconButton({
  required IconData icon,
  required Color color,
  required Color iconColor,
  required VoidCallback onPressed,
}) {
  return Container(
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: color,
    ),
    child: IconButton(
      icon: Icon(icon, size: 50, color: iconColor),
      onPressed: onPressed,
      padding: const EdgeInsets.all(8),
    ),
  );
}
