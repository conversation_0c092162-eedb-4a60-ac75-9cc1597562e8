import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:flutter/material.dart';

class DurationText extends StatelessWidget {
  final String duration; // Pass duration as string

  const DurationText({super.key, required this.duration});

  @override
  Widget build(BuildContext context) {
    String formatDuration(String duration) {
      final parts = duration.split(':');
      String twoDigits(String n) => n.padLeft(2, '0');
      String minutes = '00';
      String seconds = '00';
      String milliseconds = '00';

      if (parts.length == 3) {
        minutes = twoDigits(
            (int.parse(parts[0]) * 60 + int.parse(parts[1])).toString());
        seconds = twoDigits(parts[2]);
      } else if (parts.length == 2) {
        minutes = twoDigits(parts[0]);
        seconds = twoDigits(parts[1]);
      } else if (parts.length == 1) {
        seconds = twoDigits(parts[0]);
      }

      if (parts.last.contains('.')) {
        final secParts = parts.last.split('.');
        seconds = twoDigits(secParts[0]);
        milliseconds = twoDigits(secParts[1].substring(0, 2));
      }

      return '$minutes:$seconds:$milliseconds';
    }

    final formattedDuration = formatDuration(duration);
    final parts = formattedDuration.split(':');

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (int i = 0; i < parts.length; i++) ...[
          Text(
            parts[i],
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color:
                  parts[i] == "00" ? AppColors.lightGray : AppColors.blackGray,
            ),
          ),
          if (i == 0)
            Text(
              ":",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: parts[i] == "00"
                    ? AppColors.lightGray
                    : AppColors.blackGray,
              ),
            ),
          if (i == 1)
            Text(
              ".",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppColors.blackGray,
              ),
            ),
        ],
      ],
    );
  }
}
