import 'package:flutter/material.dart';

class ReusableBottomSheet extends StatefulWidget {
  final Widget child;
  final String title;
  final VoidCallback? onClose;

  const ReusableBottomSheet({
    super.key,
    required this.child,
    required this.title,
    this.onClose,
  });

  @override
  _ReusableBottomSheetState createState() => _ReusableBottomSheetState();
}

class _ReusableBottomSheetState extends State<ReusableBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(32.0),
        ),
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.75,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  Navigator.of(context).pop();
                  if (widget.onClose != null) {
                    widget.onClose!();
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 8.0),
          Expanded(
            child: SingleChildScrollView(child: widget.child),
          ),
        ],
      ),
    );
  }
}