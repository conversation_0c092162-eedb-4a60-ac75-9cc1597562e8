import 'package:flutter/material.dart';

class ReusableButton extends StatelessWidget {
  final String title;
  final bool isLoading;
  final Color? color;
  final Color? borderColor;
  final Color? fontColor;
  final double height;
  final double width;
  final double radius;
  final double fontSize;
  final double elevation;

  final void Function()? onTap;

  const ReusableButton({
    super.key,
    required this.title,
    this.isLoading = false,
    this.color,
    this.fontSize = 16,
    required this.onTap,
    this.height = 54,
    this.width = double.infinity,
    this.radius = 4,
    this.borderColor,
    this.elevation = 0,
    this.fontColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      child: Opacity(
        opacity: (onTap == null || isLoading) ? .5 : 1,
        child: Material(
          elevation: elevation,
          borderRadius: BorderRadius.circular(radius),
          child: Container(
            height: height,
            width: width,
            decoration: BoxDecoration(
                color: color ?? const Color(0xffF26511),
                borderRadius: BorderRadius.circular(radius),
                border: Border.all(
                    color: borderColor ?? const Color(0xffF26511), width: 1)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Center(
                child: isLoading
                    ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.5,
                    color: Colors.white,
                  ),
                )
                    : FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    title,
                    style: TextStyle(
                        fontSize: fontSize,
                        color: fontColor ??
                            (color == Colors.white
                                ? Colors.black
                                : Colors.white),
                        fontWeight: FontWeight.w700,
                        letterSpacing: .5),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
