class SleepDataFormatter {
  Map<String, int> convertSecondsToHrMin(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    return {'hour': hours, 'mins': minutes};
  }

  Map<String, int> convertMinutesToHrMin(int minutes) {
    int hours = minutes ~/ 60;
    int remainingMinutes = minutes % 60;
    return {'hour': hours, 'mins': remainingMinutes};
  }

  Map<String, int> calculateTotalSleep(int lightSleepHrs, int lightSleepMin,
      int deepSleepHrs, int deepSleepMin, int remSleepHrs, int remSleepMin) {
    int totalHours = lightSleepHrs + deepSleepHrs + remSleepHrs;
    int totalMinutes = lightSleepMin + deepSleepMin + remSleepMin;

    if (totalMinutes >= 60) {
      totalHours += totalMinutes ~/ 60;
      totalMinutes %= 60;
    }

    return {'hour': totalHours, 'mins': totalMinutes};
  }

  Map<String, dynamic> formatSleepData(Map<String, dynamic> data) {
    if (data['deepSleepCount'] == 65535) {
      var lightSleep = convertSecondsToHrMin(data['lightSleepSeconds'] ?? 0);
      var deepSleep = convertSecondsToHrMin(data['deepSleepSeconds'] ?? 0);
      var remSleep = convertSecondsToHrMin(data['remSleepSeconds'] ?? 0);
      return {
        "sleepDuration": calculateTotalSleep(
            lightSleep['hour'] ?? 0,
            lightSleep['mins'] ?? 0,
            deepSleep['hour'] ?? 0,
            deepSleep['mins'] ?? 0,
            remSleep['hour'] ?? 0,
            remSleep['mins'] ?? 0),
        "lightSleep": lightSleep,
        "deepSleep": deepSleep,
        "remSleep": remSleep,
      };
    } else {
      var lightSleep = convertMinutesToHrMin(data['lightSleepMinutes'] ?? 0);
      var deepSleep = convertMinutesToHrMin(data['deepSleepMinutes'] ?? 0);
      var remSleep = convertMinutesToHrMin(data['remSleepMinutes'] ?? 0);
      int sleepDurationSec =
          (data['endTimeStamp'] ?? 0) - (data['startTimeStamp'] ?? 0);
      var sleepDuration = convertSecondsToHrMin(sleepDurationSec);

      return {
        "sleepDuration": sleepDuration,
        "lightSleep": lightSleep,
        "deepSleep": deepSleep,
        "remSleep": remSleep,
      };
    }
  }
}
