import 'package:flutter/material.dart';
import 'package:SAiWELL/services/notifications/core/notification_scheduler_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/services/user_session_service.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:get/get.dart';

/// Temporary helper class for testing notifications when app is killed
/// This can be removed once you verify notifications are working
class NotificationTestHelper {
  static final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Schedule a test notification in 1 minute to verify background delivery
  static Future<void> scheduleTestNotification() async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'test_channel',
        'Test Notifications',
        channelDescription: 'Test notifications to verify background delivery',
        importance: Importance.max,
        priority: Priority.max,
        playSound: true,
        enableVibration: true,
        icon: '@mipmap/launcher_icon',
        color: Color(0xff6aaa64),
        colorized: true,
        autoCancel: true,
        ongoing: false,
        showWhen: true,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            'open_app',
            'Open SAiWELL',
            showsUserInterface: true,
          ),
        ],
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: null,
        categoryIdentifier: 'test',
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      final DateTime scheduledTime = DateTime.now().add(const Duration(minutes: 1));
      const String title = 'SAiWELL Test Notification';
      const String body = 'This notification was scheduled 1 minute ago. If you see this after killing the app, background notifications are working!';

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        999, // Test notification ID
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

    } catch (e) {
      // Keep error log for test notification scheduling
      debugPrint('Error scheduling test notification: $e');
    }
  }

  /// Cancel the test notification
  static Future<void> cancelTestNotification() async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(999);
    } catch (e) {
      // Keep error log for test notification cancellation
      debugPrint('Error cancelling test notification: $e');
    }
  }


  /// Check current notification permissions and settings
  static Future<void> checkNotificationStatus() async {
    try {
      // Get pending notifications
      final List<PendingNotificationRequest> pending = await NotificationSchedulerService.getPendingNotifications();

      if (pending.isEmpty) {
        // Keep important status log
        debugPrint('⚠️  NO PENDING NOTIFICATIONS FOUND!');
      }
    } catch (e) {
      // Keep error log for notification status check
      debugPrint('❌ Error checking notification status: $e');
    }
  }

  /// Comprehensive diagnostic for all notification types
  static Future<void> runFullDiagnostic() async {
    try {
      // 1. Check basic notification status
      await checkNotificationStatus();

      // 2. Check user eligibility for different notification types
      await _checkUserEligibility();

      // 3. Check Firebase Remote Config values
      await _checkRemoteConfigValues();

      // 4. Test notification scheduling
      await _testNotificationScheduling();
    } catch (e) {
      // Keep error log for full diagnostic
      debugPrint('❌ Error running full diagnostic: $e');
    }
  }

  static Future<void> _checkUserEligibility() async {
    try {
      // Check ring connection status
      final prefsService = PrefsService();
      await prefsService.getLastConnectedDeviceMac();

      // Check user session eligibility
      try {
        final userSession = Get.find<UserSessionService>();
        userSession.isUserEligibleForVoiceRecording();
        userSession.isUserEligibleForSlit();
      } catch (e) {
        // Keep error log for user session eligibility check
        debugPrint('⚠️  Could not check user session eligibility: $e');
      }

    } catch (e) {
      // Keep error log for user eligibility check
      debugPrint('❌ Error checking user eligibility: $e');
    }
  }

  static Future<void> _checkRemoteConfigValues() async {
    try {
      final remoteConfig = FirebaseRemoteConfigService();

      // Check notification schedules
      remoteConfig.getRingNotificationSchedules();
      remoteConfig.getVoiceNotificationSchedules();
      remoteConfig.getVialsNotificationSchedules();
    } catch (e) {
      // Keep error log for remote config check
      debugPrint('❌ Error checking remote config values: $e');
    }
  }

  static Future<void> _testNotificationScheduling() async {
    try {
      // Test ring notifications
      try {
        await NotificationSchedulerService.scheduleRingNotifications();
      } catch (e) {
        // Keep error log for ring notifications
        debugPrint('❌ Ring notifications failed: $e');
      }

      // Test voice notifications
      try {
        await NotificationSchedulerService.scheduleVoiceNotifications();
      } catch (e) {
        // Keep error log for voice notifications
        debugPrint('❌ Voice notifications failed: $e');
      }

      // Test vials notifications
      try {
        await NotificationSchedulerService.scheduleVialsNotifications();
      } catch (e) {
        // Keep error log for vials notifications
        debugPrint('❌ Vials notifications failed: $e');
      }

      // Check what was actually scheduled
      await Future.delayed(const Duration(milliseconds: 500));
      await checkNotificationStatus();
    } catch (e) {
      // Keep error log for notification scheduling test
      debugPrint('❌ Error testing notification scheduling: $e');
    }
  }

  /// Test individual notification types separately
  static Future<void> testIndividualNotificationTypes() async {
    try {
      debugPrint('🔬 === INDIVIDUAL NOTIFICATION TYPE TEST ===');

      // Clear all existing notifications first
      debugPrint('Clearing existing notifications...');
      for (int i = 1000; i < 4000; i++) {
        await NotificationSchedulerService.cancelNotification(i);
      }

      await Future.delayed(const Duration(milliseconds: 500));

      // Test each type individually
      debugPrint('Testing voice notifications only...');
      await NotificationSchedulerService.scheduleVoiceNotifications();
      await Future.delayed(const Duration(milliseconds: 300));
      await checkNotificationStatus();

      debugPrint('Testing vials notifications only...');
      await NotificationSchedulerService.scheduleVialsNotifications();
      await Future.delayed(const Duration(milliseconds: 300));
      await checkNotificationStatus();

      debugPrint('Testing ring notifications only...');
      await NotificationSchedulerService.scheduleRingNotifications();
      await Future.delayed(const Duration(milliseconds: 300));
      await checkNotificationStatus();

      debugPrint('🔬 === END INDIVIDUAL TEST ===');
    } catch (e) {
      debugPrint('❌ Error testing individual notification types: $e');
    }
  }

  /// Test production notification scheduling (for debugging)
  static Future<void> testProductionNotificationScheduling() async {
    try {
      debugPrint('=== Testing Production Notification Scheduling ===');
      // Schedule a test production notification in 2 minutes to verify it works
      await _scheduleTestProductionNotification();
      // Check what was scheduled
      await checkNotificationStatus();

      debugPrint('=== Production Notification Test Complete ===');
    } catch (e) {
      debugPrint('Error testing production notification scheduling: $e');
    }
  }

  /// Schedule a test production notification in 2 minutes using production settings
  static Future<void> _scheduleTestProductionNotification() async {
    try {
      // Use the same notification details as production notifications
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'scheduled_reminders', // Use production channel
        'Scheduled Reminders',
        channelDescription: 'Regular health check reminders',
        importance: Importance.max,
        priority: Priority.max,
        playSound: true,
        enableVibration: true,
        icon: '@mipmap/launcher_icon',
        color: Color(0xff6aaa64),
        colorized: true,
        autoCancel: true,
        ongoing: false,
        showWhen: true,
        category: AndroidNotificationCategory.reminder,
        visibility: NotificationVisibility.public,
        actions: <AndroidNotificationAction>[
          AndroidNotificationAction(
            'open_app',
            'Open SAiWELL',
            showsUserInterface: true,
          ),
        ],
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'default',
        badgeNumber: null,
        categoryIdentifier: 'production_test',
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      final DateTime scheduledTime = DateTime.now().add(const Duration(minutes: 2));
      const String title = 'SAiWELL Production Test';
      const String body = 'This is a production notification test scheduled 2 minutes ago. If you see this after killing the app, production notifications are working!';

      await _flutterLocalNotificationsPlugin.zonedSchedule(
        998, // Production test notification ID (different from regular test ID 999)
        title,
        body,
        tz.TZDateTime.from(scheduledTime, tz.local),
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      debugPrint('Production test notification scheduled for ${scheduledTime.toString()}');
      debugPrint('Kill the app now and wait 2 minutes to test production notification delivery!');
    } catch (e) {
      debugPrint('Error scheduling production test notification: $e');
    }
  }
}
