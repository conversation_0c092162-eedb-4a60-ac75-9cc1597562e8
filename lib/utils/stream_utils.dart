import 'dart:async';
import 'package:flutter/foundation.dart';

/// Extension methods for StreamController to add events safely
extension SafeStreamController<T> on StreamController<T> {
  /// Add an event to the stream only if the controller is not closed
  bool addSafely(T event) {
    if (!isClosed) {
      add(event);
      return true;
    } else {
      debugPrint('Warning: Attempted to add an event to a closed StreamController');
      return false;
    }
  }
}

/// Extension methods for streams to listen safely
extension SafeStreamSubscription<T> on Stream<T> {
  /// Listen to a stream with safety checks for controller disposal
  StreamSubscription<T> listenSafely(
    void Function(T) onData, {
    Function? onError,
    void Function()? onDone,
    bool? cancelOnError,
  }) {
    return listen(
      onData,
      onError: onError,
      onDone: onDone,
      cancelOnError: cancelOnError,
    );
  }
} 