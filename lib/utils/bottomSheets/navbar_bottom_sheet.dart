import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../const/app_colors.dart';
import '../const/app_images.dart';
import '../reusableWidgets/reusable_bottom_sheet.dart';

void showNavBarBottomSheet({
  required BuildContext context,
  required void Function(int i) onItemClick,
}) {
  HomeController homeController = Get.find<HomeController>();
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(32.0),
      ),
    ),
    builder: (context) {
      return SizedBox(
        height:
            105.0 * homeController.globalController.moreNavigationItems.length,
        child: ReusableBottomSheet(
          title: '',
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListView.separated(
                itemCount:
                    homeController.globalController.moreNavigationItems.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  int addedIndex = index +
                      homeController.globalController.navigationItems.length;
                  bool isSelected =
                      homeController.selectedNavBarIndex.value == addedIndex;
                  return InkWell(
                      onTap: () {
                        Get.back();
                        onItemClick(index);
                      },
                      child: Obx(
                        () => ListTile(
                          leading: SvgPicture.asset(
                            homeController.globalController
                                .moreNavigationItemsImages[index],
                            colorFilter: ColorFilter.mode(
                              isSelected
                                  ? AppColors.primaryOrange
                                  : AppColors.veryDarkTeal,
                              BlendMode.srcIn,
                            ),
                            height: 22,
                            width: 22,
                          ),
                          title: Text(
                            homeController.globalController
                                    .moreNavigationItems[index].label ??
                                "",
                            style: TextStyle(
                              fontSize: 16,
                              color: isSelected
                                  ? AppColors.primaryOrange
                                  : AppColors.veryDarkTeal,
                            ),
                          ),
                          trailing: SvgPicture.asset(AppImages.icForward),
                        ),
                      ));
                },
                separatorBuilder: (BuildContext context, int index) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 55, right: 26),
                    child: Divider(
                      thickness: 1,
                      color: AppColors.duskyBlue.withOpacity(.1),
                    ),
                  );
                },
              )
            ],
          ),
          onClose: () {},
        ),
      );
    },
  );
}
