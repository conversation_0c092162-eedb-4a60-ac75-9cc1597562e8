
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../const/app_colors.dart';
import '../reusableWidgets/reusable_button.dart';
import '../reusableWidgets/reusable_dialog.dart';

void successfullyUploadedDialog({
  required BuildContext context,
  required String message,
  required Future<Null> Function() onTap,
}) {
  ReusableDialog.show(
    isDismissible: false,
    borderRadius: BorderRadius.circular(8),
    contentPadding: const EdgeInsets.symmetric(
      horizontal: 24,
      vertical: 36,
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(
          height: 20,
        ),
        SvgPicture.asset(
          AppImages.icCheck,
          height: 69,
        ),
        const SizedBox(
          height: 18,
        ),
        Text(
          message,
          style: TextStyle(
            fontWeight: FontWeight.w700,
            color: AppColors.primaryTeal,
            fontSize: 22,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        ReusableButton(
          title: "Continue",
          onTap: onTap,
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    ),
  );
}
