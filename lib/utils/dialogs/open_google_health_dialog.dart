import 'package:flutter/material.dart';
import '../reusableWidgets/reusable_button.dart';
import '../reusableWidgets/reusable_dialog.dart';

void openGoogleHealthDialog(
    {required bool isAppDownloaded, required Future<void> Function() onTap}) {
  ReusableDialog.show(
    isDismissible: false,
    borderRadius: BorderRadius.circular(8),
    contentPadding: const EdgeInsets.symmetric(
      horizontal: 16,
      vertical: 24,
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 20,
        ),
        const Text(
          "Please grant SAiWELL permission to access your health data through the Health Connect app.",
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.black,
            fontSize: 20,
          ),
          textAlign: TextAlign.start,
        ),
        const SizedBox(
          height: 16,
        ),
        const Text(
          "Steps to allow permission:",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.black,
            fontSize: 20,
          ),
          textAlign: TextAlign.start,
        ),
        const SizedBox(
          height: 8,
        ),
        const Text(
          "1.You will be redirected to the Play Store to download the Health Connect app.\n2.Install the app and navigate to the permissions section.\n3.Grant access to SAiWELL in the permissions section.\n4.Open the SAiWELL app to complete the setup.",
          style: TextStyle(
            fontWeight: FontWeight.w400,
            color: Colors.black,
            fontSize: 16,
          ),
          textAlign: TextAlign.start,
        ),
        const SizedBox(height: 32),
        ReusableButton(
          title: isAppDownloaded ? "Open" : "Download",
          onTap: onTap,
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    ),
  );
}
