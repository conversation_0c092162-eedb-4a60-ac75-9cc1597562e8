import 'package:SAiWELL/utils/reusableWidgets/duration_text.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../services/audio_service.dart';
import '../../../utils/const/app_colors.dart';
import '../../../utils/reusableWidgets/build_circular_icon_button.dart';
import '../../../utils/reusableWidgets/custom_app_bar.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../controller/recording_controller.dart';

class RecordingScreen extends StatefulWidget {
  const RecordingScreen({super.key});

  static const routeName = "/recordingScreen";

  @override
  State<RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends State<RecordingScreen> {
  RecordingController recordingController = Get.find<RecordingController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "Recording"),
      body: SafeArea(
        child: _buildRecordingBody(context),
      ),
    );
  }

  Widget _buildRecordingBody(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 28),
          _buildTitleSection(),
          const SizedBox(height: 30),
          _buildRecordingControls(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildTitleSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Record the following text",
            style: TextStyle(
              color: AppColors.primaryTeal,
              fontSize: 20,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 12),
          Obx(
            () => recordingController.isStoryLoading.value
                ? Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.accentTeal),
                    ),
                  )
                : Text(
                    recordingController.voiceContent.value,
                    textAlign: TextAlign.justify,
                    style: TextStyle(
                      color: AppColors.mediumGray,
                      height: 1.6,
                      fontSize: 14,
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Widget _buildRecordingControls() {
    return Obx(() {
      if (recordingController.isStoryLoading.value) {
        return const SizedBox();
      }

      if (recordingController.audioRecordingService.recordingState.value ==
              RecordingState.recording ||
          recordingController.audioRecordingService.recordingState.value ==
              RecordingState.paused) {
        return _buildRecordingInProgressView();
      }

      // Initial state
      // Initial state
      return _buildInitialRecordingView();
    });
  }

  Widget _buildRecordingInProgressView() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: ReusableButton(
            title: "Recording",
            onTap: () {},
            color: AppColors.disabledGray,
            borderColor: AppColors.disabledGray,
          ),
        ),
        SizedBox(
          height: context.height * .033,
        ),
        Divider(
          color: Colors.black.withOpacity(.1),
        ),
        const SizedBox(
          height: 18,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: AudioWaveforms(
            recorderController:
                recordingController.audioRecordingService.wavefromController,
            size: Size(MediaQuery.of(context).size.width, 30),
            enableGesture: false,
            waveStyle: WaveStyle(
              waveColor: AppColors.accentTeal,
              extendWaveform: true,
              showMiddleLine: false,
              durationLinesHeight: 30,
              scaleFactor: 80,
              waveCap: StrokeCap.square,
              waveThickness: 1.8,
              spacing: 6.0,
            ),
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        DurationText(duration: recordingController.recordingDuration),
        SizedBox(
          height: context.height * .012,
        ),
        Divider(
          color: Colors.black.withOpacity(.1),
        ),
        const SizedBox(
          height: 16,
        ),
        _buildRecordingControlButtons(),
      ],
    );
  }

  Widget _buildRecordingControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        buildCircularIconButton(
          icon:
              recordingController.audioRecordingService.recordingState.value ==
                      RecordingState.recording
                  ? Icons.pause
                  : Icons.play_arrow,
          color: AppColors.primaryOrange,
          iconColor: Colors.white,
          onPressed: () {
            if (recordingController
                    .audioRecordingService.recordingState.value ==
                RecordingState.recording) {
              recordingController.pauseRecording();
            } else if (recordingController
                    .audioRecordingService.recordingState.value ==
                RecordingState.paused) {
              recordingController.resumeRecording();
            }
          },
        ),
        const SizedBox(width: 20),
        // Stop Button
        buildCircularIconButton(
          icon: Icons.stop,
          color: AppColors.extraLightGray,
          iconColor: AppColors.blackGray,
          onPressed: () {
            recordingController.stopRecording(true);
          },
        ),
      ],
    );
  }

  Widget _buildInitialRecordingView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: ReusableButton(
        title: "Start Recording",
        onTap: () async => await recordingController.startRecording(),
      ),
    );
  }
}
