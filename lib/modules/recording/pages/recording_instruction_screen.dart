import 'package:SAiWELL/modules/recording/controller/instruction_controller.dart';
import 'package:SAiWELL/modules/recording/pages/recording_screen.dart';
import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RecordingInstructionScreen extends GetView<InstructionController> {
  const RecordingInstructionScreen({super.key});

  static const routeName = "/recordingInstructionScreen";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "Recording"),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 28,
            ),
            Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.accentTeal,
                  borderRadius: BorderRadius.circular(
                    1,
                  ),
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 20,
                    ),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Record Your Speech",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          SizedBox(
                            height: 8,
                          ),
                          Text(
                            "Create a clear and noise-free voice recording for optimal health assessment.",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 24,
                    ),
                    Image.asset(
                      AppImages.imgFemale,
                      height: 134,
                    ),
                  ],
                )),
            const SizedBox(
              height: 36,
            ),
            const Text(
              "Please follow instruction:",
              style: TextStyle(
                color: Colors.black,
                fontSize: 18,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(
              height: 8,
            ),

            Obx(
              () => ListView.separated(
                  itemCount: controller.instructionTexts.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return Text(
                      "${index + 1}. ${controller.instructionTexts[index]}",
                      style: TextStyle(
                        color: AppColors.mediumGray,
                        height: 1.6,
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(
                      height: 8,
                    );
                  }),
            ),

            const SizedBox(
              height: 28,
            ),
            ReusableButton(
                title: "Continue",
                onTap: () {
                  Get.toNamed(RecordingScreen.routeName);
                }),
          ],
        ),
      ),
    );
  }
}
