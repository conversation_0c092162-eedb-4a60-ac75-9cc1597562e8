import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:get/get.dart';

class InstructionController extends GetxController {
  final RxList<dynamic> instructionTexts = [].obs;
  final FirebaseRemoteConfigService _firebaseRemoteConfigService =
      FirebaseRemoteConfigService();
  bool isFromGtPlus = false;

  @override
  void onInit() {
    super.onInit();
    isFromGtPlus = Get.arguments?["isFromGtPlus"] ?? false;
  }

  @override
  void onReady() {
    setInstructionList();
    super.onReady();
  }

  void setInstructionList() {
    instructionTexts.value =
        _firebaseRemoteConfigService.getVoiceInstructionsFromConfig();
    instructionTexts.refresh();
  }
}
