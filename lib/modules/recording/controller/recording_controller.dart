import 'dart:async';

import 'package:SAiWELL/services/analytics/events.dart';
import 'package:SAiWELL/services/api_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:record/record.dart';

import '../../../services/audio_service.dart';
import '../../../services/firebase_remote_config_service.dart';
import '../pages/playback_screen.dart';

class RecordingController extends GetxController {
  final AudioRecordingService audioRecordingService = AudioRecordingService();
  FirebaseRemoteConfigService firebaseRemoteConfigService =
      FirebaseRemoteConfigService();

  final RxString recordingDurationObservable = RxString('00:00');
  final Rxn<String> recordedFilePathObservable = Rxn<String>();
  RxString voiceContent = "".obs;
  RxBool isStoryLoading = true.obs;

  // Getters
  String get recordingDuration => recordingDurationObservable.value;
  String? get recordedFilePath => recordedFilePathObservable.value;
  bool get isRecording =>
      audioRecordingService.recordingState.value == RecordingState.recording;
  AudioRecorder get audioRecorder => audioRecordingService.audioRecorder;

  @override
  void onInit() {
    super.onInit();
    _resetAllStatesToInitial();
    _setupListeners();
    _getVoiceContent();
  }

  Future<void> _getVoiceContent() async {
    isStoryLoading.value = true;
    voiceContent.value = await BackendApi.getStory() ?? "";
    if (voiceContent.value == "") {
      voiceContent.value = firebaseRemoteConfigService.getVoiceContentString();
    }
    isStoryLoading.value = false;
  }

  void _resetAllStatesToInitial() {
    audioRecordingService.recordingState.value = RecordingState.initial;
    recordingDurationObservable.value = '00:00';
    recordedFilePathObservable.value = null;
    voiceContent.value = "";
    isStoryLoading.value = true;
    try {
      audioRecordingService.reset();
    } catch (e) {
      _handleError('Initialization', 'Failed to reset audio service: $e');
    }
  }

  void _setupListeners() {
    // Listen to recording duration changes
    audioRecordingService.recordingDuration.addListener(() {
      recordingDurationObservable.value = audioRecordingService
          .formatDuration(audioRecordingService.recordingDuration.value);
    });
  }

  Future<bool> startRecording() async {
    try {
      LogEvents.logRecordSpeechEvent();
      
      // Check microphone permissions first - BEFORE resetting state
      bool hasPermission =
          await audioRecordingService.checkMicrophonePermission();
      
      if (!hasPermission) {
        // Permission was denied, show a message and return without trying to start recording
        _handleError('Permission',
            'Microphone permission is required to start recording');
        return false;
      }
      
      // Reset only after permissions are confirmed
      resetControllerState();
      
      bool result = await audioRecordingService.startRecording();
      
      if (!result) {
        _handleError('Recording start', 'Failed to start recording');
        return false;
      }
      
      return true;
    } catch (e) {
      _handleError('Recording start', e.toString());
      return false;
    }
  }

  Future<String?> pauseRecording() async {
    try {
      return await audioRecordingService.pauseRecording();
    } catch (e) {
      _handleError('Recording pause', e.toString());
      return null;
    }
  }

  Future<String?> resumeRecording() async {
    try {
      return await audioRecordingService.resumeRecording();
    } catch (e) {
      _handleError('Recording resume', e.toString());
      return null;
    }
  }

  Future<void> stopRecording(bool shouldNavigate) async {
    try {
      final recordedPath = await audioRecordingService.stopRecording();
      if (recordedPath != null && shouldNavigate) {
        recordedFilePathObservable.value = recordedPath;
        Get.toNamed(PlaybackScreen.routeName);
      }
      audioRecordingService.recordingState.value = RecordingState.initial;
    } catch (e) {
      _handleError('Recording stop', e.toString());
    }
  }

  void resetRecording() {
    recordedFilePathObservable.value = null;
    recordingDurationObservable.value = '00:00';
    audioRecordingService.recordingState.value = RecordingState.initial;
    audioRecordingService.resetRecording();
  }

  void resetControllerState() {
    audioRecordingService.recordingState.value = RecordingState.initial;
    recordingDurationObservable.value = '00:00';
    recordedFilePathObservable.value = null;
    
    try {
      audioRecordingService.resetRecording();
    } catch (e) {
      debugPrint('Error in resetRecording: $e');
    }
    
    if (audioRecordingService.recordingState.value ==
            RecordingState.recording ||
        audioRecordingService.recordingState.value == RecordingState.paused) {
      try {
        audioRecorder.stop();
      } catch (e) {
        debugPrint('Error stopping recorder: $e');
      }
    }
  }

  void _handleError(String context, String error) {
    debugPrint('$context error: $error');
    Get.snackbar(
      "$context Error",
      error,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
