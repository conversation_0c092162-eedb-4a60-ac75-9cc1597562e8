import 'package:SAiWELL/modules/recording/controller/instruction_controller.dart';
import 'package:SAiWELL/modules/recording/controller/playback_controller.dart';
import 'package:get/get_instance/src/bindings_interface.dart';

import '../controller/recording_controller.dart';

class InstructionBinding {
  static List<Bindings> binding = [
    BindingsBuilder.put(() => InstructionController()),
  ];
}

class RecordingBinding {
  static List<Bindings> binding = [
    BindingsBuilder.put(() => RecordingController()),
  ];
}

class PlaybackBinding {
  static List<Bindings> binding = [
    BindingsBuilder.put(() => InstructionController()),
    BindingsBuilder.put(() => RecordingController()),
    BindingsBuilder.put(() => PlaybackController()),
  ];
}
