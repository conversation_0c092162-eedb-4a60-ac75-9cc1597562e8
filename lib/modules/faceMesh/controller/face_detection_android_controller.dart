import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:camera/camera.dart';
import 'package:google_mlkit_face_mesh_detection/google_mlkit_face_mesh_detection.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import '../../../services/prefs_service.dart';
import '../../../utils/dialogs/success_recording_dialog.dart';
import '../../../utils/reusableWidgets/reusable_dialog.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/const/app_colors.dart';
import '../../home/<USER>';
import '../../home/<USER>/home_controller.dart';
import 'package:SAiWELL/services/gcs_upload_service.dart';

// Helper class to return face position and dimensions together
class FacePositionResult {
  final Point<double>? position;
  final double width;
  final double height;

  FacePositionResult(this.position, this.width, this.height);
}

class FaceMeshAndroidController extends GetxController {
  CameraController? cameraController;
  final faceMeshDetector =
      FaceMeshDetector(option: FaceMeshDetectorOptions.faceMesh);
  final faceDetector = FaceDetector(
    options: FaceDetectorOptions(
      enableClassification: false,
      enableLandmarks: false,
      enableContours: false,
      enableTracking: false,
    ),
  );
  FaceMesh? latestFaceMesh;
  Face? latestFace;
  final PrefsService prefsService = PrefsService();

  final RxBool isProcessing = false.obs;
  final RxBool isDataUploading = false.obs;
  final RxBool showInstructions = true.obs;
  final RxString currentHint =
      RxString('Please position your face in the frame');
  final RxBool canCapture = true.obs;
  final RxBool showRetryPopup = false.obs;
  final RxBool isProcessingStopped = false.obs;

  // Face detection
  final RxBool isFaceDetected = false.obs;
  final RxBool showManualButton = true.obs;

  Timer? _processingTimer;
  Timer? _faceDetectionTimer;
  Timer? _noFaceDetectionTimer;
  bool _isProcessingImage = false;

  DateTime? _faceFirstDetectedTime;
  Point<double>? _lastFacePosition;
  // Maximum allowed normalized movement (as a percentage of face size)
  static const _maxMovementThreshold = 0.05;
  static const _requiredDetectionDuration = 3;
  static const _noFaceDetectionThreshold = 5;

  // Face dimensions for normalizing movement
  double _lastFaceWidth = 0;
  double _lastFaceHeight = 0;

  List<CameraDescription> cameras = [];
  bool isCameraInitialized = false;
  Size? previewSize;

  // Track recent movement measurements to smooth detection
  final List<double> _recentMovements = [];
  static const int _movementHistorySize = 7; // Increased for better averaging

  @override
  void onInit() {
    super.onInit();
    canCapture.value = true;
    showManualButton.value = false;
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      cameras = await availableCameras();

      if (cameras.isNotEmpty) {
        CameraDescription frontCamera = cameras.firstWhere(
          (camera) => camera.lensDirection == CameraLensDirection.front,
          orElse: () => cameras.first,
        );

        cameraController = CameraController(
          frontCamera,
          ResolutionPreset.high,
          enableAudio: false,
          imageFormatGroup: ImageFormatGroup.jpeg,
        );

        await cameraController!.initialize();
        previewSize = Size(
          cameraController!.value.previewSize!.height,
          cameraController!.value.previewSize!.width,
        );

        isCameraInitialized = true;
        update();

        // Start periodic face detection with a slightly longer interval
        _processingTimer =
            Timer.periodic(const Duration(milliseconds: 800), (_) {
          if (!_isProcessingImage &&
              !isProcessingStopped.value &&
              !isProcessing.value) {
            _captureAndProcessImage();
          }
        });
      }
    } catch (e) {
      print('ERROR initializing camera: $e');
    }
  }

  Future<void> _captureAndProcessImage() async {
    if (_isProcessingImage ||
        isProcessingStopped.value ||
        isProcessing.value ||
        !canCapture.value) {
      return;
    }

    _isProcessingImage = true;
    try {
      final pictureFile = await takePicture();
      if (pictureFile != null) {
        // Check if the file actually exists and has content
        final file = File(pictureFile.path);
        if (!await file.exists()) {
          return;
        }

        final fileSize = await file.length();
        if (fileSize == 0) {
          return;
        }

        // Add a small delay to ensure the file is fully written
        await Future.delayed(const Duration(milliseconds: 100));

        // Create input image with a try-catch
        InputImage? inputImage;
        try {
          inputImage = InputImage.fromFilePath(pictureFile.path);
        } catch (e) {
          print("Failed to create InputImage: $e");
          return;
        }

        try {
          // Process with both face mesh detector and face detector
          List<FaceMesh> meshes = [];
          List<Face> faces = [];

          try {
            // Process face mesh
            meshes = await faceMeshDetector.processImage(inputImage);

            // Process face detection for head pose angles
            faces = await faceDetector.processImage(inputImage);
          } catch (e) {
            print("ML Kit detection error: $e");
            return;
          }

          if (meshes.isNotEmpty) {
            latestFaceMesh = meshes.first;

            // Store face data if available (for head pose angles)
            if (faces.isNotEmpty) {
              latestFace = faces.first;
            }

            isFaceDetected.value = true;

            // Get face position and dimensions
            final facePositionResult = _getFacePosition();
            if (facePositionResult == null) {
              return;
            }

            final currentPosition = facePositionResult.position;
            final faceWidth = facePositionResult.width;
            final faceHeight = facePositionResult.height;

            // Check if face is stable for auto-capture
            if (currentPosition != null) {
              if (_lastFacePosition == null) {
                _lastFacePosition = currentPosition;
                _lastFaceWidth = faceWidth;
                _lastFaceHeight = faceHeight;
                _faceFirstDetectedTime = DateTime.now();
                updateHint('Hold still...');
                _recentMovements.clear();
              } else {
                // Calculate normalized movement - divide by average face dimension
                double averageFaceDimension = (_lastFaceWidth + _lastFaceHeight) / 2;
                if (averageFaceDimension < 1) {
                  // Avoid division by zero or very small values
                  averageFaceDimension = 100;
                }

                // Calculate absolute distance
                final absoluteDistance = sqrt(
                    pow(currentPosition.x - _lastFacePosition!.x, 2) +
                    pow(currentPosition.y - _lastFacePosition!.y, 2));

                // Normalize by face size to get a scale-invariant measurement
                final normalizedDistance = absoluteDistance / averageFaceDimension;

                // Add to recent movements for smoothing with exponential decay
                _recentMovements.add(normalizedDistance);
                if (_recentMovements.length > _movementHistorySize) {
                  _recentMovements.removeAt(0);
                }

                // Skip movement check if we have too few samples
                if (_recentMovements.length < 3) {
                  updateHint('Hold still...');

                  // Update last position with gentle smoothing
                  _lastFacePosition = Point(
                    _lastFacePosition!.x * 0.8 + currentPosition.x * 0.2,
                    _lastFacePosition!.y * 0.8 + currentPosition.y * 0.2
                  );
                  _lastFaceWidth = _lastFaceWidth * 0.8 + faceWidth * 0.2;
                  _lastFaceHeight = _lastFaceHeight * 0.8 + faceHeight * 0.2;
                  return;
                }

                // Calculate weighted average movement over recent frames
                double totalWeight = 0;
                double weightedSum = 0;

                for (int i = 0; i < _recentMovements.length; i++) {
                  // Apply exponential weighting - newer movements count more
                  double weight = pow(1.3, i).toDouble(); // Reduced weight factor
                  weightedSum += _recentMovements[i] * weight;
                  totalWeight += weight;
                }

                double avgMovement = _recentMovements.isEmpty ? 0 : weightedSum / totalWeight;

                if (avgMovement > _maxMovementThreshold) {
                  // Reset if moved too much
                  _resetAutoCapture();
                  updateHint('Face moved, hold still...');
                } else {
                  // Check if face has been stable for required time
                  final stableTime = DateTime.now()
                      .difference(_faceFirstDetectedTime!)
                      .inSeconds;

                  if (stableTime >= _requiredDetectionDuration) {
                    if (canCapture.value) {
                      _triggerAutoCapture();
                    }
                  } else {
                    updateHint('Hold still for ${_requiredDetectionDuration - stableTime} more seconds');
                  }
                }

                // Update last position with more aggressive smoothing (90% previous, 10% new)
                _lastFacePosition = Point(
                  _lastFacePosition!.x * 0.9 + currentPosition.x * 0.1,
                  _lastFacePosition!.y * 0.9 + currentPosition.y * 0.1
                );
                _lastFaceWidth = _lastFaceWidth * 0.9 + faceWidth * 0.1;
                _lastFaceHeight = _lastFaceHeight * 0.9 + faceHeight * 0.1;
              }

              // Cancel no face detection timer if it was active
              _noFaceDetectionTimer?.cancel();
            }
          } else {
            isFaceDetected.value = false;
            _resetAutoCapture();
            updateHint('Please position your face in the frame');

            // Start timer to show retry popup if no face detected for a while
            _noFaceDetectionTimer?.cancel();
            _noFaceDetectionTimer = Timer(Duration(seconds: _noFaceDetectionThreshold), () {
              if (!isFaceDetected.value && !isProcessingStopped.value) {
                showRetryPopup.value = true;
                updateHint('No face detected');
              }
            });
          }
        } catch (e) {
          print('Face mesh detector error: $e');
        }

        try {
          await File(pictureFile.path).delete();
        } catch (e) {
          print("Failed to delete temporary file: $e");
        }
      }
    } catch (e) {
      print('Critical error in face detection: $e');
    } finally {
      _isProcessingImage = false;
    }
  }

  FacePositionResult? _getFacePosition() {
    try {
      if (latestFaceMesh == null || latestFaceMesh!.points.isEmpty) {
        return null;
      }

      // First calculate the face bounding box once for all points
      double minX = double.infinity;
      double maxX = double.negativeInfinity;
      double minY = double.infinity;
      double maxY = double.negativeInfinity;

      for (final p in latestFaceMesh!.points) {
        if (p.x < minX) minX = p.x;
        if (p.x > maxX) maxX = p.x;
        if (p.y < minY) minY = p.y;
        if (p.y > maxY) maxY = p.y;
      }

      // Calculate center of face bounding box
      double centerX = (minX + maxX) / 2;
      double centerY = (minY + maxY) / 2;
      double faceWidth = maxX - minX;
      double faceHeight = maxY - minY;

      // For extreme cases, use center point directly if dimensions are strange
      if (faceWidth < 0.1 || faceHeight < 0.1) {
        return FacePositionResult(Point(centerX, centerY), 1.0, 1.0);
      }

      // Use only a smaller subset of more stable face points (primarily center face points)
      // This makes position tracking more stable by ignoring outer face points that move more
      final stablePoints = <FaceMeshPoint>[];
      double sumX = 0;
      double sumY = 0;

      // Filter points to use only those near the center of the face
      for (final point in latestFaceMesh!.points) {
        // Only use points near the center (50% of the face width/height from center)
        if ((point.x - centerX).abs() < faceWidth * 0.5 &&
            (point.y - centerY).abs() < faceHeight * 0.5) {
          stablePoints.add(point);
          sumX += point.x;
          sumY += point.y;
        }

        // If we have enough points, stop collecting more
        if (stablePoints.length >= 50) break; // Increased for better stability
      }

      // If we don't have enough stable points, fall back to using all points
      if (stablePoints.isEmpty) {
        for (final point in latestFaceMesh!.points) {
          sumX += point.x;
          sumY += point.y;
        }

        Point<double> avgPoint = Point(
          sumX / latestFaceMesh!.points.length,
          sumY / latestFaceMesh!.points.length
        );

        return FacePositionResult(avgPoint, faceWidth, faceHeight);
      }

      // Return average of stable points along with face dimensions
      Point<double> avgPoint = Point(
        sumX / stablePoints.length,
        sumY / stablePoints.length
      );

      return FacePositionResult(avgPoint, faceWidth, faceHeight);
    } catch (e) {
      print("Error in _getFacePosition: $e");
      return null;
    }
  }

  void _resetAutoCapture() {
    _faceFirstDetectedTime = null;
    _lastFacePosition = null;
    _lastFaceWidth = 0;
    _lastFaceHeight = 0;
    _faceDetectionTimer?.cancel();
    _recentMovements.clear();
  }

  void _triggerAutoCapture() async {
    if (!canCapture.value) return;
    _resetAutoCapture();
    updateHint('Capturing...');

    final success = await captureFaceMeshData(Get.context!);
    if (success) {
      updateHint('Capture successful!');
      canCapture.value = false; // Disable further auto-captures
    } else {
      updateHint('Capture failed, please try again');
    }
  }

  Future<XFile?> takePicture() async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      return null;
    }

    try {
      final image = await cameraController!.takePicture();
      return image;
    } catch (e) {
      print('Error taking picture: $e');
      return null;
    }
  }

  @override
  void onClose() {
    _cleanUpResources();
    super.onClose();
  }

  void _cleanUpResources() {
    _processingTimer?.cancel();
    _faceDetectionTimer?.cancel();
    _noFaceDetectionTimer?.cancel();

    // Ensure camera is properly released
    if (cameraController != null) {
      try {
        if (cameraController!.value.isStreamingImages) {
          cameraController!.stopImageStream();
        }
        cameraController!.dispose();
      } catch (e) {
        print('Error disposing camera controller: $e');
      }
      cameraController = null;
    }

    // Close ML Kit detectors
    faceMeshDetector.close();
    faceDetector.close();

    // Clear data
    _lastFacePosition = null;
    _faceFirstDetectedTime = null;
  }

  void updateHint(String newHint) {
    if (currentHint.value != newHint) {
      currentHint.value = newHint;
    }
  }

  void onContinuePressed() {
    showInstructions.value = false;
  }

  Future<bool> captureFaceMeshData(BuildContext context) async {
    if (latestFaceMesh == null || isProcessing.value) return false;
    isProcessing.value = true;

    try {
      // Reset auto-capture state
      _resetAutoCapture();
      showManualButton.value = true;

      final faceData = _extractFacePoints();
      if (faceData == null) {
        return false;
      }

      final success = await _uploadFaceData(faceData, context);
      return success;
    } finally {
      isProcessing.value = false;
    }
  }

  String? _extractFacePoints() {
    if (latestFaceMesh == null) return null;

    final points = latestFaceMesh!.points.map((p) => [p.x, p.y, p.z]).expand((e) => e).toList();
    final contours = <String, List<List<double>>>{};

    // Extract face contours
    latestFaceMesh!.contours.forEach((key, contourPoints) {
      if (contourPoints != null && contourPoints.isNotEmpty) {
        contours[key.toString()] = contourPoints.map((p) => [p.x, p.y, p.z]).toList();
      }
    });

    // Extract head pose angles if available
    Map<String, double>? eulerAngles;
    if (latestFace != null) {
      eulerAngles = {
        'yaw': latestFace!.headEulerAngleY ?? 0.0,
        'pitch': latestFace!.headEulerAngleX ?? 0.0,
        'roll': latestFace!.headEulerAngleZ ?? 0.0,
      };
    }

    final jsonMap = <String, dynamic>{
      'nodeName': 'faceMesh',
      'identifier': DateTime.now().millisecondsSinceEpoch.toString(),
      'isTracked': true,
      'points': points,
      'contours': contours,
    };

    // Add euler angles if available
    if (eulerAngles != null) {
      jsonMap['eulerAngles'] = eulerAngles;
    }

    return jsonEncode(jsonMap);
  }

  Future<bool> _uploadFaceData(String faceDataJson, BuildContext context) async {
    isDataUploading.value = true;

    try {
      // Parse the face data
      Map<String, dynamic> parsedData = jsonDecode(faceDataJson);

      // Generate timestamp for the filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final timestampStr = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());

      // Get temporary directory for saving files
      final tempDir = await getTemporaryDirectory();
      final faceMeshDataDir = Directory('${tempDir.path}/faceMesh');
      if (!faceMeshDataDir.existsSync()) {
        faceMeshDataDir.createSync();
      }

      // Save face data to CSV files (both geometry and facedata like iOS)
      final geometryFilePath = '${faceMeshDataDir.path}/facemesh_geometry_$timestamp.csv';
      final faceDataFilePath = '${faceMeshDataDir.path}/facemesh_facedata_$timestamp.csv';

      final geometryFile = File(geometryFilePath);
      final faceDataFile = File(faceDataFilePath);

      final String geometryCsv = _convertPointsToCsv(parsedData, timestampStr);
      final String faceDataCsv = _convertFaceDataToCsv(parsedData, timestampStr);

      await geometryFile.writeAsString(geometryCsv);
      await faceDataFile.writeAsString(faceDataCsv);

      // Get user ID
      String uid = await prefsService.getUid();



      // Upload both files to GCS (similar to iOS implementation)
      bool geometrySuccess = await _uploadFaceMeshDataToGCS(
        csvData: geometryCsv,
        uid: uid,
        type: 'geometry',
        timestamp: timestamp,
        context: context,
      );

      bool faceDataSuccess = await _uploadFaceMeshDataToGCS(
        csvData: faceDataCsv,
        uid: uid,
        type: 'facedata',
        timestamp: timestamp,
        context: context,
      );

      bool uploadSuccess = geometrySuccess && faceDataSuccess;

      if (uploadSuccess) {
        print("Upload success: $uid/face_scan_$timestamp");

        // Clean up resources before navigating away
        _cleanUpResources();

        // Show success dialog using the reusable dialog component
        Get.back();
        successfullyUploadedDialog(
          context: context,
          message: "Your Face data has been uploaded successfully!",
          onTap: () async {
            ReusableDialog.close();
            Get.until((route) => route.settings.name == HomeScreen.routeName);

            // Reload webview if coming from GT-Plus
            try {
              final homeController = Get.find<HomeController>();
              homeController.reloadWebViewFromGTPlus();
            } catch (e) {
              debugPrint('Error accessing HomeController: $e');
            }
          },
        );

        return true;
      } else {
        print("Upload failed");
        return false;
      }
    } catch (e) {
      print('Error uploading face data: $e');

      // Use ReusableDialog for error dialog too for consistency
      ReusableDialog.show(
        isDismissible: true,
        borderRadius: BorderRadius.circular(8),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 36,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 20),
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 18),
            Text(
              'Upload Failed',
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 22,
                color: AppColors.primaryTeal,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Unable to upload face data. Please try again later.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ReusableButton(
              title: "OK",
              onTap: () => ReusableDialog.close(),
            ),
            const SizedBox(height: 16),
          ],
        ),
      );

      return false;
    } finally {
      isDataUploading.value = false;
    }
  }

  Future<bool> _uploadFaceMeshDataToGCS({
    required String csvData,
    required String uid,
    required String type,
    required int timestamp,
    required BuildContext context,
  }) async {
    final success = await GcsUploadService.uploadToGcs(
      bytes: utf8.encode(csvData),
      uid: uid,
      type: type,
      timestamp: timestamp,
      contentType: 'text/csv',
      extension: 'csv', // Keep extension - buildGcsPath will handle double extension
      credentialsAssetPath: 'assets/gCloud/credentials.json',
    );
    if (!success) {
      print("Upload failed for $type");
      return false;
    }
    return true;
  }

  String _convertPointsToCsv(Map<String, dynamic> parsedData, String timestamp) {
    final List<String> headers = ['timestamp'];
    final List<dynamic> points = parsedData['points'] as List<dynamic>;
    final int pointCount = points.length ~/ 3;

    for (int i = 0; i < pointCount; i++) {
      headers.addAll(['point_${i}_x', 'point_${i}_y', 'point_${i}_z']);
    }

    final List<String> values = [timestamp];
    values.addAll(points.map((v) => v.toString()));

    return '${headers.join(',')}\n${values.join(',')}\n';
  }

  String _convertFaceDataToCsv(Map<String, dynamic> parsedData, String timestamp) {
    final List<String> headers = [];
    final List<String> values = [];

    headers.addAll(['timestamp', 'nodeName', 'identifier', 'isTracked']);
    values.addAll([
      timestamp,
      parsedData['nodeName'].toString(),
      parsedData['identifier'].toString(),
      parsedData['isTracked'].toString()
    ]);

    // Add Euler angles if available
    if (parsedData.containsKey('eulerAngles')) {
      final eulerAngles = parsedData['eulerAngles'] as Map<String, dynamic>;
      headers.addAll(['yaw', 'pitch', 'roll']);
      values.addAll([
        eulerAngles['yaw'].toString(),
        eulerAngles['pitch'].toString(),
        eulerAngles['roll'].toString()
      ]);
    }

    return '${headers.join(',')}\n${values.join(',')}\n';
  }

  void captureAndSubmit() {
    captureFaceMeshData(Get.context!);
  }

  Future<bool> isARKitSupported() async {
    return false;
  }
}
