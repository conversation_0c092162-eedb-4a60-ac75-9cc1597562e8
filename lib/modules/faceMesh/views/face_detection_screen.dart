import 'dart:io';
import 'package:arkit_plugin/arkit_plugin.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../../../utils/permission_handler_util.dart';
import '../controller/face_detection_ios_controller.dart' as ios;
import '../controller/face_detection_android_controller.dart' as android;
import '../../../utils/const/app_colors.dart';

class FaceDetectionScreen extends StatefulWidget {
  const FaceDetectionScreen({super.key});
  static const routeName = "/faceDetectionScreen";

  @override
  State<FaceDetectionScreen> createState() => _FaceDetectionScreenState();
}

class _FaceDetectionScreenState extends State<FaceDetectionScreen> {
  late final dynamic controller;
  bool isIOS = Platform.isIOS;

  @override
  void initState() {
    super.initState();
    controller = isIOS
        ? Get.find<ios.FaceMeshIosController>()
        : Get.find<android.FaceMeshAndroidController>();
    _checkCameraPermission();
  }

  Future<void> _checkCameraPermission() async {
    final permissionHandler = PermissionHandlerUtil();
    final hasPermission = await permissionHandler.checkCameraPermission();
    if (!hasPermission) {
      Get.back(); // Go back if permission denied
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Face Detection')),
      body: FutureBuilder<bool>(
        future: controller.isARKitSupported(),
        builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final bool isSupported = snapshot.data ?? false;

          // If the device is iOS but doesn't support ARKit
          if (isIOS && !isSupported) {
            return _buildUnsupportedWidget();
          }

          return Obx(() {
            if (controller.showInstructions.value) {
              return _buildInstructionsPopup();
            } else {
              return _buildFaceDetectionView();
            }
          });
        },
      ),
    );
  }

  Widget _buildFaceDetectionView() {
    return Stack(
      children: [
        // Platform specific camera view
        isIOS ? _buildARKitView() : _buildCameraView(),

        // Loading indicator
        Obx(() => controller.isProcessing.value
            ? const Center(child: CircularProgressIndicator())
            : const SizedBox()),
        // Hints overlay
        _buildHintsOverlay(),
      ],
    );
  }

  Widget _buildARKitView() {
    return ARKitSceneView(
      configuration: ARKitConfiguration.faceTracking,
      onARKitViewCreated: controller.onARKitViewCreated,
    );
  }

  Widget _buildCameraView() {
    final androidController = controller as android.FaceMeshAndroidController;
    final scale = 1 /
        (controller.cameraController.value.aspectRatio *
            MediaQuery.of(context).size.aspectRatio);
    return GetBuilder<android.FaceMeshAndroidController>(
        init: androidController,
        builder: (controller) {
          return controller.isCameraInitialized
              ? Transform.scale(
                  scale: scale,
                  alignment: Alignment.topCenter,
                  child: CameraPreview(controller.cameraController!),
                )
              : const Center(child: CircularProgressIndicator());
        });
  }

  Widget _buildInstructionsPopup() {
    return Container(
      color: Colors.black.withOpacity(0.9),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Instructions',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Hold your phone in front of your face,\nkeep a neutral expression, and tap Continue.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 24),
              ReusableButton(
                width: 128,
                height: 45,
                title: "Continue",
                onTap: controller.onContinuePressed,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUnsupportedWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Device Not Supported',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'ARKit face tracking requires an iOS device with A9 or later processor (iPhone 6s or newer) running iOS 11+.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            OutlinedButton(
              onPressed: () => Get.back(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHintsOverlay() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Obx(() => AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: Text(
              controller.currentHint.value,
              key: ValueKey(controller.currentHint.value),
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    blurRadius: 4.0,
                    color: Colors.black,
                    offset: Offset(2.0, 2.0),
                  ),
                ],
              ),
            ),
          )),
    );
  }
}
