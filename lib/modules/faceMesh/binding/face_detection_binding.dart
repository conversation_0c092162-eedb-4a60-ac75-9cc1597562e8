import 'dart:io';
import 'package:SAiWELL/modules/faceMesh/controller/face_detection_ios_controller.dart' as ios;
import 'package:SAiWELL/modules/faceMesh/controller/face_detection_android_controller.dart' as android;
import 'package:get/get.dart';

class FaceDetectionBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      if (Platform.isIOS) {
        Get.put(ios.FaceMeshIosController());
      } else {
        Get.put(android.FaceMeshAndroidController());
      }
    }),
  ];
}
