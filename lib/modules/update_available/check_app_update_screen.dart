import 'dart:io';
import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/modules/home/<USER>';
import 'package:SAiWELL/modules/update_available/package_info.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:SAiWELL/utils/const/app_strings.dart';
import 'package:SAiWELL/utils/dimensions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:open_store/open_store.dart';
import 'package:flutter/foundation.dart';

class CheckAppUpdateScreen extends StatefulWidget {
  static String routeName = "/checkAppUpdateScreen";
  const CheckAppUpdateScreen({super.key});

  @override
  State<CheckAppUpdateScreen> createState() => _CheckAppUpdateScreenState();
}

class _CheckAppUpdateScreenState extends State<CheckAppUpdateScreen> {
  final packageInfo = PackageInfoSetup();
  final firebaseRemoteConfigService = FirebaseRemoteConfigService();
  bool _showUpdateDialog = false;
  @override
  void initState() {
    setup();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_showUpdateDialog) {
        _showUpdateVersionDialog(context);
      } else {
        _navigateToNextPage();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(),
    );
  }

  int _getExtendedVersionNumber(String version) {
    List versionCells = version.split('.');
    versionCells = versionCells.map((i) => int.parse(i)).toList();
    return versionCells[0] * 100000 + versionCells[1] * 1000 + versionCells[2];
  }

  Future<void> _showUpdateVersionDialog(BuildContext context) async {
    WidgetStateColor materialLoginThemeColor =
        WidgetStateColor.resolveWith((states) => AppColors.darkGray);

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        Dimensions dimensions = Dimensions(context);
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 62 / 896 * dimensions.height,
                  width: 62 / 414 * dimensions.width,
                  child: Image.asset(AppImages.appLogoImage),
                ),
                SizedBox(
                  height: 16 / 896 * dimensions.height,
                ),
                Text(
                  packageInfo.appName,
                  // style: poppinsTextStyle(20 / 414 * dimensions.width,
                  //     colorGrey800, FontWeight.w600),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: ListBody(
                children: <Widget>[
                  Text(
                    checkUpdateScreenText['text1']!,
                    // style: poppinsTextStyle(16 / 414 * dimensions.width,
                    //     colorGrey600, FontWeight.w500),
                  ),
                  SizedBox(
                    height: 4 / 896 * dimensions.height,
                  ),
                  ...getBulletPointSteps(dimensions),
                ],
              ),
            ),
            actions: <Widget>[
              Center(
                child: SizedBox(
                  width: 100 / 414 * dimensions.width,
                  child: TextButton(
                    style: ButtonStyle(
                        backgroundColor: materialLoginThemeColor,
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              40 / 414 * dimensions.width),
                        ))),
                    child: Text(
                      checkUpdateScreenText['text3']!,

                     //  style: poppinsTextStyle(16 / 414 * dimensions.width,
                      //     colorWhite, FontWeight.w400),
                    ),
                    onPressed: () {
                      _launchAppOrPlayStore();
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future setup() async {
    final appVersion = _getExtendedVersionNumber(packageInfo.version);
    final requiredMinVersion = _getExtendedVersionNumber(
        Platform.isAndroid
            ? firebaseRemoteConfigService.getAndroidMinRequiredVersion()
            : firebaseRemoteConfigService.getMinRequiredVersion());
    debugPrint("------requiredMinVersion : $requiredMinVersion");
    if (appVersion < requiredMinVersion) {
      _showUpdateDialog = true;
    }
  }

  void _launchAppOrPlayStore() {
    OpenStore.instance.open(
      appStoreId: iosAppId,
      androidAppBundleId: packageInfo.packageName,
    );
  }

  void _navigateToNextPage() {
    Get.offAllNamed(
      HomeScreen.routeName,
    );
  }

  List getBulletPointSteps(Dimensions dimensions) {
    List<String> texts = [
      checkUpdateScreenText['text4']!,
      checkUpdateScreenText['text5']!,
      checkUpdateScreenText['text6']!,
    ];
    List<Widget> steps = [];
    for (String text in texts) {
      steps.add(Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            checkUpdateScreenText['text7']!,
            // style: poppinsTextStyle(
            //     12 / 414 * dimensions.width, colorGrey400, FontWeight.w400),
          ),
          SizedBox(
            width: 244 / 414 * dimensions.width,
            child: Text(
              text,
              overflow: TextOverflow.fade,
              // style: poppinsTextStyle(
              //     12 / 414 * dimensions.width, colorGrey400, FontWeight.w400),
            ),
          ),
        ],
      ));
      steps.add(SizedBox(
        height: 4 / 896 * dimensions.height,
      ));
    }

    return steps;
  }
}
