import 'package:SAiWELL/services/firebase_remote_config_service.dart';

enum CaptureType {
  face,
  faceMesh,
  lips,
  teeth,
  tongue,
  palm,
  nails,
}

class InstructionConfig {
  final String title;
  final List<String> bulletPoints;
  final bool useFrontCamera;
  final String imageAsset;

  InstructionConfig({
    required this.title,
    required this.bulletPoints,
    required this.useFrontCamera,
    required this.imageAsset,
  });

  static Future<InstructionConfig> getConfig(CaptureType type) async {
    FirebaseRemoteConfigService firebaseRemoteConfigService =
        FirebaseRemoteConfigService();
    final configs =
        firebaseRemoteConfigService.instructionConfigs['instruction_configs'];

    final configKey = type.toString().split('.').last;
    final config = configs[configKey];

    if (config == null) {
      throw Exception('Configuration not found for type: $type');
    }

    return InstructionConfig(
      title: config['title'],
      bulletPoints: List<String>.from(config['bulletPoints']),
      useFrontCamera: config['useFrontCamera'],
      imageAsset: config['imageAsset'],
    );
  }

  factory InstructionConfig.fromJson(Map<String, dynamic> json) {
    return InstructionConfig(
      title: json['title'],
      bulletPoints: List<String>.from(json['bulletPoints']),
      useFrontCamera: json['useFrontCamera'],
      imageAsset: json['imageAsset'],
    );
  }
}
