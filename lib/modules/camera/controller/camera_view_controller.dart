import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:SAiWELL/modules/home/<USER>';
import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:googleapis/storage/v1.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:path/path.dart' as path;

import '../../../services/prefs_service.dart';
import '../../../utils/dialogs/success_recording_dialog.dart';
import '../../../utils/reusableWidgets/reusable_dialog.dart';
import '../instruction_config.dart';
import '../pages/camera_preview_screen.dart';
import 'camera_manager.dart';
import 'package:SAiWELL/services/gcs_upload_service.dart';

class CameraViewController extends GetxController {
  late CaptureType captureType;
  late InstructionConfig config;
  CameraController? get cameraController => _cameraManager.cameraController;

  // Camera manager instance
  late CameraManager _cameraManager;
  CameraManager get cameraManager => _cameraManager;

  final RxBool isInitialized = false.obs;
  final RxString imagePath = ''.obs;
  final RxBool isLoading = true.obs;
  final RxBool isUsingFrontCamera = true.obs;

  // For tap-to-focus functionality
  final RxBool isFocusing = false.obs;
  final Rx<Offset> focusPoint = Offset.zero.obs;
  final Rx<Offset> relativeFocusPoint = Offset.zero.obs;

  PrefsService prefsService = PrefsService();
  HomeController homeController = Get.find<HomeController>();

  @override
  void onInit() {
    super.onInit();
    _cameraManager = CameraManager();
    initializeCamera();
  }

  @override
  void onClose() {
    _cameraManager.dispose();
    super.onClose();
  }

  Future<void> initializeCamera() async {
    try {
      isLoading.value = true;
      captureType = Get.arguments?["type"] ?? CaptureType.face;
      config = await InstructionConfig.getConfig(captureType);
      isUsingFrontCamera.value = config.useFrontCamera;

      await _cameraManager.initializeCamera(
        isFrontCamera: isUsingFrontCamera.value,
        context: Get.context!,
      );

      // Lock to portrait orientation
      await _cameraManager.cameraController
          ?.lockCaptureOrientation(DeviceOrientation.portraitUp);

      isInitialized.value = true;
      update();
    } catch (e) {
      print('Error initializing camera: $e');
      isInitialized.value = false;
    } finally {
      isLoading.value = false;
    }
  }



  Future<void> switchCamera(BuildContext context) async {
    try {
      isLoading.value = true;
      isUsingFrontCamera.value = !isUsingFrontCamera.value;

      await _cameraManager.initializeCamera(
        isFrontCamera: isUsingFrontCamera.value,
        context: context,
      );

      isInitialized.value = true;
      update();
    } catch (e) {
      print('Error switching camera: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> setFocusDirectly(Offset normalizedPosition) async {
    if (isLoading.value) return;

    // Store the original coordinates for the focus indicator UI
    relativeFocusPoint.value = normalizedPosition;

    // Use the normalized coordinates for actual camera focus
    await _cameraManager.setFocusPoint(
      normalizedPosition,
      (focusing) => isFocusing.value = focusing,
    );
  }

  Future<void> takePicture(BuildContext context) async {
    if (!isInitialized.value) return;

    try {
      isLoading.value = true;
      // Take picture using camera manager
      final XFile? photo = await _cameraManager.takePicture();
      if (photo == null) {
        throw Exception('Failed to capture image');
      }

      final String originalPath = photo.path;
      imagePath.value = "";
      
      print('Original image path: $originalPath');
      print('File extension: ${path.extension(originalPath)}');

      if ([CaptureType.teeth, CaptureType.nails, CaptureType.lips, CaptureType.tongue]
          .contains(captureType)) {
        try {
          final imageFile = File(originalPath);
          final imageBytes = await imageFile.readAsBytes();
          print('Image bytes length: ${imageBytes.length}');
          
          final image = img.decodeImage(imageBytes);
          if (image == null) {
            throw Exception('Image decoding failed');
          }

          final imageWidth = image.width;
          final imageHeight = image.height;

          // Calculate center crop based on the exact overlay size (75% of width)
          double scanAreaPercentage = 0.75; // This matches the overlay in camera_capture_screen.dart
          
          // Use the smaller dimension to ensure the crop is square and fits within the image
          double minDimension = min(imageWidth, imageHeight).toDouble();
          double cropSize = minDimension * scanAreaPercentage;

          // Ensure valid crop size
          cropSize = cropSize.clamp(0.0, minDimension).roundToDouble();

          // Center the crop in both dimensions
          int cropLeft = ((imageWidth - cropSize) / 2).round();
          int cropTop = ((imageHeight - cropSize) / 2).round();

          // Ensure valid crop area
          if (cropLeft < 0 ||
              cropTop < 0 ||
              (cropLeft + cropSize) > imageWidth ||
              (cropTop + cropSize) > imageHeight) {
            throw Exception('Invalid crop dimensions');
          }

          // Print debug information to identify any issues
          print('Image dimensions: ${imageWidth}x${imageHeight}');
          print('Crop parameters: left=$cropLeft, top=$cropTop, size=${cropSize.round()}');

          final cropRect = Rectangle<int>(cropLeft, cropTop, cropSize.round(), cropSize.round());
          print('Using Rectangle for cropping: $cropRect');
          
          // Try both crop methods to see which one works
          img.Image croppedImage;
          try {
            croppedImage = img.copyCrop(
              image,
              x: cropLeft,
              y: cropTop,
              width: cropSize.round(),
              height: cropSize.round(),
            );
          } catch (e) {
            print('Named parameter copyCrop failed: $e');
            // Fallback to another method
            croppedImage = img.copyRectify(
              image, 
              topLeft: img.Point(cropLeft.toDouble(), cropTop.toDouble()),
              topRight: img.Point((cropLeft + cropSize.round()).toDouble(), cropTop.toDouble()),
              bottomLeft: img.Point(cropLeft.toDouble(), (cropTop + cropSize.round()).toDouble()),
              bottomRight: img.Point((cropLeft + cropSize.round()).toDouble(), (cropTop + cropSize.round()).toDouble())
            );
          }

          final croppedPath = originalPath.replaceAll('.jpg', '_cropped.jpg');
          print('Saving cropped image to: $croppedPath');
          await File(croppedPath).writeAsBytes(img.encodePng(croppedImage));
          print('Cropped image saved successfully');
          imagePath.value = croppedPath;
        } catch (e) {
          print('Image cropping error: ${e.toString()}');
          // If cropping fails, use original image
          imagePath.value = originalPath;
        }
      } else {
        imagePath.value = originalPath;
      }

      Get.toNamed(CameraPreviewScreen.routeName);
    } catch (e) {
      print('Camera Error: ${e.toString()}');
      Get.snackbar(
          'Capture Failed', 'Error: ${e.toString().replaceAll('\n', ' ')}');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> uploadPhoto(BuildContext context) async {
    try {
      isLoading.value = true;
      final imageFile = File(imagePath.value);
      final bytes = await imageFile.readAsBytes();
      final extension = path.extension(imagePath.value).replaceFirst('.', '');
      final prefix = _getFilenamePrefix(captureType);
      final uid = await prefsService.getUid();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final contentType = _getContentType(extension);
      final success = await GcsUploadService.uploadToGcs(
        bytes: bytes,
        uid: uid,
        type: prefix,
        timestamp: timestamp,
        contentType: contentType,
        extension: extension,
        credentialsAssetPath: 'assets/gCloud/credentials.json',
      );
      if (!success) throw Exception('Upload failed');
      print("uploaded to GCS");
      successfullyUploadedDialog(
        context: context,
        message: "Your image has been uploaded successfully!",
        onTap: () async {
          ReusableDialog.close();
          Get.until((route) => route.settings.name == HomeScreen.routeName);

          // Reload webview if coming from GT-Plus
          try {
            final homeController = Get.find<HomeController>();
            homeController.reloadWebViewFromGTPlus();
          } catch (e) {
            debugPrint('Error accessing HomeController: $e');
          }
        },
      );
    } on DetailedApiRequestError catch (e) {
      print("error is  : ${e.message}");
      Get.snackbar('Upload Error', 'Failed to upload image');
    } catch (e) {
      print("error is  : $e");
      Get.snackbar('Upload Error', 'Failed to upload image');
    } finally {
      isLoading.value = false;
    }
  }

  String _getFilenamePrefix(CaptureType type) {
    switch (type) {
      case CaptureType.faceMesh:
        return 'face_mesh';
      case CaptureType.face:
        return 'face';
      case CaptureType.tongue:
        return 'tongue';
      case CaptureType.palm:
        return 'palm';
      case CaptureType.teeth:
        return 'teeth';
      case CaptureType.nails:
        return 'nails';
      case CaptureType.lips:
        return 'lips';
    }
  }

  String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      default:
        return 'image/jpeg';
    }
  }

  Future<ServiceAccountCredentials> loadServiceAccountCredentials() async {
    String jsonContent =
        await rootBundle.loadString('assets/gCloud/credentials.json');
    final Map<String, dynamic> jsonMap = jsonDecode(jsonContent);
    return ServiceAccountCredentials.fromJson(jsonMap);
  }
}
