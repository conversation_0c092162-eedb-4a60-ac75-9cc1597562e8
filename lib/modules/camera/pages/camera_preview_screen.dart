import 'dart:io';

import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/const/app_colors.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';
import '../controller/camera_view_controller.dart';

class CameraPreviewScreen extends GetView<CameraViewController> {
  const CameraPreviewScreen({Key? key}) : super(key: key);

  static const routeName = "/cameraPreviewScreen";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "Preview"),
      body: Column(
        children: [
          Expanded(
            child: Obx(() => Image.file(
                  File(controller.imagePath.value),
                  fit: BoxFit.contain,
                )),
          ),
          _buildButton(context),
        ],
      ),
    );
  }

  Widget _buildButton(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 36, top: 16),
      child: Row(
        children: [
          Expanded(
            child: ReusableButton(
              title: "Re-Take",
              fontColor: AppColors.primaryOrange,
              color: Colors.white,
              onTap: () {
                Get.back();
              },
            ),
          ),
          const SizedBox(
            width: 24,
          ),
          Expanded(
              child: Obx(
            () => ReusableButton(
              title: "Done",
              isLoading: controller.isLoading.value,
              onTap: () {
                controller.uploadPhoto(context);
              },
            ),
          )),
        ],
      ),
    );
  }
}
