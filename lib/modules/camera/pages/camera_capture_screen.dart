import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:SAiWELL/utils/permission_handler_util.dart';

import '../controller/camera_view_controller.dart';
import '../instruction_config.dart';

class CameraCaptureScreen extends StatefulWidget {
  const CameraCaptureScreen({Key? key}) : super(key: key);
  static const routeName = "/cameraCaptureScreen";

  @override
  State<CameraCaptureScreen> createState() => _CameraCaptureScreenState();
}

class _CameraCaptureScreenState extends State<CameraCaptureScreen> {
  late final CameraViewController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<CameraViewController>();
    _checkCameraPermission();
  }

  Future<void> _checkCameraPermission() async {
    final permissionHandler = PermissionHandlerUtil();
    final hasPermission = await permissionHandler.checkCameraPermission();
    if (!hasPermission) {
      Get.back(); // Go back if permission denied
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.value) {
        return Scaffold(
          body: Center(
            child: CircularProgressIndicator(
              color: AppColors.primaryOrange,
            ),
          ),
        );
      }

      if (!controller.isInitialized.value) {
        return Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            title: const Text('Camera'),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Get.back(),
            ),
          ),
          body: const Center(
            child: Text(
              "Camera initializing...",
              style: TextStyle(color: Colors.white),
            ),
          ),
        );
      }

      // Camera is initialized
      return Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Column(
            children: [
              // AppBar-like widget
              Container(
                padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
                color: Colors.black,
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.arrow_back,
                          color: AppColors.primaryOrange),
                      onPressed: () => Get.back(),
                    ),
                    const Expanded(
                      child: Text(
                        'Camera',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 48), // Balance the back button
                  ],
                ),
              ),
              // Camera preview takes most of the screen
              Expanded(
                child: _buildCameraPreview(context),
              ),
              // Bottom controls
            ],
          ),
        ),
      );
    });
  }

  Widget _buildCameraPreview(BuildContext context) {
    if (controller.cameraController == null ||
        !controller.cameraController!.value.isInitialized) {
      return const Center(
        child: Text(
          "Camera not available",
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final size = MediaQuery.of(context).size;
        final screenWidth = size.width;
        final screenHeight = size.height;

        // Calculate scale for camera preview
        var camera = controller.cameraController!.value;
        var scale = size.aspectRatio * camera.aspectRatio;
        if (scale < 1) scale = 1 / scale;

        return Stack(
          children: [
            SizedBox(
              width: screenWidth,
              height: screenHeight,
              child: Transform.scale(
                scale: scale,
                child: Center(
                  child: CameraPreview(
                    controller.cameraController!,
                  ),
                ),
              ),
            ),
            Visibility(
                visible: [
                  CaptureType.teeth,
                  CaptureType.nails,
                  CaptureType.lips,
                  CaptureType.tongue
                ].contains(controller.captureType),
                child: OverlayWidget(screenWidth: screenWidth)),

            // Focus indicator
            Obx(() {
              if (controller.isFocusing.value) {
                final focusPoint = controller.relativeFocusPoint.value;
                return Positioned(
                  left: focusPoint.dx * screenWidth - 25,
                  top: focusPoint.dy * screenHeight - 25,
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.yellow, width: 2),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Container(
                        height: 10,
                        width: 10,
                        decoration: BoxDecoration(
                          color: Colors.yellow.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                );
              } else {
                return const SizedBox.shrink();
              }
            }),

            // Tap-to-focus layer
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTapDown: (TapDownDetails details) {
                  final RenderBox box = context.findRenderObject() as RenderBox;
                  final Offset localPosition =
                      box.globalToLocal(details.globalPosition);

                  final normalizedX = localPosition.dx / screenWidth;
                  final normalizedY = localPosition.dy / screenHeight;

                  controller.setFocusDirectly(Offset(normalizedX, normalizedY));
                },
                child: Container(color: Colors.transparent),
              ),
            ),
            Positioned(
              bottom: 8,
              child: SizedBox(
                width: screenWidth,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    const SizedBox(width: 50),
                    _buildCaptureButton(context),
                    _buildSwitchCameraButton(context),

                    // This is a placeholder to balance the layout
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSwitchCameraButton(BuildContext context) {
    return GestureDetector(
      onTap: () => controller.switchCamera(context),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.black54,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: const Icon(
          Icons.flip_camera_ios,
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildCaptureButton(BuildContext context) {
    return Obx(() => GestureDetector(
          onTap: controller.isLoading.value
              ? null
              : () => controller.takePicture(context),
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: controller.isLoading.value ? Colors.grey : Colors.white,
                width: 4,
              ),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: controller.isLoading.value ? Colors.grey : Colors.white,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ));
  }
}

class OverlayWidget extends StatelessWidget {
  const OverlayWidget({
    super.key,
    required this.screenWidth,
  });

  final double screenWidth;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: screenWidth * 0.75,
        height: screenWidth * 0.75,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white, width: 2),
        ),
      ),
    );
  }
}
