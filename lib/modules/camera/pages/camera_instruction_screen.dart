import 'package:SAiWELL/modules/camera/pages/camera_capture_screen.dart';
import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../faceMesh/views/face_detection_screen.dart';
import '../controller/camera_view_controller.dart';

class CameraInstructionScreen extends GetView<CameraViewController> {
  const CameraInstructionScreen({Key? key}) : super(key: key);

  static const routeName = "/cameraInstructionScreen";

  @override
  Widget build(BuildContext context) {
    return Obx(() => controller.isLoading.value
        ? const Material(
            color: Colors.white,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        : Scaffold(
            appBar: CustomAppBar(
              title: "${controller.config.title} Image",
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Image.asset(
                  controller.config.imageAsset,
                  height: context.height * .35,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
                const SizedBox(height: 24),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 28),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Please follow instruction:",
                          style: TextStyle(
                            color: AppColors.veryDarkTeal,
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        ...controller.config.bulletPoints
                            .asMap()
                            .entries
                            .map((entry) {
                          final index = entry.key;
                          final point = entry.value;
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text('${index + 1}. $point'),
                          );
                        }),
                        const Spacer(),
                        ReusableButton(
                          title: "Capture ${controller.config.title}",
                          onTap: () {
                            if (controller.config.title.toLowerCase() ==
                                "face mesh") {
                              Get.toNamed(FaceDetectionScreen.routeName);
                            } else {
                              Get.toNamed(CameraCaptureScreen.routeName);
                            }
                          },
                        ),
                        const SizedBox(
                          height: 48,
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ));
  }
}
