import 'dart:async';
import 'package:SAiWELL/common_controllers/global_controller.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:SAiWELL/utils/notification_test_helper.dart';
import 'package:SAiWELL/debug/notification_debug_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:workmanager/workmanager.dart';
import '../../services/background_fetch_service.dart';

import '../../utils/bottomSheets/navbar_bottom_sheet.dart';
import '../../utils/const/app_colors.dart';
import '../../utils/const/app_const.dart';
import '../../utils/reusableWidgets/common_bottom_navigation_bar_item.dart';
import 'controller/home_controller.dart';

class HomeScreen extends GetView<HomeController> {
  const HomeScreen({super.key});

  static const routeName = "/homeScreen";

  @override
  Widget build(BuildContext context) {
    return Obx(() => PopScope(
          canPop: !controller.globalController.shouldShowNavbar.value,
          onPopInvokedWithResult: (isPop, result) async {
            // Check if we need to reload webview after returning from GT-Plus
            controller.reloadWebViewFromGTPlus();

            if (controller.globalController.shouldShowNavbar.value) {
              final bool shouldPop = await showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      backgroundColor: Colors.white,
                      title: const Text('Exit App'),
                      content: const Text('Are you sure you want to exit?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: Text(
                            'No',
                            style: TextStyle(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: Text(
                            'Yes',
                            style: TextStyle(
                              color: AppColors.primaryOrange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ) ??
                  false;
              if (shouldPop) {
                SystemNavigator.pop();
              }
            }
          },
          child: SafeArea(
            child: Scaffold(
              resizeToAvoidBottomInset: true,
              bottomNavigationBar: Obx(
                () => controller.isPageLoading.value ||
                        !controller.globalController.shouldShowNavbar.value
                    ? const SizedBox.shrink()
                    : _buildBottomNavBar(context),
              ),
              appBar: PreferredSize(
                preferredSize: const Size.fromHeight(kToolbarHeight),
                child: Obx(() {
                  return (controller.globalController.shouldShowNavbar.value &&
                          !controller.isPageLoading.value)
                      ? AppBar(
                          leading: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Image.asset(
                              AppImages.horizontalAppLogoImage,
                            ),
                          ),
                          leadingWidth: 140,
                          actions: [
                            // Debug notification test button (only in debug mode)
                            if (kDebugMode)
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () =>
                                        _showNotificationTestDialog(context),
                                    child: const Padding(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 8),
                                      child: Icon(
                                        Icons.bug_report,
                                        color: Colors.orange,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () async {
                                      // Schedule backgroundSyncTask to run after 1 minute
                                      await Workmanager().registerOneOffTask(
                                        'test_background_sync',
                                        backgroundSyncTask,
                                        initialDelay:
                                            const Duration(minutes: 1),
                                        inputData: {'test': true},
                                      );
                                      Get.snackbar(
                                        'Background Sync Scheduled',
                                        'Task will run in 1 minute. Kill the app to test background execution.',
                                        backgroundColor:
                                            Colors.blue.withOpacity(0.8),
                                        colorText: Colors.white,
                                        duration: const Duration(seconds: 5),
                                      );
                                    },
                                    child: const Padding(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 8),
                                      child: Icon(
                                        Icons.sync,
                                        color: Colors.green,
                                        size: 24,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            // Profile Icon
                            InkWell(
                              onTap: () {
                                controller.selectedNavBarIndex.value = -1;
                                controller.navigateToUrl(controller
                                        .globalController.navigationBaseUrl +
                                    "/profile");
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: SvgPicture.asset(AppImages.icProfile),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox.shrink();
                }),
              ),
              body: FutureBuilder(
                future: waitHere(),
                builder: (buildContext, snapshot) {
                  return snapshot.connectionState != ConnectionState.done
                      ? _buildLoadingIndicator()
                      : Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            _buildWebView(context),
                            Obx(() => controller.isPageLoading.value
                                ? _buildLoadingIndicator()
                                : const SizedBox()),
                            Obx(
                              () => controller.isPageLoading.value ||
                                      !controller.globalController
                                          .shouldShowNavbar.value ||
                                      controller.globalController
                                              .currentToastStatus.value ==
                                          CurrentToastStatus.start ||
                                      controller.globalController
                                              .currentToastStatus.value ==
                                          CurrentToastStatus.completed
                                  ? const SizedBox()
                                  : Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      margin: const EdgeInsets.only(bottom: 8),
                                      decoration: BoxDecoration(
                                        color: Colors.green,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        controller.globalController
                                                    .currentToastStatus.value ==
                                                CurrentToastStatus.dataSyncing
                                            ? controller.syncStatusMessage.value
                                            : "Connecting...",
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                            )
                          ],
                        );
                },
              ),
            ),
          ),
        ));
  }

  Future<bool> waitHere() async {
    controller.isPageLoading.value = true;
    await Future.delayed(const Duration(seconds: 3));
    controller.isPageLoading.value = false;
    return true;
  }

  Widget _buildBottomNavBar(BuildContext context) {
    if (controller.globalController.navigationItems.length < 2) {
      return const SizedBox.shrink();
    }

    final displayIndex = (controller.selectedNavBarIndex.value < 0 ||
            controller.selectedNavBarIndex.value >=
                controller.globalController.navigationItems.length)
        ? controller.globalController.navigationItems.length - 1
        : controller.selectedNavBarIndex.value;

    return SizedBox(
      height: 70,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color(0x00000000).withOpacity(.15),
              offset: const Offset(5, -5),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        ),
        child: BottomNavigationBar(
          elevation: 0,
          backgroundColor: Colors.white,
          selectedItemColor: controller.selectedNavBarIndex.value == -1
              ? AppColors.veryDarkTeal
              : AppColors.primaryOrange,
          unselectedItemColor: AppColors.veryDarkTeal,
          currentIndex: displayIndex,
          onTap: (index) {
            if (index ==
                controller.globalController.navigationItems.length - 1) {
              showNavBarBottomSheet(
                context: context,
                onItemClick: (int i) {
                  controller.selectedNavBarIndex.value =
                      controller.globalController.navigationItems.length + i;
                  String redirectUrl =
                      controller.globalController.navigationBaseUrl +
                          (controller.globalController.moreNavigationItems[i]
                                  .value ??
                              "");
                  controller.navigateToUrl(redirectUrl);
                },
              );
            } else {
              controller.selectedNavBarIndex.value = index;
              String redirectUrl = controller
                      .globalController.navigationBaseUrl +
                  (controller.globalController.navigationItems[index].value ??
                      "");
              controller.navigateToUrl(redirectUrl);
            }
          },
          type: BottomNavigationBarType.fixed,
          items: List.generate(
            controller.globalController.navigationItems.length,
            (index) => commonBottomNavigationBarItem(
              icon: controller.globalController.navigationItemsImages[index],
              label: controller.globalController.navigationItems[index].label ??
                  "",
              itemIndex: index,
              navbarLength: controller.globalController.navigationItems.length,
              currentIndex: controller.selectedNavBarIndex.value,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWebView(BuildContext context) {
    return InAppWebView(
      initialUrlRequest: URLRequest(
        url: WebUri((controller.isHomePageRequiredInWebView.value
            ? webViewUrl
            : ringWebViewUrl)),
      ),
      gestureRecognizers: Set()
        ..add(Factory<VerticalDragGestureRecognizer>(
            () => VerticalDragGestureRecognizer())),
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        javaScriptCanOpenWindowsAutomatically: true,
        useShouldOverrideUrlLoading: true,
        mediaPlaybackRequiresUserGesture: false,
        allowsInlineMediaPlayback: true,
        iframeAllow: "camera; microphone",
        iframeAllowFullscreen: true,
        transparentBackground: true,
        isInspectable: true,
      ),
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        debugPrint("Navigation to: ${navigationAction.request.url}");
        return NavigationActionPolicy.ALLOW;
      },
      onWebViewCreated: (inAppWebViewController) {
        controller.inAppWebViewController = inAppWebViewController;
        controller.setWebViewListeners();
        controller.injectUrlChangeMonitor();
        controller.startUrlPolling();
        controller.isHomePageRequiredInWebView.value = true;
      },
      onLoadStart: (inAppWebViewController, webUri) async {
        debugPrint("Load started: ${webUri.toString()}");
        controller.isPageLoading.value = true;
      },
      onLoadStop: (inAppWebViewController, webUri) async {
        debugPrint("Load stopped: ${webUri.toString()}");

        controller.isPageLoading.value = false;
        await controller.pullToRefreshController.endRefreshing();

        // Call the controller's onWebViewLoadStop method for NFC handling
        await controller.onWebViewLoadStop(inAppWebViewController, webUri);

        Future.delayed(const Duration(milliseconds: 500), () {
          controller.injectUrlChangeMonitor();
          controller.injectTextFieldMoniter();
          if (webUri.toString().contains("/landing")) {
            controller.addLoginHandlers();
          }
        });
      },
      onProgressChanged: (controller, progress) {
        debugPrint("Loading progress: $progress%");
        if (progress == 100) {
          this.controller.isPageLoading.value = false;
        } else {
          this.controller.isPageLoading.value = true;
        }
      },
      onConsoleMessage: (controller, consoleMessage) {
        debugPrint('Console message: ${consoleMessage.message}');
      },
      pullToRefreshController: controller.pullToRefreshController,
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      height: Get.height,
      width: Get.width,
      color: Colors.white,
      child: const Center(
        child: Image(
          image: AssetImage(
            AppImages.appLogoImage,
          ),
        ),
      ),
    );
  }

  void _showNotificationTestDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: const Text('🐛 Debug: Test Notifications'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('This will schedule a test notification in 1 minute.'),
            SizedBox(height: 8),
            Text('Steps to test:'),
            Text('1. Tap "Schedule Test"'),
            Text('2. Kill the app completely'),
            Text('3. Wait 1 minute'),
            Text('4. Check if notification appears'),
            SizedBox(height: 8),
            Text(
                'This verifies background notifications work when app is killed.',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              Get.toNamed(NotificationDebugScreen.routeName);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
            child: const Text('Debug Screen',
                style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () async {
              await NotificationTestHelper.scheduleTestNotification();
              Navigator.of(context).pop();
              Get.snackbar(
                'Test Scheduled',
                'Kill the app now and wait 1 minute for the test notification!',
                backgroundColor: Colors.blue.withOpacity(0.8),
                colorText: Colors.white,
                duration: const Duration(seconds: 5),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            child: const Text('Schedule Test',
                style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () async {
              await NotificationTestHelper.checkNotificationStatus();
              Navigator.of(context).pop();
              Get.snackbar(
                'Status Checked',
                'Check console/logs for notification status details',
                backgroundColor: Colors.green.withOpacity(0.8),
                colorText: Colors.white,
                duration: const Duration(seconds: 3),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Check Status',
                style: TextStyle(color: Colors.white)),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await NotificationTestHelper
                  .testProductionNotificationScheduling();
              Get.snackbar(
                'Production Test',
                'Production notifications rescheduled. Check logs for details.',
                backgroundColor: Colors.orange.withValues(alpha: 0.8),
                colorText: Colors.white,
                duration: const Duration(seconds: 3),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text(
              'Test Production',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
