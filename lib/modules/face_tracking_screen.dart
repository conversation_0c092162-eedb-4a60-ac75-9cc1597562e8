// import 'package:arkit_plugin/arkit_plugin.dart';
// import 'package:flutter/material.dart';
//
//
// class FaceDetectionPage extends StatefulWidget {
//   const FaceDetectionPage({super.key});
//
//   static const routeName = "/faceDetectionPage";
//
//   @override
//   _FaceDetectionPageState createState() => _FaceDetectionPageState();
// }
//
// class _FaceDetectionPageState extends State<FaceDetectionPage> {
//   bool isCapturing = false;
//   Map<String, dynamic>? capturedFaceData;
//
//   @override
//   Widget build(BuildContext context) => Scaffold(
//     appBar: AppBar(title: const Text('Face Detection')),
//     body: capturedFaceData == null
//         ? _buildCaptureScreen()
//         : _buildPreviewScreen(),
//   );
//
//   Widget _buildCaptureScreen() {
//     return isCapturing
//         ? _FaceCaptureView(
//       onFaceCaptured: (faceData) {
//         setState(() {
//           capturedFaceData = faceData;
//           isCapturing = false;
//         });
//       },
//       onCancel: () {
//         setState(() {
//           isCapturing = false;
//         });
//       },
//     )
//         : Center(
//       child: ElevatedButton.icon(
//         onPressed: () {
//           setState(() {
//             isCapturing = true;
//           });
//         },
//         icon: const Icon(Icons.camera),
//         label: const Text('Capture Face'),
//         style: ElevatedButton.styleFrom(
//           padding: const EdgeInsets.symmetric(
//             horizontal: 24,
//             vertical: 16,
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildPreviewScreen() {
//     return Center(
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           const Icon(
//             Icons.face,
//             size: 100,
//             color: Colors.blue,
//           ),
//           const SizedBox(height: 20),
//           const Text(
//             'Face Data Captured!',
//             style: TextStyle(
//               fontSize: 24,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           const SizedBox(height: 40),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               ElevatedButton.icon(
//                 onPressed: _uploadFaceData,
//                 icon: const Icon(Icons.upload),
//                 label: const Text('Upload'),
//                 style: ElevatedButton.styleFrom(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 24,
//                     vertical: 16,
//                   ),
//                 ),
//               ),
//               const SizedBox(width: 20),
//               OutlinedButton.icon(
//                 onPressed: () {
//                   setState(() {
//                     capturedFaceData = null;
//                   });
//                 },
//                 icon: const Icon(Icons.replay),
//                 label: const Text('Retake'),
//                 style: OutlinedButton.styleFrom(
//                   padding: const EdgeInsets.symmetric(
//                     horizontal: 24,
//                     vertical: 16,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   Future<void> _uploadFaceData() async {
//     try {
//       showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (context) => const Center(
//           child: CircularProgressIndicator(),
//         ),
//       );
//
//       // Add your upload logic here
//       print('Uploading face data: ${capturedFaceData.toString()}');
//
//       Navigator.pop(context); // Remove loading indicator
//
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(
//           content: Text('Face data uploaded successfully!'),
//           backgroundColor: Colors.green,
//         ),
//       );
//
//       // Reset after successful upload
//       setState(() {
//         capturedFaceData = null;
//       });
//     } catch (e) {
//       Navigator.pop(context); // Remove loading indicator
//
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('Upload failed: ${e.toString()}'),
//           backgroundColor: Colors.red,
//         ),
//       );
//     }
//   }
// }
//
// class _FaceCaptureView extends StatefulWidget {
//   final Function(Map<String, dynamic>) onFaceCaptured;
//   final VoidCallback onCancel;
//
//   const _FaceCaptureView({
//     required this.onFaceCaptured,
//     required this.onCancel,
//   });
//
//   @override
//   _FaceCaptureViewState createState() => _FaceCaptureViewState();
// }
//
// class _FaceCaptureViewState extends State<_FaceCaptureView> {
//   late ARKitController arkitController;
//   ARKitNode? node;
//   bool isFaceDetected = false;
//
//   @override
//   void dispose() {
//     arkitController.dispose();
//     super.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) => Stack(
//     children: [
//       ARKitSceneView(
//         configuration: ARKitConfiguration.faceTracking,
//         onARKitViewCreated: onARKitViewCreated,
//       ),
//       Positioned(
//         top: 20,
//         right: 20,
//         child: IconButton(
//           icon: const Icon(Icons.close),
//           color: Colors.white,
//           onPressed: widget.onCancel,
//         ),
//       ),
//       if (isFaceDetected)
//         Positioned(
//           bottom: 20,
//           left: 0,
//           right: 0,
//           child: Center(
//             child: ElevatedButton.icon(
//               onPressed: _captureFace,
//               icon: const Icon(Icons.camera),
//               label: const Text('Capture'),
//               style: ElevatedButton.styleFrom(
//                 padding: const EdgeInsets.symmetric(
//                   horizontal: 24,
//                   vertical: 16,
//                 ),
//               ),
//             ),
//           ),
//         ),
//     ],
//   );
//
//   void onARKitViewCreated(ARKitController arkitController) {
//     this.arkitController = arkitController;
//     this.arkitController.onAddNodeForAnchor = _handleAddAnchor;
//     this.arkitController.onUpdateNodeForAnchor = _handleUpdateAnchor;
//   }
//
//   void _handleAddAnchor(ARKitAnchor anchor) {
//     if (!(anchor is ARKitFaceAnchor)) return;
//
//     final material = ARKitMaterial(
//       fillMode: ARKitFillMode.lines,
//       diffuse: ARKitMaterialProperty.color(Colors.white),
//     );
//     anchor.geometry.materials.value = [material];
//
//     node = ARKitNode(geometry: anchor.geometry);
//     arkitController.add(node!, parentNodeName: anchor.nodeName);
//   }
//
//   void _handleUpdateAnchor(ARKitAnchor anchor) {
//     if (anchor is ARKitFaceAnchor && mounted) {
//       if (node != null) {
//         arkitController.updateFaceGeometry(node!, anchor.identifier);
//       }
//
//       setState(() {
//         isFaceDetected = true;
//       });
//     }
//   }
//
//   void _captureFace() {
//     final faceData = {
//       'timestamp': DateTime.now().toIso8601String(),
//       'capturedAt': DateTime.now().toString(),
//       // Add additional face data as needed
//     };
//
//     widget.onFaceCaptured(faceData);
//   }
// }