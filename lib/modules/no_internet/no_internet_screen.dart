import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:SAiWELL/common_controllers/connectivity.dart';
import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';

class NoInternetScreen extends StatelessWidget {
  static const String routeName = '/no-internet';
  
  const NoInternetScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final connectivityController = Get.find<ConnectivityController>();
    
    return WillPopScope(
      // Prevent going back when no internet
      onWillPop: () async => false,
      child: Scaffold(
        appBar: const CustomAppBar(title: 'Network Error'),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              children: [
                const Spacer(flex: 1),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      )
                    ]
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.snowGray,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.signal_wifi_off_rounded,
                          size: 64,
                          color: AppColors.primaryTeal,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'No Internet Connection',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryTeal,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Please check your internet connection and try again',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.mediumGray,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),
                      Obx(() => SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: connectivityController.loadingCheckConnectivity.value 
                              ? null 
                              : () => connectivityController.checkConnectivity(),
                          icon: connectivityController.loadingCheckConnectivity.value 
                              ? const SizedBox(
                                  width: 20, 
                                  height: 20, 
                                  child: CircularProgressIndicator(strokeWidth: 2)
                                )
                              : Icon(Icons.refresh, color: Colors.white),
                          label: Text(
                            connectivityController.loadingCheckConnectivity.value 
                                ? 'Checking...' 
                                : 'Retry',
                            style: const TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryOrange,
                            padding: const EdgeInsets.symmetric(
                              vertical: 14,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      )),
                    ],
                  ),
                ),
                const Spacer(flex: 2),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 