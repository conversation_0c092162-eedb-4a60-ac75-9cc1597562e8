import 'package:SAiWELL/modules/ppg/controller/ppg_controller.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:SAiWELL/modules/ppg/screens/ppg_graph_screen.dart';

import '../../../utils/const/app_colors.dart';
import '../../../utils/reusableWidgets/reusable_button.dart';

class PpgInstructionScreen extends GetView<PPGController> {
  const PpgInstructionScreen({super.key});

  static const routeName = "/ppgInstructionScreen";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "PPG Recording"),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(
            width: double.infinity,
            AppImages.imgPPGInstruction,
          ),
          const SizedBox(
            height: 36,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "Please follow instruction:",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Obx(
                  () => ListView.separated(
                      itemCount: controller.instructionTexts.length,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return Text(
                          "${index + 1}. ${controller.instructionTexts[index]}",
                          style: TextStyle(
                            color: AppColors.mediumGray,
                            height: 1.6,
                          ),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(
                          height: 8,
                        );
                      }),
                ),
                const SizedBox(
                  height: 28,
                ),
                ReusableButton(
                    title: "Continue",
                    onTap: () {
                      Get.toNamed(PpgGraphScreen.routeName);
                    }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
