import 'package:get/get.dart';
import 'package:get/get_instance/src/bindings_interface.dart';
import '../controller/ppg_controller.dart';

class PpgBinding {
  static List<Bindings> binding = [
    BindingsBuilder(() {
      // If the controller is already registered, don't try to put it again
      if (!Get.isRegistered<PPGController>()) {
        Get.lazyPut<PPGController>(() => PPGController());
      }
    }),
  ];
} 