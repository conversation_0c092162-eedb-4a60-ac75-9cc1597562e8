import 'package:SAiWELL/models/ble_device.dart';
import 'package:SAiWELL/modules/ring/controller/ring_list_controller.dart';
import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RingListScreen extends GetView<RingListController> {
  const RingListScreen({super.key});

  static const routeName = "/ringListScreen";

  @override
  Widget build(BuildContext context) {
    // Get the isFromPPG parameter from arguments
    final isFromPPG = Get.arguments?['isFromPPG'] == true;
    
    // Debug print to log the isFromPPG value
    debugPrint("RingListScreen - isFromPPG: $isFromPPG");
    
    return Scaffold(
      appBar: const CustomAppBar(title: 'Connect Device'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // literally,
            _buildInstructionText(),
            const SizedBox(height: 40),
            Visibility(
                visible: controller.foundDevices.isEmpty,
                child: _getNoDeviceFoundImage()),
            Visibility(
              visible: controller.foundDevices.isNotEmpty,
              child: SizedBox(
                height: 400,
                child: ListView.builder(
                    itemCount: controller.foundDevices.length,
                    itemBuilder: (context, index) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: _buildRingTile(
                              controller.foundDevices[index], index),
                        )),
              ),
            ),
            const SizedBox(height: 36),
            Obx(
              () => controller.foundDevices.isEmpty
                  ? ReusableButton(
                      title: "Try Again",
                      onTap: controller.rescanAction,
                    )
                  : ReusableButton(
                      title: "Connect",
                      onTap: controller.currentSelectedDeviceIndex.value == -1
                          ? null
                          : () => controller.connectAction(isFromPPG: isFromPPG),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getNoDeviceFoundImage() {
    return Image.asset(
      AppImages.bleNoDevicesFound,
      width: double.infinity,
      // width: 60,
    );
  }

  Widget _buildRingImage() {
    return Image.asset(
      AppImages.ringImage,
      // width: double.infinity,
      width: 60,
    );
  }

  Widget _buildInstructionText() {
    return Column(
      children: [
        Text(
          controller.foundDevices.isEmpty
              ? "No device found"
              : "Found ${controller.foundDevices.length} Device",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: AppColors.primaryTeal,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          controller.foundDevices.isEmpty
              ? "Click the button to try again"
              : "Click the button to connect the device".capitalize!,
          style: TextStyle(
            color: AppColors.darkGray,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildRingTile(BLEDevice device, int index) {
    return InkWell(
      onTap: () => controller.currentSelectedDeviceIndex.value = index,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: AppColors.darkGray)),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildRingImage(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.name,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.darkGray,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  "Medical smart ring".capitalize!,
                  style: TextStyle(
                    color: AppColors.darkGray,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            Obx(() {
              return controller.currentSelectedDeviceIndex.value == index
                  ? const Icon(Icons.check_circle_sharp)
                  : const Icon(Icons.circle_outlined);
            })
          ],
        ),
      ),
    );
  }
}
