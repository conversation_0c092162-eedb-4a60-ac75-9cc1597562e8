import 'package:SAiWELL/modules/ring/controller/ring_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../utils/const/app_colors.dart';
import '../../utils/const/app_images.dart';
import '../../utils/reusableWidgets/custom_app_bar.dart';

class ConnectingRingScreen extends StatefulWidget {
  const ConnectingRingScreen({super.key});
  static const routeName = "/connectingRingScreen";

  @override
  State<ConnectingRingScreen> createState() => _ConnectingRingScreenState();
}

class _ConnectingRingScreenState extends State<ConnectingRingScreen> {
  RingListController controller = Get.find<RingListController>();

  @override
  void initState() {
    super.initState();
    // Future.delayed(const Duration(seconds: 3), () {
    //   Get.toNamed(RingListScreen.routeName);
    // });
  }

  @override
  Widget build(BuildContext context) {
    final double maxRippleSize = MediaQuery.of(context).size.width;
    const double ringSize = 135.0;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Connecting',
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Flexible(
            flex: 2,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  AppImages.ringImage,
                  width: ringSize,
                  height: ringSize,
                ),

                _buildRipple(maxRippleSize, .6),
                _buildRipple(maxRippleSize * .8, .8),

                // Ring image
              ],
            ),
          ),
          Column(
            children: [
              Text(
                "connecting to ${controller.foundDevices[controller.currentSelectedDeviceIndex.value].name}..."
                    .capitalize!,
                style: TextStyle(
                  color: AppColors.primaryTeal,
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "It is going to take only few seconds".capitalize!,
                style: TextStyle(
                  color: AppColors.darkGray,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

Widget _buildRipple(double size, double opacity) {
  return SpinKitRipple(
    color: AppColors.primaryTeal.withOpacity(opacity),
    size: size,
    borderWidth: 8,
    duration: const Duration(milliseconds: 5000),
  );
}
