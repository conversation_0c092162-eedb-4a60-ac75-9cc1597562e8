import 'package:SAiWELL/common_controllers/global_controller.dart';
import 'package:SAiWELL/models/ble_device.dart';
import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:SAiWELL/modules/home/<USER>';
import 'package:SAiWELL/modules/ring/connecting_ring_screen.dart';
import 'package:SAiWELL/modules/ring/searching_ring_screen.dart';
import 'package:SAiWELL/services/native_communicator.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/services/analytics/events.dart';
import 'package:SAiWELL/services/notifications/core/notification_manager.dart';
import 'package:SAiWELL/utils/flutter_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class RingListController extends GetxController {
  RxList<BLEDevice> foundDevices = <BLEDevice>[].obs;
  HomeController homeController = Get.find<HomeController>();
  GlobalController globalController = Get.find<GlobalController>();
  PrefsService prefsService = PrefsService();

  RxInt currentSelectedDeviceIndex = (-1).obs;

  void connectAction({bool? isFromPPG}) async {

    bool isRingFirstConnection = await prefsService.getIsRingFirstConnection();
    if (!isRingFirstConnection) {
      LogEvents.logRingFirstConnectionEvent();
      prefsService.setIsRingFirstConnection(true);
    }

    globalController.currentToastStatus.value = CurrentToastStatus.connecting;
    Get.toNamed(ConnectingRingScreen.routeName);

    NativeCommunicator communicator = NativeCommunicator();

    // Save device details first
    globalController.ringMacAddress.value =
        foundDevices[currentSelectedDeviceIndex.value].macId.toString();
    prefsService
        .setLastConnectedDeviceMac(globalController.ringMacAddress.value);
    globalController.ringName.value =
        foundDevices[currentSelectedDeviceIndex.value].name.toString();
    prefsService.setLastConnectedDeviceName(globalController.ringName.value);

    // Connect to device
    await communicator.connectToDeviceV2(
      macId: foundDevices[currentSelectedDeviceIndex.value].macId,
      updateRingStatusToWebView: homeController.updateRingStatusToWebView,
      reloadWebView: homeController.reloadWebView,
      updateDeviceConnectionDetailsToWebview:
          homeController.updateDeviceConnectionDetailsToWebview,
      updateBetteryStatusInWebView: homeController.updateBatteryStatusToWebview,
    );

    // Check connection state
    bool isConnected = await communicator.getConnectionStateV2();

    // Set global connection state based on current state
    globalController.isConnected.value = isConnected;

    // If not connected immediately, try a few times
    if (!isConnected) {
      int maxTry = 20;

      do {
        await Future.delayed(const Duration(milliseconds: 500));

        // Check if Bluetooth was turned off during connection attempt
        if (globalController.currentToastStatus.value == CurrentToastStatus.notConnected) {
          break;
        }

        isConnected = await communicator.getConnectionStateV2();

        // Update global state if needed
        if (isConnected) {
          globalController.isConnected.value = true;
          homeController.isHomePageRequiredInWebView.value = false;
          break;
        }
      } while (!isConnected && --maxTry > 0);
    }

    // Update final connection state only if not already set by Bluetooth callback
    if (globalController.currentToastStatus.value == CurrentToastStatus.connecting) {
      globalController.currentToastStatus.value = isConnected
          ? CurrentToastStatus.connected
          : CurrentToastStatus.notConnected;
    }



    // Handle navigation based on connection result
    if (globalController.currentToastStatus.value == CurrentToastStatus.connected) {
      // Connection successful
      if (isFromPPG == true) {
        try {
          await NotificationManager.startDailyNotifications();
        } catch (e) {
          // Keep error log for notification enabling
          debugPrint('Error enabling notifications for new ring user: $e');
        }
        // If coming from PPG, go back to PPG screen
        Get.until((route) => route.settings.name == HomeScreen.routeName);
      } else {
        try {
          await NotificationManager.startDailyNotifications();
        } catch (e) {
          // Keep error log for notification enabling
          debugPrint('Error enabling notifications for new ring user: $e');
        }
        // Otherwise, go to home page
        Get.offNamed(HomeScreen.routeName);
      }
    } else {
      // Connection failed - go back to ring list screen
      Get.back(); // Go back to ring list screen

      // Show appropriate error message
      if (globalController.currentToastStatus.value == CurrentToastStatus.notConnected) {
        CustomToast.error("Connection failed. Please check if Bluetooth is enabled and try again.");
      }
    }
  }

  void rescanAction() {
    int count = 0;
    Get.offNamedUntil(
      SearchingRingScreen.routeName,
      (route) {
        count++;
        return count == 3;
      },
    );
  }
}
