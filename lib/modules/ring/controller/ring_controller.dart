import 'package:SAiWELL/models/ble_device.dart';
import 'package:SAiWELL/modules/ring/controller/ring_list_controller.dart';
import 'package:SAiWELL/modules/ring/ring_list_screen.dart';
import 'package:SAiWELL/modules/ring/searching_ring_screen.dart';
import 'package:SAiWELL/services/bluetooth_service.dart';
import 'package:SAiWELL/services/native_communicator.dart';
import 'package:SAiWELL/utils/flutter_toast.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RingController extends GetxController {
  RingListController ringListController = Get.find<RingListController>();
  BluetoothService bleService = BluetoothService();
  final NativeCommunicator _communicator = NativeCommunicator();
  
  checkPermissionAndStartScanning({bool? isFromPPG}) async {
    // Check Bluetooth permissions first
    final hasPermission = await bleService.checkPermission();
    if (!hasPermission) {
      CustomToast.error("Required permissions not granted");
      return;
    }

    // Then check if Bluetooth is enabled
    bool isBluetoothReady = bleService.checkIsBluetoothReady();
    if (!isBluetoothReady) {
      await Future.delayed(const Duration(seconds: 1));
      isBluetoothReady = bleService.checkIsBluetoothReady();
      if (!isBluetoothReady) {
        _showBluetoothDisabledDialog(isFromPPG: isFromPPG);
        return;
      }
    }
    
    // If everything is ready, navigate to searching screen
    onSearchRingClick(isFromPPG: isFromPPG);
  }

  void _showBluetoothDisabledDialog({bool? isFromPPG}) {
    // Close any open dialogs first
    if (Get.isDialogOpen ?? false) {
      ReusableDialog.close();
    }
    
    // Show the Bluetooth dialog using ReusableDialog
    ReusableDialog.show(
      isDismissible: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Bluetooth is Disabled',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Bluetooth is required to connect to your ring device. Would you like to open Bluetooth settings now?',
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () => ReusableDialog.close(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  ReusableDialog.close();
                  await bleService.openBluetoothSettings();
                },
                child: const Text('Open Settings'),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  void onSearchRingClick({bool? isFromPPG}) async {
    Get.toNamed(SearchingRingScreen.routeName, arguments: {'isFromPPG': isFromPPG});
  }

  startScan() async {
    try {
      List<BLEDevice> devices = await _communicator.startScanDevicesV2();
      ringListController.foundDevices.value = devices;
      
      // Pass the isFromPPG argument from current screen to the next screen
      Get.toNamed(RingListScreen.routeName, arguments: Get.arguments);
    } catch (e) {
      CustomToast.error("Failed to scan: ${e.toString()}");
    }
  }
}
