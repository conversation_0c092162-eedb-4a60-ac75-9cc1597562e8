import 'package:SAiWELL/modules/ring/controller/ring_controller.dart';
import 'package:SAiWELL/utils/const/app_colors.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:SAiWELL/utils/reusableWidgets/custom_app_bar.dart';
import 'package:SAiWELL/utils/reusableWidgets/reusable_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RingHomeScreen extends GetView<RingController> {
  const RingHomeScreen({super.key});

  static const String routeName = "/ringHomeScreen";

  @override
  Widget build(BuildContext context) {
    // Retrieve isFromPPG from arguments
    final isFromPPG = Get.arguments?['isFromPPG'] == true;
    
    // Debug print to log the isFromPPG value
    debugPrint("RingHomeScreen - isFromPPG: $isFromPPG");
    
    return Scaffold(
      appBar: const CustomAppBar(title: 'Connect Device'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(child: _buildRingImage(context)),
            const SizedBox(height: 40),
            _buildInstructionText(),
            const SizedBox(height: 36),
            ReusableButton(
              title: "Search for Ring",
              onTap: () => controller.checkPermissionAndStartScanning(isFromPPG: isFromPPG),
            ),
            const SizedBox(height: 36),
            // SelectableText(
            //   fcmToken ?? "",
            //   style: TextStyle(fontSize: 16),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildRingImage(BuildContext context) {
    return Image.asset(
      AppImages.ringImage,
      width: double.infinity,
    );
  }

  Widget _buildInstructionText() {
    return Column(
      children: [
        Text(
          "Please Make Sure Your\nSAiWELL Ring Is ON",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: AppColors.primaryTeal,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          "turn on the bluetooth connection".capitalize!,
          style: TextStyle(
            color: AppColors.darkGray,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
