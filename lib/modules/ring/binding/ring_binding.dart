import 'package:SAiWELL/modules/home/<USER>/home_controller.dart';
import 'package:SAiWELL/modules/ring/controller/ring_controller.dart';
import 'package:SAiWELL/modules/ring/controller/ring_list_controller.dart';
import 'package:get/get_instance/src/bindings_interface.dart';

class RingBinding {
  static List<Bindings> binding = [
    BindingsBuilder.put(() => HomeController()),
    BindingsBuilder.put(() => RingListController()),
    BindingsBuilder.put(() => RingController()),
  ];
}
