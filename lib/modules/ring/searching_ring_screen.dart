import 'package:SAiWELL/modules/ring/controller/ring_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../utils/const/app_colors.dart';
import '../../utils/const/app_images.dart';
import '../../utils/reusableWidgets/custom_app_bar.dart';

class SearchingRingScreen extends StatefulWidget {
  const SearchingRingScreen({super.key});
  static const routeName = "/searchingRingScreen";

  @override
  State<SearchingRingScreen> createState() => _SearchingRingScreenState();
}

class _SearchingRingScreenState extends State<SearchingRingScreen> {
  RingController controller = Get.find<RingController>();

  @override
  void initState() {
    super.initState();
      controller.startScan();
  }

  @override
  Widget build(BuildContext context) {
    final double maxRippleSize = MediaQuery.of(context).size.width;
    const double ringSize = 135.0;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Searching',
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Flexible(
            flex: 2,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Image.asset(
                  AppImages.ringImage,
                  width: ringSize,
                  height: ringSize,
                ),
                _buildRipple(maxRippleSize, .6),
                _buildRipple(maxRippleSize * .8, .8),

                // Ring image
              ],
            ),
          ),
          Column(
            children: [
              Text(
                "searching for device...".capitalize!,
                style: TextStyle(
                  color: AppColors.primaryTeal,
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 6),
              Text(
                "It is going to take only few seconds".capitalize!,
                style: TextStyle(
                  color: AppColors.darkGray,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

Widget _buildRipple(double size, double opacity) {
  return SpinKitRipple(
    color: AppColors.primaryTeal.withOpacity(opacity),
    size: size,
    borderWidth: 8,
    duration: const Duration(milliseconds: 5000),
  );
}
