import 'package:SAiWELL/modules/splash/controller/splash_controller.dart';
import 'package:SAiWELL/utils/const/app_images.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SplashScreen extends GetView<SplashController> {
  const SplashScreen({super.key});

  static const routeName = "/splashScreen";

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SplashController>(
      builder: (controller) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(AppImages.appLogoImageWithImages),
              ],
            ),
          ),
        );
      },
    );
  }
}
