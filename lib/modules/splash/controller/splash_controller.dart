import 'dart:io';
import 'package:SAiWELL/modules/update_available/check_app_update_screen.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:SAiWELL/utils/permission_handler_util.dart';

class SplashController extends GetxController {
  Future<void> doLoading() async {
    try {
      await Health().configure();
      await askPermissions();
      final prefs = await SharedPreferences.getInstance();
      String debugStep = prefs.getString("debug_step") ?? "No debug info";
      print("Debug Step: $debugStep");
      await Future.delayed(const Duration(
        milliseconds: 100,
      ));

      if (Get.isRegistered<SplashController>()) {
        await Get.offAllNamed(
          CheckAppUpdateScreen.routeName,
        );
      }
    } catch (e) {
      print('Error during splash loading: $e');
    }
  }

  Future<void> askPermissions() async {
    final permissionUtil = PermissionHandlerUtil();
    
    if (Platform.isAndroid) {
      // Request all required Android permissions
      await permissionUtil.checkMultiplePermissions(
        [
          Permission.activityRecognition,
          Permission.location,
        ],
        title: 'App Permissions',
        message: 'These permissions are needed for the app to function properly.',
      );
    } else {
      // For iOS, only ask for location
      await permissionUtil.checkLocationPermission();
    }
  }

  @override
  void onInit() {
    super.onInit();
    doLoading();
  }
}
