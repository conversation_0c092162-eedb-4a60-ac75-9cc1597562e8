String? fcmToken;

const bool isProdEnv =
    String.fromEnvironment("ENVIRONMENT", defaultValue: "development") ==
        "production";
const String? fbDatabaseId = isProdEnv ? null : "staging";

String iosAppId = "**********";
//
// String? baseURL;

const Map<String, String> healthKitTypeMap = {
  "HKQuantityTypeIdentifierActiveEnergyBurned": "ACTIVE_ENERGY_BURNED",
  "HKCategoryTypeIdentifierAtrialFibrillationBurden":
      "ATRIAL_FIBRILLATION_BURDEN",
  "HKQuantityTypeIdentifierAudiogram": "AUDIOGRAM",
  "HKQuantityTypeIdentifierBasalEnergyBurned": "BASAL_ENERGY_BURNED",
  "HKQuantityTypeIdentifierBloodGlucose": "BLOOD_GLUCOSE",
  "HKQuantityTypeIdentifierBloodOxygen": "BLOOD_OXYGEN",
  "HKQuantityTypeIdentifierBloodPressureDiastolic": "BLOOD_PRESSURE_DIASTOLIC",
  "HKQuantityTypeIdentifierBloodPressureSystolic": "BLOOD_PRESSURE_SYSTOLIC",
  "HKQuantityTypeIdentifierBodyFatPercentage": "BODY_FAT_PERCENTAGE",
  "HKQuantityTypeIdentifierBodyMassIndex": "BODY_MASS_INDEX",
  "HKQuantityTypeIdentifierBodyTemperature": "BODY_TEMPERATURE",
  "HKQuantityTypeIdentifierDietaryCarbohydrates": "DIETARY_CARBS_CONSUMED",
  "HKQuantityTypeIdentifierDietaryCaffeine": "DIETARY_CAFFEINE",
  "HKQuantityTypeIdentifierDietaryEnergyConsumed": "DIETARY_ENERGY_CONSUMED",
  "HKQuantityTypeIdentifierDietaryFatTotal": "DIETARY_FATS_CONSUMED",
  "HKQuantityTypeIdentifierDietaryProtein": "DIETARY_PROTEIN_CONSUMED",
  "HKQuantityTypeIdentifierDietaryFiber": "DIETARY_FIBER",
  "HKQuantityTypeIdentifierDietarySugar": "DIETARY_SUGAR",
  "HKQuantityTypeIdentifierDietaryFatMonounsaturated":
      "DIETARY_FAT_MONOUNSATURATED",
  "HKQuantityTypeIdentifierDietaryFatPolyunsaturated":
      "DIETARY_FAT_POLYUNSATURATED",
  "HKQuantityTypeIdentifierDietaryFatSaturated": "DIETARY_FAT_SATURATED",
  "HKQuantityTypeIdentifierDietaryCholesterol": "DIETARY_CHOLESTEROL",
  "HKQuantityTypeIdentifierDietaryVitaminA": "DIETARY_VITAMIN_A",
  "HKQuantityTypeIdentifierDietaryThiamin": "DIETARY_THIAMIN",
  "HKQuantityTypeIdentifierDietaryRiboflavin": "DIETARY_RIBOFLAVIN",
  "HKQuantityTypeIdentifierDietaryNiacin": "DIETARY_NIACIN",
  "HKQuantityTypeIdentifierDietaryPantothenicAcid": "DIETARY_PANTOTHENIC_ACID",
  "HKQuantityTypeIdentifierDietaryVitaminB6": "DIETARY_VITAMIN_B6",
  "HKQuantityTypeIdentifierDietaryBiotin": "DIETARY_BIOTIN",
  "HKQuantityTypeIdentifierDietaryVitaminB12": "DIETARY_VITAMIN_B12",
  "HKQuantityTypeIdentifierDietaryVitaminC": "DIETARY_VITAMIN_C",
  "HKQuantityTypeIdentifierDietaryVitaminD": "DIETARY_VITAMIN_D",
  "HKQuantityTypeIdentifierDietaryVitaminE": "DIETARY_VITAMIN_E",
  "HKQuantityTypeIdentifierDietaryVitaminK": "DIETARY_VITAMIN_K",
  "HKQuantityTypeIdentifierDietaryFolate": "DIETARY_FOLATE",
  "HKQuantityTypeIdentifierDietaryCalcium": "DIETARY_CALCIUM",
  "HKQuantityTypeIdentifierDietaryChloride": "DIETARY_CHLORIDE",
  "HKQuantityTypeIdentifierDietaryIron": "DIETARY_IRON",
  "HKQuantityTypeIdentifierDietaryMagnesium": "DIETARY_MAGNESIUM",
  "HKQuantityTypeIdentifierDietaryPhosphorus": "DIETARY_PHOSPHORUS",
  "HKQuantityTypeIdentifierDietaryPotassium": "DIETARY_POTASSIUM",
  "HKQuantityTypeIdentifierDietarySodium": "DIETARY_SODIUM",
  "HKQuantityTypeIdentifierDietaryZinc": "DIETARY_ZINC",
  "HKQuantityTypeIdentifierDietaryChromium": "DIETARY_CHROMIUM",
  "HKQuantityTypeIdentifierDietaryCopper": "DIETARY_COPPER",
  "HKQuantityTypeIdentifierDietaryIodine": "DIETARY_IODINE",
  "HKQuantityTypeIdentifierDietaryManganese": "DIETARY_MANGANESE",
  "HKQuantityTypeIdentifierDietaryMolybdenum": "DIETARY_MOLYBDENUM",
  "HKQuantityTypeIdentifierDietarySelenium": "DIETARY_SELENIUM",
  "HKQuantityTypeIdentifierElectrodermalActivity": "ELECTRODERMAL_ACTIVITY",
  "HKQuantityTypeIdentifierForcedExpiratoryVolume1": "FORCED_EXPIRATORY_VOLUME",
  "HKQuantityTypeIdentifierHeartRate": "HEART_RATE",
  "HKQuantityTypeIdentifierHeartRateVariabilitySDNN":
      "HEART_RATE_VARIABILITY_SDNN",
  "HKQuantityTypeIdentifierHeight": "HEIGHT",
  "HKQuantityTypeIdentifierInsulinDelivery": "INSULIN_DELIVERY",
  "HKCategoryTypeIdentifierHighHeartRateEvent": "HIGH_HEART_RATE_EVENT",
  "HKCategoryTypeIdentifierIrregularHeartRateEvent":
      "IRREGULAR_HEART_RATE_EVENT",
  "HKCategoryTypeIdentifierLowHeartRateEvent": "LOW_HEART_RATE_EVENT",
  "HKQuantityTypeIdentifierRestingHeartRate": "RESTING_HEART_RATE",
  "HKQuantityTypeIdentifierRespiratoryRate": "RESPIRATORY_RATE",
  "HKQuantityTypeIdentifierPeripheralPerfusionIndex":
      "PERIPHERAL_PERFUSION_INDEX",
  "HKQuantityTypeIdentifierStepCount": "STEPS",
  "HKQuantityTypeIdentifierWaistCircumference": "WAIST_CIRCUMFERENCE",
  "HKQuantityTypeIdentifierWalkingHeartRateAverage": "WALKING_HEART_RATE",
  "HKQuantityTypeIdentifierBodyMass": "WEIGHT",
  "HKQuantityTypeIdentifierFlightsClimbed": "FLIGHTS_CLIMBED",
  "HKQuantityTypeIdentifierDistanceWalkingRunning": "DISTANCE_WALKING_RUNNING",
  "HKQuantityTypeIdentifierDistanceSwimming": "DISTANCE_SWIMMING",
  "HKQuantityTypeIdentifierDistanceCycling": "DISTANCE_CYCLING",
  "HKCategoryTypeIdentifierMindfulSession": "MINDFULNESS",
  "HKCategoryTypeIdentifierSleepAnalysisAsleep": "SLEEP_ASLEEP",
  "HKCategoryTypeIdentifierSleepAnalysisAwake": "SLEEP_AWAKE",
  "HKCategoryTypeIdentifierSleepAnalysisDeep": "SLEEP_DEEP",
  "HKCategoryTypeIdentifierSleepAnalysisInBed": "SLEEP_IN_BED",
  "HKCategoryTypeIdentifierSleepAnalysisLight": "SLEEP_LIGHT",
  "HKCategoryTypeIdentifierSleepAnalysisREM": "SLEEP_REM",
  "HKQuantityTypeIdentifierWater": "WATER",
  "HKQuantityTypeIdentifierAppleExerciseTime": "EXERCISE_TIME",
  "HKWorkoutTypeIdentifier": "WORKOUT",
  "HKCategoryTypeIdentifierHeadacheNotPresent": "HEADACHE_NOT_PRESENT",
  "HKCategoryTypeIdentifierHeadacheMild": "HEADACHE_MILD",
  "HKCategoryTypeIdentifierHeadacheModerate": "HEADACHE_MODERATE",
  "HKCategoryTypeIdentifierHeadacheSevere": "HEADACHE_SEVERE",
  "HKCategoryTypeIdentifierHeadacheUnspecified": "HEADACHE_UNSPECIFIED",
  "HKCategoryTypeIdentifierElectrocardiogram": "ELECTROCARDIOGRAM",
  "HKQuantityTypeIdentifierNutrition": "NUTRITION",
  "HKCharacteristicTypeIdentifierBiologicalSex": "GENDER",
  "HKCharacteristicTypeIdentifierDateOfBirth": "BIRTH_DATE",
  "HKCharacteristicTypeIdentifierBloodType": "BLOOD_TYPE",
  "HKCategoryTypeIdentifierMenstrualFlow": "MENSTRUATION_FLOW",
};

const Map<String, String> unitHealthKitTypeMap = {
  "count":
      "COUNT", // For steps, flights climbed, body mass index, menstrual flow, headache types
  "count/min":
      "BEATS_PER_MINUTE", // For heart rate, resting heart rate, walking heart rate average
  "kcal":
      "KILOCALORIE", // For active energy burned, basal energy burned, dietary energy consumed
  "meter":
      "METER", // For distance walking/running, height, distance cycling, distance swimming
  "mmHg": "MILLIMETER_OF_MERCURY", // For blood pressure systolic, diastolic
  "percent":
      "PERCENT", // For oxygen saturation, body fat percentage, atrial fibrillation burden
  "degreeC": "DEGREE_CELSIUS", // For body temperature
  "min":
      "MINUTE", // For mindfulness, exercise time, sleep metrics, apple stand time
  "g":
      "GRAM", // For dietary metrics: carbs, fats, proteins, vitamins, fiber, etc.
  "mg/dL": "MILLIGRAM_PER_DECILITER", // For blood glucose
  "l/min": "RESPIRATIONS_PER_MINUTE", // For respiratory rate
  "ms": "MILLISECOND", // For heart rate variability SDNN
  "dBHL": "DECIBEL_HEARING_LEVEL", // For audiogram
  "siemen": "SIEMEN", // For electrodermal activity
  "L":
      "LITER", // For forced expiratory volume, water intake, and possibly dietary liquids
  "IU": "INTERNATIONAL_UNIT", // For insulin delivery
  "µg":
      "MICROGRAM", // For dietary vitamins, minerals, trace elements (e.g., selenium, iodine, folate)
  "mg":
      "MILLIGRAM", // For dietary minerals, vitamins (e.g., calcium, magnesium, iron)
  "volt": "VOLT", // For electrocardiogram
  "no_unit":
      "NO_UNIT", // For workout, gender, birth date, blood type, nutrition
  "hours":
      "HOUR", // For sleep metrics (asleep, awake, in bed, REM, light, deep)
  "km":
      "KILOMETER", // For large distance metrics (e.g., cycling, swimming, running)
  "µS": "MICROSIEMEN", // For electrodermal activity (alternative)
  "gender": "GENDER", // Biological sex
  "date": "DATE", // For date of birth
  "type": "BLOOD_TYPE", // For blood type
  "flow_type": "FLOW_TYPE", // For menstrual flow categories
  "ml/min":
      "MILLILITER_PER_MINUTE", // Added for completeness if required for volume flows
  "ml": "MILLILITER", // For liquid intake measurement
};
