class HRMonitoringConfigRemoteConfigModel {
  final int type;
  final int startHour;
  final int startMinute;
  final int endHour;
  final int endMinute;
  final Map<String, bool> weeks;
  final int intervalTime;

  HRMonitoringConfigRemoteConfigModel({
    required this.type,
    required this.startHour,
    required this.startMinute,
    required this.endHour,
    required this.endMinute,
    required this.weeks,
    required this.intervalTime,
  });

  factory HRMonitoringConfigRemoteConfigModel.fromJson(
      Map<String, dynamic> json) {
    return HRMonitoringConfigRemoteConfigModel(
      type: json['type'] ?? 1,
      startHour: json['startHour'] ?? 0,
      startMinute: json['startMinute'] ?? 0,
      endHour: json['endHour'] ?? 23,
      endMinute: json['endMinute'] ?? 59,
      weeks: Map<String, bool>.from(json['weeks'] ??
          {
            'sunday': true,
            'monday': true,
            'tuesday': true,
            'wednesday': true,
            'thursday': true,
            'friday': true,
            'saturday': true,
          }),
      intervalTime: json['intervalTime'] ?? 5,
    );
  }
}
