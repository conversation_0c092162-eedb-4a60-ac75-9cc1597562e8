class CombinedFirebaseModel {
  num? diastolicBloodPressure;
  bool? temperatureValid;
  num? hrv;
  num? systolicBloodPressure;
  num? bloodOxygen;
  num? respirationRate;
  num? startTimeStamp;
  num? heartRate;
  num? temperature;
  num? fat;
  num? step;
  num? cvrr;

  CombinedFirebaseModel(
      {this.diastolicBloodPressure,
      this.temperatureValid,
      this.hrv,
      this.systolicBloodPressure,
      this.bloodOxygen,
      this.respirationRate,
      this.startTimeStamp,
      this.heartRate,
      this.temperature,
      this.fat,
      this.step,
      this.cvrr});

  CombinedFirebaseModel.fromJson(Map<String, dynamic> json) {
    diastolicBloodPressure = json['diastolicBloodPressure'];
    temperatureValid = json['temperatureValid'];
    hrv = json['hrv'];
    systolicBloodPressure = json['systolicBloodPressure'];
    bloodOxygen = json['bloodOxygen'];
    respirationRate = json['respirationRate'];
    startTimeStamp = json['startTimeStamp'];
    heartRate = json['heartRate'];
    temperature = json['temperature'];
    fat = json['fat'];
    step = json['step'];
    cvrr = json['cvrr'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['diastolicBloodPressure'] = diastolicBloodPressure;
    data['temperatureValid'] = temperatureValid;
    data['hrv'] = hrv;
    data['systolicBloodPressure'] = systolicBloodPressure;
    data['bloodOxygen'] = bloodOxygen;
    data['respirationRate'] = respirationRate;
    data['startTimeStamp'] = startTimeStamp;
    data['heartRate'] = heartRate;
    data['temperature'] = temperature;
    data['fat'] = fat;
    data['step'] = step;
    data['cvrr'] = cvrr;
    return data;
  }
}
