import 'package:cloud_firestore/cloud_firestore.dart';

class StepFirebaseV2Model {
  num? calories;
  num? distance;
  num? step;
  Timestamp? vitalCollectedTimestamp;

  StepFirebaseV2Model({
    this.calories,
    this.distance,
    this.step,
    this.vitalCollectedTimestamp,
  });

  StepFirebaseV2Model.fromJson(Map<String, dynamic> json) {
    calories = json['calories'];
    distance = json['distance'];
    step = json['step'];
    vitalCollectedTimestamp = json['vitalCollectedTimestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['calories'] = calories;
    data['distance'] = distance;
    data['step'] = step;
    data['vitalCollectedTimestamp'] = vitalCollectedTimestamp;
    return data;
  }
}
