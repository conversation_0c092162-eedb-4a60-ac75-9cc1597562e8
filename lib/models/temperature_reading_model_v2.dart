import 'package:intl/intl.dart';

class TemperatureReadingModelV2 {
  final DateTime date;
  final double temperature;

  TemperatureReadingModelV2({
    required this.date,
    required this.temperature,
  });

  Map<String, dynamic> toMap() {
    return {
      'date': DateFormat('yyyy.MM.dd HH:mm:ss').format(date),
      'temperature': temperature,
    };
  }

  factory TemperatureReadingModelV2.fromMap(Map<String, dynamic> map) {
    if (map['date'] == null || map['temperature'] == null) {
      throw FormatException('Missing required fields in temperature reading');
    }
    return TemperatureReadingModelV2(
      date: _parseDate(map['date'].toString()),
      temperature: double.parse(map['temperature'].toString()),
    );
  }

  static DateTime _parseDate(String dateStr) {
    try {
      return DateFormat('yyyy.MM.dd HH:mm:ss').parse(dateStr);
    } catch (e) {
      throw FormatException('Invalid date format: $dateStr');
    }
  }

  static List<TemperatureReadingModelV2> parseReadings(dynamic input) {
    List<TemperatureReadingModelV2> readings = [];

    // Handle different input types
    if (input is Map) {
      if (input.containsKey('dataEnd') && input.containsKey('dicData')) {
        // This is Android format data
        var dicData = input['dicData'];
        if (dicData is List) {
          for (var item in dicData) {
            if (item is Map) {
              try {
                String? dateStr = item['date']?.toString();
                String? tempStr = item['temperature']?.toString();
                
                if (dateStr == null || tempStr == null) {
                  print('Missing required fields in temperature reading: $item');
                  continue;
                }
                
                readings.add(TemperatureReadingModelV2(
                  date: _parseDate(dateStr),
                  temperature: double.parse(tempStr),
                ));
              } catch (e) {
                print('Error parsing temperature item: $e');
                print('Problematic item: $item');
              }
            }
          }
          return readings;
        }
      }
      return []; // Empty list for empty or invalid map
    }
    
    // Original string-based parsing logic
    String inputStr = input.toString();
    if (inputStr.isEmpty) {
      return [];
    }

    // For iOS format with brackets
    if (inputStr.startsWith('[') && inputStr.endsWith(']')) {
      // Handle empty array case
      if (inputStr == '[]') {
        return [];
      }

      // Remove outer brackets and split into individual readings
      String cleaned = inputStr.substring(1, inputStr.length - 1).trim();

      // Handle case where there's no content between brackets
      if (cleaned.isEmpty) {
        return [];
      }

      List<String> readingsStr = cleaned.split('], [');
      List<TemperatureReadingModelV2> readings = [];

      for (String readingStr in readingsStr) {
        // Skip empty strings
        if (readingStr.trim().isEmpty) {
          continue;
        }

        try {
          // Clean up the string
          readingStr = readingStr.replaceAll('[', '').replaceAll(']', '');
          Map<String, dynamic> readingMap = {};

          // Split into key-value pairs
          List<String> pairs = readingStr.split(', ');
          for (String pair in pairs) {
            if (pair.trim().isEmpty) continue;

            List<String> keyValue = pair.split(': ');
            if (keyValue.length != 2) {
              throw FormatException('Invalid key-value pair format: $pair');
            }

            String key = keyValue[0].replaceAll('"', '');
            String value = keyValue[1].replaceAll('"', '');
            readingMap[key] = value;
          }

          // Verify required fields are present
          if (!readingMap.containsKey('date') || !readingMap.containsKey('temperature')) {
            print('Missing required fields in reading: $readingStr');
            continue;
          }

          readings.add(TemperatureReadingModelV2.fromMap(readingMap));
        } catch (e) {
          print('Error parsing reading: $readingStr');
          print('Error details: $e');
          continue;
        }
      }

      return readings;
    }
    
    // For non-bracketed data, try to parse directly if it looks like Android format
    print('Unrecognized data format for temperature readings: $inputStr');
    return [];
  }

  @override
  String toString() {
    return 'TemperatureReading(date: ${DateFormat('yyyy.MM.dd HH:mm:ss').format(date)}, temperature: $temperature)';
  }
}