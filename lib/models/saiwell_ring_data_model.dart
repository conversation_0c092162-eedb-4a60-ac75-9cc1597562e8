import 'dart:io';

class SaiwellRingDataModel {
  List<SaiwellRingDataModelData>? data;

  SaiwellRingDataModel({this.data});

  SaiwellRingDataModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <SaiwellRingDataModelData>[];
      json['data'].forEach((v) {
        data!.add(SaiwellRingDataModelData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SaiwellRingDataModelData {
  num? heartValue;
  num? hrvValue;
  num? cvrrValue;
  num? stepValue;
  num? dBPValue;
  num? bodyFatFloatValue;
  num? bloodSugarValue;
  num? oOValue;
  num? bodyFatIntValue;
  num? tempIntValue;
  num? tempFloatValue;
  num? startTime;
  num? sBPValue;
  num? respiratoryRateValue;
  bool? temperatureValid;

  SaiwellRingDataModelData(
      {this.heartValue,
      this.hrvValue,
      this.cvrrValue,
      this.stepValue,
      this.dBPValue,
      this.bodyFatFloatValue,
      this.bloodSugarValue,
      this.oOValue,
      this.bodyFatIntValue,
      this.tempIntValue,
      this.tempFloatValue,
      this.startTime,
      this.sBPValue,
      this.respiratoryRateValue,
      this.temperatureValid = false});

  SaiwellRingDataModelData.fromJson(Map<String, dynamic> json) {
    if (Platform.isAndroid) {
      heartValue = json['heartValue'];
      hrvValue = json['hrvValue'];
      cvrrValue = json['cvrrValue'];
      stepValue = json['stepValue'];
      dBPValue = json['DBPValue'];
      bodyFatFloatValue = json['bodyFatFloatValue'];
      bloodSugarValue = json['bloodSugarValue'];
      oOValue = json['OOValue'];
      bodyFatIntValue = json['bodyFatIntValue'];
      tempIntValue = json['tempIntValue'];
      tempFloatValue = json['tempFloatValue'];
      startTime = json['startTime'];
      sBPValue = json['SBPValue'];
      respiratoryRateValue = json['respiratoryRateValue'];
    } else {
      heartValue = json['heartRate'];
      hrvValue = json['hrv'];
      cvrrValue = json['cvrr'];
      stepValue = json['step'];
      dBPValue = json['diastolicBloodPressure'];
      bodyFatFloatValue = json['fat'];
      bloodSugarValue = -1;
      oOValue = json['bloodOxygen'];
      bodyFatIntValue = json['fat'];
      tempIntValue = json['temperature'];
      tempFloatValue = json['temperature'];
      startTime = json['startTimeStamp'];
      sBPValue = json['systolicBloodPressure'];
      respiratoryRateValue = json['respirationRate'];
      temperatureValid = json['temperatureValid'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['heartValue'] = heartValue;
    data['hrvValue'] = hrvValue;
    data['cvrrValue'] = cvrrValue;
    data['stepValue'] = stepValue;
    data['DBPValue'] = dBPValue;
    data['bodyFatFloatValue'] = bodyFatFloatValue;
    data['bloodSugarValue'] = bloodSugarValue;
    data['OOValue'] = oOValue;
    data['bodyFatIntValue'] = bodyFatIntValue;
    data['tempIntValue'] = tempIntValue;
    data['tempFloatValue'] = tempFloatValue;
    data['startTime'] = startTime;
    data['SBPValue'] = sBPValue;
    data['respiratoryRateValue'] = respiratoryRateValue;
    data['temperatureValid'] = temperatureValid;
    return data;
  }
}
