import 'dart:async';
import 'dart:io';
import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/models/db_enums.dart';
import 'package:SAiWELL/models/saiwell_ring_sleep_data_model.dart';
import 'package:SAiWELL/services/native_communicator.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/utils/common_methods.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';

enum HealthDataType {
  sleep("sleep", DBCollectionName.saiwellRingSleep, getSleepData),
  heartRate("heart_rate", null, getEmptyData),
  bloodPressure("blood_pressure", null, getEmptyData),
  lipidsUricAcid("comprehensive", null, getEmptyData);

  final String _type;
  final DBCollectionName? _dbCollectionName;
  final Future<List<Map<String, dynamic>>> Function(
      HealthDataType healthDataType) getFinalDataList;

  const HealthDataType(
      this._type, this._dbCollectionName, this.getFinalDataList);

  String getType() {
    return _type;
  }

  DBCollectionName? getDBCollectionName() {
    return _dbCollectionName;
  }
}

Future<List<Map<String, dynamic>>> getEmptyData(
    HealthDataType healthDataType) async {
  return [];
}

Future<List> getHealthData(HealthDataType healthDataType) async {
  NativeCommunicator nativeCommunicator = NativeCommunicator();
  List dataResponse = await nativeCommunicator.getHealthData(healthDataType);
  return dataResponse;
}

Future<List<Map<String, dynamic>>> getSleepData(
    HealthDataType healthDataType) async {
  List<Map<String, dynamic>> dataList = [];
  try {
    List response = await getHealthData(healthDataType);
    List<Map<String, dynamic>> convertedData =
        CommonMethods.convertObjectListToMapList(response);
    Map<String, dynamic> responseMap = {"data": convertedData};

    SaiwellRingSleepDataModel saiwellRingDataModel =
        SaiwellRingSleepDataModel.fromJson(responseMap);

    for (int i = 0; i < (saiwellRingDataModel.data?.length ?? 0); i++) {
      Map<String, dynamic> data = {
        'deepSleepCount': saiwellRingDataModel.data?[i].deepSleepCount ?? -1,
        'deepSleepMinutes':
            (saiwellRingDataModel.data?[i].deepSleepTotal ?? -1) ~/ 60,
        'deepSleepSeconds':
            (saiwellRingDataModel.data?[i].deepSleepTotal ?? -1),
        'endTimeStamp': (saiwellRingDataModel.data?[i].endTime ?? -1).toInt() ~/
            (Platform.isAndroid ? 1000 : 1),
        'lightSleepCount': saiwellRingDataModel.data?[i].lightSleepCount ?? -1,
        'lightSleepMinutes':
            (saiwellRingDataModel.data?[i].lightSleepTotal ?? -1) ~/ 60,
        'lightSleepSeconds':
            (saiwellRingDataModel.data?[i].lightSleepTotal ?? -1),
        'remSleepMinutes':
            (saiwellRingDataModel.data?[i].rapidEyeMovementTotal ?? -1) ~/ 60,
        'remSleepSeconds':
            saiwellRingDataModel.data?[i].rapidEyeMovementTotal ?? -1,
        'sleepDetailDatas': saiwellRingDataModel.data?[i].sleepData
            ?.map((e) => e.toJson())
            .toList(),
        'startTimeStamp':
            (saiwellRingDataModel.data?[i].startTime ?? -1).toInt() ~/
                (Platform.isAndroid ? 1000 : 1),
        'vitalCollectedTimestamp': Timestamp.fromMillisecondsSinceEpoch(
            (saiwellRingDataModel.data?[i].endTime?.toInt() ?? 0) *
                (Platform.isAndroid ? 1 : 1000)),
      };
      dataList.add(data);
    }
  } catch (e, stacktrace) {
    postSleepDataInErrorCollection([], e.toString(), stacktrace.toString());
  }
  return dataList;
}

FirebaseFirestore firestore = FirebaseFirestore.instanceFor(
    app: Firebase.app(), databaseId: fbDatabaseId);
PrefsService prefsService = PrefsService();
final Map<String, dynamic> _lastUpdatedTimestampMap = {
  'saiwellRingLastUpdated': Timestamp.now()
};

void postSleepDataInErrorCollection(
    List response, String? errorMsg, String? stacktrace) async {
  print("--- [postSleepDataInErrorCollection] adding sleep error data");
  await Future.delayed(const Duration(seconds: 2));
  String uid = await prefsService.getUid();
  DocumentReference userDocRef = firestore.collection("RingLogs").doc(uid);

  DocumentSnapshot docSnapshot = await userDocRef.get();
  WriteBatch batch = firestore.batch();
  int timestamp = DateTime.now().millisecondsSinceEpoch ~/ (1000);
  if (!docSnapshot.exists) {
    batch.set(userDocRef, _lastUpdatedTimestampMap);
  } else {
    batch.update(userDocRef, _lastUpdatedTimestampMap);
  }

  CollectionReference saiwellRingDataRef = userDocRef.collection("SLEEP_ERROR");
  batch.set(saiwellRingDataRef.doc(timestamp.toString()),
      {'rawData': response, 'error': errorMsg, 'stacktrace': stacktrace});
  await batch.commit();
  print("--- [postSleepDataInErrorCollection] adding sleep error data -- 2");
}

void postSportDataInErrorCollection(List response, String? errorMsg) async {
  print("---[postSportDataInErrorCollection] adding sport error data");
  await Future.delayed(const Duration(seconds: 2));
  String uid = await prefsService.getUid();
  DocumentReference userDocRef = firestore.collection("RingLogs").doc(uid);

  DocumentSnapshot docSnapshot = await userDocRef.get();
  WriteBatch batch = firestore.batch();
  int timestamp = DateTime.now().millisecondsSinceEpoch ~/ (1000);
  if (!docSnapshot.exists) {
    batch.set(userDocRef, _lastUpdatedTimestampMap);
  } else {
    batch.update(userDocRef, _lastUpdatedTimestampMap);
  }

  CollectionReference saiwellRingDataRef = userDocRef.collection("SPORT_ERROR");
  batch.set(saiwellRingDataRef.doc(timestamp.toString()),
      {'rawData': response, 'error': errorMsg});
  await batch.commit();
  print("---[postSportDataInErrorCollection] adding sport error data -- 2");
}

void postCombinedDataInErrorCollection(List response, String? errorMsg) async {
  print("---[postCombinedDataInErrorCollection] adding combined error data");
  await Future.delayed(const Duration(seconds: 2));
  String uid = await prefsService.getUid();
  DocumentReference userDocRef = firestore.collection("RingLogs").doc(uid);

  DocumentSnapshot docSnapshot = await userDocRef.get();
  WriteBatch batch = firestore.batch();
  int timestamp = DateTime.now().millisecondsSinceEpoch ~/ (1000);
  if (!docSnapshot.exists) {
    batch.set(userDocRef, _lastUpdatedTimestampMap);
  } else {
    batch.update(userDocRef, _lastUpdatedTimestampMap);
  }

  CollectionReference saiwellRingDataRef =
      userDocRef.collection("COMBINED_ERROR");
  batch.set(saiwellRingDataRef.doc(timestamp.toString()),
      {'rawData': response, 'error': errorMsg});
  await batch.commit();
  print(
      "---[postCombinedDataInErrorCollection] adding combined error data -- 2");
}
