

import 'package:cloud_firestore/cloud_firestore.dart';

class HrvFirebaseV2Model {
  num? diastolicBloodPressure;
  num? heartRate;
  num? hrv;
  num? stress;
  num? systolicBloodPressure;
  Timestamp? vitalCollectedTimestamp;

  HrvFirebaseV2Model({
    this.diastolicBloodPressure,
    this.heartRate,
    this.hrv,
    this.stress,
    this.systolicBloodPressure,
    this.vitalCollectedTimestamp,
  });

  HrvFirebaseV2Model.fromJson(Map<String, dynamic> json) {
    diastolicBloodPressure = json['diastolicBloodPressure'];
    heartRate = json['heartRate'];
    hrv = json['hrv'];
    stress = json['stress'];
    systolicBloodPressure = json['systolicBloodPressure'];
    vitalCollectedTimestamp = json['vitalCollectedTimestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['diastolicBloodPressure'] = diastolicBloodPressure;
    data['heartRate'] = heartRate;
    data['hrv'] = hrv;
    data['stress'] = stress;
    data['systolicBloodPressure'] = systolicBloodPressure;
    data['vitalCollectedTimestamp'] = vitalCollectedTimestamp;
    return data;
  }
}
