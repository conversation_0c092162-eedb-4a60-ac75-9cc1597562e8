

import 'package:cloud_firestore/cloud_firestore.dart';

class OxygenFirebaseV2Model {
  num? bloodOxygen;
  Timestamp? vitalCollectedTimestamp;

  OxygenFirebaseV2Model({
    this.bloodOxygen,
    this.vitalCollectedTimestamp,
  });

  OxygenFirebaseV2Model.fromJson(Map<String, dynamic> json) {
    bloodOxygen = json['bloodOxygen'];
    vitalCollectedTimestamp = json['vitalCollectedTimestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['bloodOxygen'] = bloodOxygen;
    data['vitalCollectedTimestamp'] = vitalCollectedTimestamp;
    return data;
  }
}
