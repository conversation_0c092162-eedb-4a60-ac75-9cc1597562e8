class NavigationModel {
  String? baseUrl;
  List<Navigation>? navigation;

  NavigationModel({this.baseUrl, this.navigation});

  NavigationModel.fromJson(Map<String, dynamic> json) {
    baseUrl = json['baseUrl'];
    if (json['navigation'] != null) {
      navigation = <Navigation>[];
      json['navigation'].forEach((v) {
        navigation!.add(Navigation.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['baseUrl'] = baseUrl;
    if (navigation != null) {
      data['navigation'] = navigation!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Navigation {
  String? label;
  String? value;
  bool? isForNavbar;

  Navigation({this.label, this.value, this.isForNavbar});

  Navigation.fromJson(Map<String, dynamic> json) {
    label = json['label'];
    value = json['value'];
    isForNavbar = json['isForNavbar'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['label'] = label;
    data['value'] = value;
    data['isForNavbar'] = isForNavbar;
    return data;
  }
}
