import 'package:intl/intl.dart';

class BloodOxygenReadingModelV2 {
  final DateTime date;
  final int automaticSpo2Data;

  BloodOxygenReadingModelV2({
    required this.date,
    required this.automaticSpo2Data,
  });

  // Convert the model to a Map
  Map<String, dynamic> toMap() {
    return {
      'date': DateFormat('yyyy.MM.dd HH:mm:ss').format(date),
      'automaticSpo2Data': automaticSpo2Data,
    };
  }

  // Create a model from a Map
  factory BloodOxygenReadingModelV2.fromMap(Map<String, dynamic> map) {
    if (map['date'] == null || (map['automaticSpo2Data'] == null && map['Blood_oxygen'] == null)) {
      throw const FormatException('Missing required fields in blood oxygen reading');
    }
    
    // Use 'Blood_oxygen' field if 'automaticSpo2Data' is missing
    String spo2Field = map['automaticSpo2Data'] != null ? 'automaticSpo2Data' : 'Blood_oxygen';
    
    return BloodOxygenReadingModelV2(
      date: _parseDate(map['date'].toString()),
      automaticSpo2Data: int.parse(map[spo2Field].toString()),
    );
  }

  // Helper method to parse date string
  static DateTime _parseDate(String dateStr) {
    try {
      return DateFormat('yyyy.MM.dd HH:mm:ss').parse(dateStr);
    } catch (e) {
      try {
        // Try an alternative format that might be used
        return DateFormat('yyyy.MM.dd HH:mm').parse(dateStr);
      } catch (e) {
        throw FormatException('Invalid date format: $dateStr');
      }
    }
  }

  static List<BloodOxygenReadingModelV2> parseReadings(dynamic input) {
    List<BloodOxygenReadingModelV2> readings = [];

    // Handle Android List<Map> format - This is the working case for Android
    if (input is List) {
      for (var item in input) {
        if (item is Map) {
          try {
            String? dateStr = item['date']?.toString();
            String? spo2Value = item['Blood_oxygen']?.toString();
            
            if (dateStr != null && spo2Value != null) {
              readings.add(BloodOxygenReadingModelV2(
                date: _parseDate(dateStr),
                automaticSpo2Data: int.parse(spo2Value),
              ));
            }
          } catch (e) {
            print("Error processing List item: $e");
          }
        }
      }
      
      if (readings.isNotEmpty) {
        return readings;
      }
    }
    
    // Original iOS string-based parsing logic
    String inputStr = input.toString();
    if (inputStr.isEmpty || inputStr == '[]') {
      return [];
    }

    // Remove outer brackets and split into individual readings
    String cleaned = inputStr.trim();
    if (cleaned.startsWith('[')) {
      cleaned = cleaned.substring(1);
    }
    if (cleaned.endsWith(']')) {
      cleaned = cleaned.substring(0, cleaned.length - 1);
    }

    // If empty after cleaning, return empty list
    if (cleaned.isEmpty) {
      return [];
    }

    List<String> readingsStr = cleaned.split('], [');

    for (String readingStr in readingsStr) {
      // Clean up the string and create a map
      readingStr = readingStr.replaceAll('[', '').replaceAll(']', '');
      if (readingStr.isEmpty) continue;

      Map<String, dynamic> readingMap = {};

      // Split into key-value pairs
      List<String> pairs = readingStr.split(', ');
      for (String pair in pairs) {
        if (pair.isEmpty) continue;

        List<String> keyValue = pair.split(': ');
        // Check if we have both key and value
        if (keyValue.length != 2) continue;

        String key = keyValue[0].replaceAll('"', '');
        String value = keyValue[1];
        readingMap[key] = value;
      }

      // Only add if we have all required fields
      if (readingMap.containsKey('date') && 
          (readingMap.containsKey('automaticSpo2Data') || readingMap.containsKey('Blood_oxygen'))) {
        try {
          // Handle case where data comes with 'Blood_oxygen' field (Android format)
          if (readingMap.containsKey('Blood_oxygen') && !readingMap.containsKey('automaticSpo2Data')) {
            readingMap['automaticSpo2Data'] = readingMap['Blood_oxygen'];
          }
          readings.add(BloodOxygenReadingModelV2.fromMap(readingMap));
        } catch (e) {
          print('Error parsing reading: $e');
          continue;
        }
      } else {
        print('Missing required fields in reading: $readingMap');
      }
    }

    return readings;
  }

  @override
  String toString() {
    return 'BloodOxygenReading(date: ${DateFormat('yyyy.MM.dd HH:mm:ss').format(date)}, automaticSpo2Data: $automaticSpo2Data)';
  }
}
