class SaiwellRingDeviceDataModel {
  num? devicetBindState;
  num? devicetSyncState;
  num? deviceSubVersion;
  num? deviceMainVersion;
  num? hardwareType;
  num? bloodAlgoMainVersion;
  String? deviceVersion;
  num? deviceBatteryValue;
  num? deviceId;
  num? bleAgreementSubVersion;
  num? tpSubVersion;
  num? bleAgreementMainVersion;
  num? deviceBatteryState;
  num? tpMainVersion;
  num? bloodAlgoSubVersion;
  num? uiMainVersion;
  num? uiSubVersion;
  num? bloodSugarSubVersion;
  num? bloodSugarMainVersion;
  String? macId;

  SaiwellRingDeviceDataModel(
      {this.devicetBindState,
      this.devicetSyncState,
      this.deviceSubVersion,
      this.deviceMainVersion,
      this.hardwareType,
      this.bloodAlgoMainVersion,
      this.deviceVersion,
      this.deviceBatteryValue,
      this.deviceId,
      this.bleAgreementSubVersion,
      this.tpSubVersion,
      this.bleAgreementMainVersion,
      this.deviceBatteryState,
      this.tpMainVersion,
      this.bloodAlgoSubVersion,
      this.uiMainVersion,
      this.uiSubVersion,
      this.bloodSugarSubVersion,
      this.bloodSugarMainVersion,
      this.macId});

  SaiwellRingDeviceDataModel.fromJson(Map<String, dynamic> json) {
    devicetBindState = json['devicetBindState'];
    devicetSyncState = json['devicetSyncState'];
    deviceSubVersion = json['deviceSubVersion'];
    deviceMainVersion = json['deviceMainVersion'];
    hardwareType = json['hardwareType'];
    bloodAlgoMainVersion = json['bloodAlgoMainVersion'];
    deviceVersion = json['deviceVersion'];
    deviceBatteryValue = json['deviceBatteryValue'];
    deviceId = json['deviceId'];
    bleAgreementSubVersion = json['bleAgreementSubVersion'];
    tpSubVersion = json['tpSubVersion'];
    bleAgreementMainVersion = json['bleAgreementMainVersion'];
    deviceBatteryState = json['deviceBatteryState'];
    tpMainVersion = json['tpMainVersion'];
    bloodAlgoSubVersion = json['bloodAlgoSubVersion'];
    uiMainVersion = json['uiMainVersion'];
    uiSubVersion = json['uiSubVersion'];
    bloodSugarSubVersion = json['bloodSugarSubVersion'];
    bloodSugarMainVersion = json['bloodSugarMainVersion'];
    macId = json["macId"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['devicetBindState'] = devicetBindState;
    data['devicetSyncState'] = devicetSyncState;
    data['deviceSubVersion'] = deviceSubVersion;
    data['deviceMainVersion'] = deviceMainVersion;
    data['hardwareType'] = hardwareType;
    data['bloodAlgoMainVersion'] = bloodAlgoMainVersion;
    data['deviceVersion'] = deviceVersion;
    data['deviceBatteryValue'] = deviceBatteryValue;
    data['deviceId'] = deviceId;
    data['bleAgreementSubVersion'] = bleAgreementSubVersion;
    data['tpSubVersion'] = tpSubVersion;
    data['bleAgreementMainVersion'] = bleAgreementMainVersion;
    data['deviceBatteryState'] = deviceBatteryState;
    data['tpMainVersion'] = tpMainVersion;
    data['bloodAlgoSubVersion'] = bloodAlgoSubVersion;
    data['uiMainVersion'] = uiMainVersion;
    data['uiSubVersion'] = uiSubVersion;
    data['bloodSugarSubVersion'] = bloodSugarSubVersion;
    data['bloodSugarMainVersion'] = bloodSugarMainVersion;
    return data;
  }
}
