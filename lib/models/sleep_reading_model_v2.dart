import 'package:intl/intl.dart';

class SleepReadingModelV2 {
  final DateTime startTime;
  final int totalSleepTime;
  final List<int> sleepQuality;
  final int sleepUnitLength;
  late final List<SleepQualityGroupData> sleepQualityGroups;

  late final int deepSleepCount;
  late final int lightSleepCount;
  late final int remSleepCount;
  late final int awakeCount;
  late final int otherSleepCount;

  SleepReadingModelV2({
    required this.startTime,
    required this.totalSleepTime,
    required this.sleepQuality,
    required this.sleepUnitLength,
  }) {
    _calculateSleepStages();
    _calculateSleepGroups();
  }

  DateTime get endTime => startTime.add(Duration(minutes: totalSleepTime));

  String getSleepQualityGroupsJson() {
    try {
      return '[${sleepQualityGroups.map((group) => group.toJson()).join(',')}]';
    } catch (e) {
      print('Error converting sleep quality groups to JSON: $e');
      return '[]';
    }
  }

  void _calculateSleepStages() {
    try {
      deepSleepCount = sleepQuality.where((value) => value == 1).length;
      lightSleepCount = sleepQuality.where((value) => value == 2).length;
      remSleepCount = sleepQuality.where((value) => value == 3).length;
      otherSleepCount = sleepQuality.where((value) => value == 4).length;
      awakeCount = sleepQuality.where((value) => value == 5).length;
    } catch (e) {
      print('Error calculating sleep stages: $e');
      deepSleepCount =
          lightSleepCount = remSleepCount = otherSleepCount = awakeCount = 0;
    }
  }

  void _calculateSleepGroups() {
    sleepQualityGroups = [];
    if (sleepQuality.isEmpty) return;

    int currentType = sleepQuality[0];
    int currentCount = 1;
    int currentStartTime = startTime.millisecondsSinceEpoch ~/ 1000;

    for (int i = 1; i < sleepQuality.length; i++) {
      if (sleepQuality[i] != currentType) {
        sleepQualityGroups.add(SleepQualityGroupData(
          duration: currentCount * 60,
          sleepType: currentType,
          startTimeStamp: currentStartTime,
        ));

        currentType = sleepQuality[i];
        currentStartTime += (currentCount * sleepUnitLength * 60);
        currentCount = 1;
      } else {
        currentCount++;
      }
    }

    // Add the last group
    sleepQualityGroups.add(SleepQualityGroupData(
      duration: currentCount * 60,
      sleepType: currentType,
      startTimeStamp: currentStartTime,
    ));
  }

  static List<SleepReadingModelV2> parseReadings(dynamic input) {
    List<SleepReadingModelV2> readings = [];

    // Handle Android format (Map with dicData)
    if (input is Map && input.containsKey('dataEnd') && input.containsKey('dicData')) {
      var dicData = input['dicData'];
      
      // If no sleep data is found, return empty list
      if (dicData is! List || dicData.isEmpty) {
        print('Empty sleep data received from Android');
        return readings;
      }
      
      print('Sleep data from Android: $dicData');
      
      // Process Android sleep data format
      try {
        for (var sleepEntry in dicData) {
          if (sleepEntry is Map && 
              sleepEntry.containsKey('date') && 
              sleepEntry.containsKey('sleepUnitLength') && 
              sleepEntry.containsKey('arraySleepQuality')) {
              
            String dateStr = sleepEntry['date'];
            int unitLength = int.tryParse(sleepEntry['sleepUnitLength']) ?? 1;
            
            // Parse arraySleepQuality - it's a string of space-separated numbers
            String qualityStr = sleepEntry['arraySleepQuality'];
            List<int> qualityArray = qualityStr
                .split(' ')
                .map((s) => s.trim())
                .where((s) => s.isNotEmpty)
                .map((s) => int.tryParse(s) ?? 0)
                .where((value) => value >= 1 && value <= 5)
                .toList();
                
            if (qualityArray.isEmpty) {
              print('Empty sleep quality array');
              continue;
            }
            
            // Calculate total sleep time from the number of quality readings
            int totalSleepTime = qualityArray.length * unitLength;
            
            // Parse start time from the date string
            DateTime startTime;
            try {
              startTime = DateTime.parse(dateStr);
            } catch (e) {
              print('Error parsing date: $e');
              continue;
            }
            
            readings.add(SleepReadingModelV2(
              startTime: startTime,
              totalSleepTime: totalSleepTime,
              sleepQuality: qualityArray,
              sleepUnitLength: unitLength,
            ));
          }
        }
      } catch (e) {
        print('Error processing Android sleep data: $e');
      }
      
      return readings;
    }
    
    // Handle iOS format (String)
    String inputStr = input.toString();
    if (inputStr.isEmpty) {
      print('Empty input received');
      return readings;
    }

    try {
      // Clean up the input string
      inputStr = inputStr.replaceAll('\n', ' ').trim();

      // Split input into individual records
      RegExp recordRegex = RegExp(r'\[(.*?)\](?=\s*,\s*\[|\s*$)');
      Iterable<RegExpMatch> records = recordRegex.allMatches(inputStr);

      for (var record in records) {
        String recordStr = record.group(1) ?? '';

        // Extract data using regex patterns
        RegExp dateRegex = RegExp(r'"startTime_SleepData":\s*([\d.:\s]+)');
        RegExp unitLengthRegex = RegExp(r'"sleepUnitLength":\s*(\d+)');
        RegExp totalTimeRegex = RegExp(r'"totalSleepTime":\s*(\d+)');
        RegExp qualityRegex =
            RegExp(r'<__NSArrayM\s+0x[0-9a-fA-F]+>\(([\d,\s]+)\)');

        String? dateStr = dateRegex.firstMatch(recordStr)?.group(1)?.trim();
        String? unitLengthStr =
            unitLengthRegex.firstMatch(recordStr)?.group(1)?.trim();
        String? totalTimeStr =
            totalTimeRegex.firstMatch(recordStr)?.group(1)?.trim();
        String? qualityStr =
            qualityRegex.firstMatch(recordStr)?.group(1)?.trim();

        if (dateStr == null || unitLengthStr == null || 
            totalTimeStr == null || qualityStr == null) {
          print('Missing required fields in sleep reading: $recordStr');
          continue;
        }

        try {
          // Parse sleep quality array
          List<int> qualityArray = qualityStr
              .split(',')
              .map((s) => s.trim())
              .where((s) => s.isNotEmpty)
              .map((s) => int.parse(s))
              .where((value) => value >= 1 && value <= 5)
              .toList();

          // Parse and validate total sleep time
          int totalTime = int.parse(totalTimeStr);
          if (totalTime < 0) {
            print('Invalid total sleep time: $totalTime');
            continue;
          }

          // Parse and validate unit length
          int unitLength = int.parse(unitLengthStr);
          if (unitLength <= 0) {
            print('Invalid sleep unit length: $unitLength');
            continue;
          }

          // Parse date and create model
          DateTime startTime = _parseDate(dateStr);
          readings.add(SleepReadingModelV2(
            startTime: startTime,
            totalSleepTime: totalTime,
            sleepQuality: qualityArray,
            sleepUnitLength: unitLength,
          ));
        } catch (e) {
          print('Error processing record: $e');
          print('Problematic record: $recordStr');
          continue;
        }
      }
    } catch (e) {
      print('Error parsing sleep data: $e');
    }

    return readings;
  }

  static DateTime _parseDate(String dateStr) {
    try {
      dateStr = dateStr.trim();
      if (dateStr.contains('.')) {
        List<String> parts = dateStr.split(' ');
        if (parts.length != 2)
          throw const FormatException('Invalid date format');
        String datePart = parts[0].replaceAll('.', '-');
        return DateTime.parse('$datePart ${parts[1]}');
      }
      return DateTime.parse(dateStr);
    } catch (e) {
      throw FormatException('Invalid date format: $dateStr');
    }
  }

  @override
  String toString() {
    final dateFormat = DateFormat('yyyy.MM.dd HH:mm:ss');
    try {
      return 'SleepModelV2(startTime: ${dateFormat.format(startTime)}, '
          'endTime: ${dateFormat.format(endTime)}, '
          'totalSleepTime: $totalSleepTime, '
          'sleepQuality: ${sleepQuality.length} readings, '
          'sleepUnitLength: $sleepUnitLength, '
          'deepSleep: $deepSleepCount minutes, '
          'lightSleep: $lightSleepCount minutes, '
          'remSleep: $remSleepCount minutes, '
          'awake: $awakeCount minutes, '
          'groups: ${sleepQualityGroups.length})';
    } catch (e) {
      return 'SleepModelV2(Error formatting toString: $e)';
    }
  }
}

class SleepQualityGroupData {
  final int duration;
  final int sleepType;
  final int startTimeStamp;

  SleepQualityGroupData({
    required this.duration,
    required this.sleepType,
    required this.startTimeStamp,
  });

  Map<String, dynamic> toJson() => {
        'duration': duration,
        'sleepType': sleepType,
        'startTimeStamp': startTimeStamp,
      };

  @override
  String toString() {
    return toJson().toString();
  }
}
