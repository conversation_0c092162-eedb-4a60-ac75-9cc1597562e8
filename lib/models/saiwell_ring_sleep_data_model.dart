import 'dart:io';

class SaiwellRingSleepDataModel {
  List<Data>? data;

  SaiwellRingSleepDataModel({this.data});

  SaiwellRingSleepDataModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  num? wakeCount;
  num? deepSleepCount;
  num? rapidEyeMovementTotal;
  num? lightSleepCount;
  num? wakeDuration;
  List<SleepData>? sleepData;
  num? startTime;
  num? lightSleepTotal;
  num? endTime;
  num? deepSleepTotal;

  Data(
      {this.wakeCount,
      this.deepSleepCount,
      this.rapidEyeMovementTotal,
      this.lightSleepCount,
      this.wakeDuration,
      this.sleepData,
      this.startTime,
      this.lightSleepTotal,
      this.endTime,
      this.deepSleepTotal});

  Data.fromJson(Map<String, dynamic> json) {
    if (Platform.isAndroid) {
      wakeCount = json['wakeCount'];
      deepSleepCount = json['deepSleepCount'];
      rapidEyeMovementTotal = json['rapidEyeMovementTotal'];
      lightSleepCount = json['lightSleepCount'];
      wakeDuration = json['wakeDuration'];
      if (json['sleepData'] != null && json['sleepData'] is List) {
        sleepData = <SleepData>[];
        json['sleepData'].forEach((v) {
          if (v is Map<String, dynamic>) {
            sleepData!.add(SleepData.fromJson(v));
          } else if (v is Map) {
            Map<String, dynamic> recoveredData =
                v.map((key, value) => MapEntry(key.toString(), value));
            sleepData!.add(SleepData.fromJson(recoveredData));
          } else {
            print('Invalid data format in sleepData: $v');
          }
        });
      }

      startTime = json['startTime'];
      lightSleepTotal = json['lightSleepTotal'];
      endTime = json['endTime'];
      deepSleepTotal = json['deepSleepTotal'];
    } else {
      wakeCount = json['wakeCount'];
      deepSleepCount = json['deepSleepCount'];
      rapidEyeMovementTotal = (json['remSleepMinutes'] ?? 0) * 60;
      lightSleepCount = json['lightSleepCount'];
      wakeDuration = json['wakeDuration'];
      if (json['sleepDetailDatas'] != null &&
          json['sleepDetailDatas'] is List) {
        sleepData = <SleepData>[];
        json['sleepDetailDatas'].forEach((v) {
          if (v is Map<String, dynamic>) {
            sleepData!.add(SleepData.fromJson(v));
          } else if (v is Map) {
            Map<String, dynamic> recoveredData =
                v.map((key, value) => MapEntry(key.toString(), value));
            sleepData!.add(SleepData.fromJson(recoveredData));
          } else {
            print('Invalid data format in sleepData: $v');
          }
        });
      }

      startTime = json['startTimeStamp'];
      lightSleepTotal = (json['lightSleepMinutes'] ?? 0) * 60;
      endTime = json['endTimeStamp'];
      deepSleepTotal = (json['deepSleepMinutes'] ?? 0) * 60;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['wakeCount'] = wakeCount;
    data['deepSleepCount'] = deepSleepCount;
    data['rapidEyeMovementTotal'] = rapidEyeMovementTotal;
    data['lightSleepCount'] = lightSleepCount;
    data['wakeDuration'] = wakeDuration;
    if (sleepData != null) {
      data['sleepData'] = sleepData!.map((v) => v.toJson()).toList();
    }
    data['startTime'] = startTime;
    data['lightSleepTotal'] = lightSleepTotal;
    data['endTime'] = endTime;
    data['deepSleepTotal'] = deepSleepTotal;
    return data;
  }
}

class SleepData {
  num? sleepStartTime;
  num? sleepLen;
  num? sleepType;

  SleepData({this.sleepStartTime, this.sleepLen, this.sleepType});

  SleepData.fromJson(Map<String, dynamic> json) {
    if (Platform.isAndroid) {
      sleepStartTime = json['sleepStartTime'];
      sleepLen = json['sleepLen'];
      sleepType = json['sleepType'];
    } else {
      sleepStartTime = json['startTimeStamp'];
      sleepLen = json['duration'];
      sleepType = json['sleepType'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['startTimeStamp'] =
        (sleepStartTime ?? 0) ~/ (Platform.isAndroid ? 1000 : 1);
    data['duration'] = sleepLen;
    data['sleepType'] = sleepType;
    return data;
  }
}
