import 'package:intl/intl.dart';

class StepsReadingModelV2 {
  final DateTime date;
  final List<int> arraySteps;
  final double calories;
  final double distance;
  final int step;

  StepsReadingModelV2({
    required this.date,
    required this.arraySteps,
    required this.calories,
    required this.distance,
    required this.step,
  });

  Map<String, dynamic> toMap() {
    return {
      'date': DateFormat('yyyy.MM.dd HH:mm:ss').format(date),
      'arraySteps': arraySteps,
      'calories': calories,
      'distance': distance,
      'step': step,
    };
  }

  factory StepsReadingModelV2.fromMap(Map<String, dynamic> map) {
    // Check for required fields
    final requiredFields = ['date', 'arraySteps', 'calories', 'distance', 'step'];
    for (String field in requiredFields) {
      if (map[field] == null) {
        throw FormatException('Missing required field: $field in steps reading');
      }
    }

    // Handle arraySteps parsing
    List<int> parseArraySteps(dynamic arrayStepsInput) {
      if (arrayStepsInput is String) {
        // Parse NSArray format: <__NSArrayM 0x3010964c0>(14,0,0,0,0,0,0,0,0,0)
        String cleaned = arrayStepsInput
            .replaceAll(RegExp(r'<__NSArrayM 0x[0-9a-f]+>\('), '')
            .replaceAll(')', '');
        return cleaned
            .split(',')
            .map((s) => int.parse(s.trim()))
            .toList();
      } else if (arrayStepsInput is List) {
        return arrayStepsInput
            .map((e) => int.parse(e.toString()))
            .toList();
      }
      throw FormatException('Invalid arraySteps format: $arrayStepsInput');
    }

    return StepsReadingModelV2(
      date: _parseDate(map['date'].toString()),
      arraySteps: parseArraySteps(map['arraySteps']),
      calories: double.parse(map['calories'].toString()),
      distance: double.parse(map['distance'].toString()),
      step: int.parse(map['step'].toString()),
    );
  }

  static DateTime _parseDate(String dateStr) {
    try {
      // Try parsing with time
      return DateFormat('yyyy.MM.dd HH:mm:ss').parse(dateStr);
    } catch (e) {
      try {
        // Fallback to date-only format
        return DateFormat('yyyy.MM.dd').parse(dateStr);
      } catch (e) {
        throw FormatException('Invalid date format: $dateStr');
      }
    }
  }

  static List<StepsReadingModelV2> parseReadings(dynamic input) {
    List<StepsReadingModelV2> readings = [];

    // Handle different input types
    if (input is Map) {
      if (input.containsKey('dataEnd') && input.containsKey('dicData')) {
        // This is Android format data
        var dicData = input['dicData'];
        if (dicData is List) {
          for (var item in dicData) {
            if (item is Map) {
              try {
                // Check for required fields
                String? dateStr = item['date']?.toString();
                
                // For Android, the step key can be either 'step' or 'detailMinterStep'
                String? stepStr = item['step']?.toString();
                stepStr ??= item['detailMinterStep']?.toString();
                
                String? distanceStr = item['distance']?.toString();
                String? caloriesStr = item['calories']?.toString();
                
                if (dateStr == null || stepStr == null || 
                    distanceStr == null || caloriesStr == null) {
                  print('Missing required fields in steps reading: $item');
                  continue;
                }
                
                readings.add(StepsReadingModelV2(
                  date: _parseDate(dateStr),
                  arraySteps: [], // Android doesn't provide arraySteps
                  calories: double.parse(caloriesStr),
                  distance: double.parse(distanceStr),
                  step: int.parse(stepStr),
                ));
              } catch (e) {
                print('Error parsing step item: $e');
                print('Problematic item: $item');
              }
            }
          }
          return readings;
        }
      }
      return []; // Empty list for empty or invalid map
    }
    
    // Handle string input (for iOS)
    String inputStr = input.toString();
    if (inputStr.isEmpty) return [];

    // Remove outer brackets and split into individual readings
    String cleaned = inputStr.trim();
    if (cleaned.startsWith('[')) {
      cleaned = cleaned.substring(1);
    }
    if (cleaned.endsWith(']')) {
      cleaned = cleaned.substring(0, cleaned.length - 1);
    }

    if (cleaned.isEmpty) return [];

    List<String> readingsStr = cleaned.split('], [');

    for (String readingStr in readingsStr) {
      try {
        // Clean up the string
        readingStr = readingStr.replaceAll('[', '').replaceAll(']', '');
        Map<String, dynamic> readingMap = {};

        // Split into key-value pairs
        List<String> pairs = readingStr.split(', ');
        for (String pair in pairs) {
          if (pair.isEmpty) continue;

          List<String> keyValue = pair.split(': ');
          if (keyValue.length != 2) continue;

          String key = keyValue[0].replaceAll('"', '');
          String value = keyValue[1].replaceAll('"', '');
          readingMap[key] = value;
        }

        // Verify required fields
        final requiredFields = ['date', 'arraySteps', 'calories', 'distance', 'step'];
        for (String field in requiredFields) {
          if (!readingMap.containsKey(field)) {
            print('Missing required field: $field in reading: $readingStr');
            continue;
          }
        }

        readings.add(StepsReadingModelV2.fromMap(readingMap));
      } catch (e) {
        print('Error parsing reading: $readingStr');
        print('Error details: $e');
        continue;
      }
    }

    return readings;
  }

  @override
  String toString() {
    return 'StepsReading(date: ${DateFormat('yyyy.MM.dd HH:mm:ss').format(date)}, '
        'arraySteps: $arraySteps, calories: $calories, '
        'distance: $distance, step: $step)';
  }
}
