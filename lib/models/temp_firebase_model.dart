import 'package:cloud_firestore/cloud_firestore.dart';

class TempFirebaseModel {
  num? temperature;
  Timestamp? vitalCollectedTimestamp;

  TempFirebaseModel({
    this.temperature,
    this.vitalCollectedTimestamp,
  });

  TempFirebaseModel.fromJson(Map<String, dynamic> json) {
    temperature = json['temperature'];
    vitalCollectedTimestamp = json['vitalCollectedTimestamp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['temperature'] = temperature;
    data['vitalCollectedTimestamp'] = vitalCollectedTimestamp;
    return data;
  }
}
