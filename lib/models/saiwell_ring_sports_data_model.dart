import 'dart:io';

class SaiwellRingSportsDataModel {
  List<Data>? data;

  SaiwellRingSportsDataModel({this.data});

  SaiwellRingSportsDataModel.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? sportEndTime;
  int? sportStep;
  int? sportDistance;
  int? sportStartTime;
  int? sportCalorie;

  Data(
      {this.sportEndTime,
      this.sportStep,
      this.sportDistance,
      this.sportStartTime,
      this.sportCalorie});

  Data.fromJson(Map<String, dynamic> json) {
    if (Platform.isAndroid) {
      sportEndTime = json['sportEndTime'];
      sportStep = json['sportStep'];
      sportDistance = json['sportDistance'];
      sportStartTime = json['sportStartTime'];
      sportCalorie = json['sportCalorie'];
    } else {
      sportEndTime = json['endTimeStamp'];
      sportStep = json['step'];
      sportDistance = json['distance'];
      sportStartTime = json['startTimeStamp'];
      sportCalorie = json['calories'];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['sportEndTime'] = sportEndTime;
    data['sportStep'] = sportStep;
    data['sportDistance'] = sportDistance;
    data['sportStartTime'] = sportStartTime;
    data['sportCalorie'] = sportCalorie;
    return data;
  }
}
