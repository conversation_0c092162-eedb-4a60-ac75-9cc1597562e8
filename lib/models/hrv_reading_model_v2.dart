import 'package:intl/intl.dart';

class HRVReadingModelV2 {
  final DateTime date;
  final int diastolicBP;
  final int stress;
  final int heartRate;
  final int systolicBP;
  final int hrv;
  final int vascularAging;

  HRVReadingModelV2({
    required this.date,
    required this.diastolicBP,
    required this.stress,
    required this.heartRate,
    required this.systolicBP,
    required this.hrv,
    required this.vascularAging,
  });

  Map<String, dynamic> toMap() {
    return {
      'date': DateFormat('yyyy.MM.dd HH:mm:ss').format(date),
      'diastolicBP': diastolicBP,
      'stress': stress,
      'heartRate': heartRate,
      'systolicBP': systolicBP,
      'hrv': hrv,
      'vascularAging': vascularAging,
    };
  }

  factory HRVReadingModelV2.fromMap(Map<String, dynamic> map) {
    // Check for required fields
    final requiredFields = [
      'date', 'diastolicBP', 'stress', 'heartRate',
      'systolicBP', 'hrv', 'vascularAging'
    ];
    
    for (String field in requiredFields) {
      if (map[field] == null) {
        throw FormatException('Missing required field: $field in HRV reading');
      }
    }

    return HRVReadingModelV2(
      date: _parseDate(map['date'].toString()),
      diastolicBP: int.parse(map['diastolicBP'].toString()),
      stress: int.parse(map['stress'].toString()),
      heartRate: int.parse(map['heartRate'].toString()),
      systolicBP: int.parse(map['systolicBP'].toString()),
      hrv: int.parse(map['hrv'].toString()),
      vascularAging: int.parse(map['vascularAging'].toString()),
    );
  }

  static DateTime _parseDate(String dateStr) {
    try {
      return DateFormat('yyyy.MM.dd HH:mm:ss').parse(dateStr);
    } catch (e) {
      throw FormatException('Invalid date format: $dateStr');
    }
  }

  static List<HRVReadingModelV2> parseReadings(dynamic input) {
    List<HRVReadingModelV2> readings = [];

    // Handle different input types
    if (input is Map) {
      if (input.containsKey('dataEnd') && input.containsKey('dicData')) {
        // This is Android format data
        var dicData = input['dicData'];
        if (dicData is List) {
          for (var item in dicData) {
            if (item is Map) {
              try {
                // Map Android data fields to our model fields
                String? dateStr = item['date']?.toString();
                String? highBP = item['highBP']?.toString();
                String? lowBP = item['lowBP']?.toString();
                String? heartRate = item['heartRate']?.toString();
                String? stress = item['stress']?.toString();
                String? hrv = item['hrv']?.toString();
                String? vascularAging = item['vascularAging']?.toString();
                
                // Check for missing fields
                if (dateStr == null || highBP == null || lowBP == null || 
                    heartRate == null || stress == null || hrv == null || 
                    vascularAging == null) {
                  print('Missing required fields in HRV reading: $item');
                  continue;
                }
                
                readings.add(HRVReadingModelV2(
                  date: _parseDate(dateStr),
                  diastolicBP: int.parse(lowBP),
                  stress: int.parse(stress),
                  heartRate: int.parse(heartRate),
                  systolicBP: int.parse(highBP),
                  hrv: int.parse(hrv),
                  vascularAging: int.parse(vascularAging),
                ));
              } catch (e) {
                print('Error parsing HRV item: $e');
                print('Problematic item: $item');
              }
            }
          }
          return readings;
        }
      }
      return []; // Empty list for empty or invalid map
    }
    
    // Original string-based parsing logic
    String inputStr = input.toString();
    if (inputStr.isEmpty) {
      return [];
    }

    // For iOS format with brackets
    if (inputStr.startsWith('[') && inputStr.endsWith(']')) {
      // Handle empty array case
      if (inputStr == '[]') {
        return [];
      }

      // Remove outer brackets and split into individual readings
      String cleaned = inputStr.substring(1, inputStr.length - 1).trim();

      // Handle case where there's no content between brackets
      if (cleaned.isEmpty) {
        return [];
      }

      List<String> readingsStr = cleaned.split('], [');
      List<HRVReadingModelV2> readings = [];

      for (String readingStr in readingsStr) {
        // Skip empty strings
        if (readingStr.trim().isEmpty) {
          continue;
        }

        try {
          // Clean up the string
          readingStr = readingStr.replaceAll('[', '').replaceAll(']', '');
          Map<String, dynamic> readingMap = {};

          // Split into key-value pairs
          List<String> pairs = readingStr.split(', ');
          for (String pair in pairs) {
            if (pair.trim().isEmpty) continue;

            List<String> keyValue = pair.split(': ');
            if (keyValue.length != 2) {
              throw FormatException('Invalid key-value pair format: $pair');
            }

            String key = keyValue[0].replaceAll('"', '');
            String value = keyValue[1].replaceAll('"', '');
            readingMap[key] = value;
          }

          // Verify all required fields are present
          final requiredFields = [
            'date', 'diastolicBP', 'stress', 'heartRate',
            'systolicBP', 'hrv', 'vascularAging'
          ];

          for (String field in requiredFields) {
            if (!readingMap.containsKey(field)) {
              print('Missing required field: $field in reading: $readingStr');
              continue;
            }
          }

          readings.add(HRVReadingModelV2.fromMap(readingMap));
        } catch (e) {
          print('Error parsing reading: $readingStr');
          print('Error details: $e');
          continue;
        }
      }

      return readings;
    }
    
    // For non-bracketed data
    print('Unrecognized data format for HRV readings: $inputStr');
    return [];
  }

  @override
  String toString() {
    return 'HRVReading(date: ${DateFormat('yyyy.MM.dd HH:mm:ss').format(date)}, '
        'diastolicBP: $diastolicBP, stress: $stress, heartRate: $heartRate, '
        'systolicBP: $systolicBP, hrv: $hrv, vascularAging: $vascularAging)';
  }
}