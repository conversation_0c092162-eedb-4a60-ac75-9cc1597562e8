class SleepFirebaseModel {
  num? remSleepMinutes;
  num? deepSleepMinutes;
  num? lightSleepCount;
  num? lightSleepMinutes;
  num? deepSleepCount;
  num? startTimeStamp;
  num? endTimeStamp;
  num? awakeSleepMinutes;

  List<SleepDetailDatas>? sleepDetailDatas;

  SleepFirebaseModel(
      {this.remSleepMinutes,
      this.deepSleepMinutes,
      this.lightSleepCount,
      this.lightSleepMinutes,
      this.deepSleepCount,
      this.startTimeStamp,
      this.awakeSleepMinutes,
      this.sleepDetailDatas});

  SleepFirebaseModel.fromJson(Map<String, dynamic> json) {
    remSleepMinutes = json['remSleepMinutes'];
    deepSleepMinutes = json['deepSleepMinutes'];
    lightSleepCount = json['lightSleepCount'];
    lightSleepMinutes = json['lightSleepMinutes'];
    deepSleepCount = json['deepSleepCount'];
    startTimeStamp = json['startTimeStamp'];
    endTimeStamp = json['endTimeStamp'];
    awakeSleepMinutes = json['awakeSleepMinutes'];
    if (json['sleepDetailDatas'] != null) {
      sleepDetailDatas = <SleepDetailDatas>[];
      json['sleepDetailDatas'].forEach((v) {
        sleepDetailDatas!.add(SleepDetailDatas.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['remSleepMinutes'] = remSleepMinutes;
    data['deepSleepMinutes'] = deepSleepMinutes;
    data['lightSleepCount'] = lightSleepCount;
    data['lightSleepMinutes'] = lightSleepMinutes;
    data['awakeSleepMinutes'] = awakeSleepMinutes;
    data['deepSleepCount'] = deepSleepCount;
    data['startTimeStamp'] = startTimeStamp;
    if (sleepDetailDatas != null) {
      data['sleepDetailDatas'] =
          sleepDetailDatas!.map((v) => v.toJson()).toList();
    }
    data['endTimeStamp'] = endTimeStamp;
    return data;
  }
}

class SleepDetailDatas {
  int? duration;
  int? startTimeStamp;
  int? sleepType;

  SleepDetailDatas({this.duration, this.startTimeStamp, this.sleepType});

  SleepDetailDatas.fromJson(Map<String, dynamic> json) {
    duration = json['duration'];
    startTimeStamp = json['startTimeStamp'];
    sleepType = json['sleepType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['duration'] = duration;
    data['startTimeStamp'] = startTimeStamp;
    data['sleepType'] = sleepType;
    return data;
  }
}
