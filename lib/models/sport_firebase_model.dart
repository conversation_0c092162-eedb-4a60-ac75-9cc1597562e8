class SportFirebaseModel {
  int? startTimeStamp;
  int? distance;
  int? endTimeStamp;
  int? step;
  int? calories;

  SportFirebaseModel({
    this.startTimeStamp,
    this.distance,
    this.endTimeStamp,
    this.step,
    this.calories,
  });

  SportFirebaseModel.fromJson(Map<String, dynamic> json) {
    startTimeStamp = json['startTimeStamp'];
    distance = json['distance'];
    endTimeStamp = json['endTimeStamp'];
    step = json['step'];
    calories = json['calories'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['startTimeStamp'] = startTimeStamp;
    data['distance'] = distance;
    data['endTimeStamp'] = endTimeStamp;
    data['step'] = step;
    data['calories'] = calories;
    return data;
  }
}
