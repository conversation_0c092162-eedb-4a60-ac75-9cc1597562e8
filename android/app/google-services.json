{"project_info": {"project_number": "391397853270", "firebase_url": "https://sfoto-clinic-396605-default-rtdb.firebaseio.com", "project_id": "sfoto-clinic-396605", "storage_bucket": "sfoto-clinic-396605.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:391397853270:android:8fe8fb85c9fcde7b3e13f7", "android_client_info": {"package_name": "com.saiwell"}}, "oauth_client": [{"client_id": "391397853270-3e6mlengmrjs363c2mbgqa49d5dcs1m5.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCHZ7FSKWQ_gx7s5SRr9TGfEdor8L81BD8"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "391397853270-3e6mlengmrjs363c2mbgqa49d5dcs1m5.apps.googleusercontent.com", "client_type": 3}, {"client_id": "391397853270-e09spnubh4uapr4brla7n930c3irpeat.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.saigeware.rpm"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:391397853270:android:ae0c87672c7f25163e13f7", "android_client_info": {"package_name": "com.saiwell.sw"}}, "oauth_client": [{"client_id": "391397853270-3e6mlengmrjs363c2mbgqa49d5dcs1m5.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCHZ7FSKWQ_gx7s5SRr9TGfEdor8L81BD8"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "391397853270-3e6mlengmrjs363c2mbgqa49d5dcs1m5.apps.googleusercontent.com", "client_type": 3}, {"client_id": "391397853270-e09spnubh4uapr4brla7n930c3irpeat.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.saigeware.rpm"}}]}}}], "configuration_version": "1"}