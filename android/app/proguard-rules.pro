# Flutter wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class com.google.firebase.** { *; }
-dontwarn io.flutter.embedding.**
-ignorewarnings

# Keep Ring SDK V2 classes
-keep class com.ycproduct.api.** { *; }
-keep class com.ycproduct.callback.** { *; }
-keep class com.ycproduct.model.** { *; }
-dontwarn com.ycproduct.**

# Keep all JStyle BLE SDK classes
-keep class com.jstyle.** { *; }
-keep class com.jstyle.blesdk2301.** { *; }
-keep class com.jstyle.blesdk2301.callback.** { *; }
-keep class com.jstyle.blesdk2301.model.** { *; }
-dontwarn com.jstyle.**

# Keep classes used via reflection
-keepclassmembers class com.jstyle.blesdk2301.callback.OnScanResults {
    public void Success(com.jstyle.blesdk2301.model.Device);
    public void Fail(int);
}
-keepclassmembers class com.jstyle.blesdk2301.callback.BleConnectionListener {
    public void onConnectionStateChange(boolean);
    public void Connecting();
    public void Connected();
    public void DisConnected();
}
-keepclassmembers class com.jstyle.blesdk2301.model.Device {
    <methods>;
}

# Flutter Local Notifications ProGuard rules
-keep class com.dexterous.flutterlocalnotifications.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Keep generic signature of TypeToken and its subclasses
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod

# Keep Gson TypeToken classes
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type

# Keep notification receiver classes
-keep class com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver { *; }
-keep class com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver { *; }