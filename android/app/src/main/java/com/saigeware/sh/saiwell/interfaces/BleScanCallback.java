package com.saigeware.sh.saiwell.interfaces;

import com.saigeware.sh.saiwell.model.BleDevice;
import com.jstyle.blesdk2301.callback.OnScanResults;
import com.jstyle.blesdk2301.model.Device;

import java.util.ArrayList;
import java.util.List;

/**
 * Legacy interface for backward compatibility
 * Use com.jstyle.blesdk2301.callback.OnScanResults for new code
 */
public interface BleScanCallback {
    void getBleScannedDevice(List<BleDevice> scannedDevices);
    
    /**
     * Convert to OnScanResults adapter
     */
    default OnScanResults toOnScanResults() {
        final List<BleDevice> devices = new ArrayList<>();
        
        return new OnScanResults() {
            @Override
            public void Success(Device device) {
                BleDevice bleDevice = new BleDevice(device.getMac(), device.getName());
                if (!devices.contains(bleDevice)) {
                    devices.add(bleDevice);
                    getBleScannedDevice(devices);
                }
            }
            
            @Override
            public void Fail(int errorCode) {
                // Handle failure by returning empty list
                getBleScannedDevice(devices);
            }
        };
    }
}

