package com.saigeware.sh.saiwell;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.content.Context;
import androidx.annotation.NonNull;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.plugins.GeneratedPluginRegistrant;
import com.saigeware.sh.saiwell.model.BleDevice;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Arrays;
import java.util.Calendar;
import java.util.LinkedList;
import android.util.Log;
import android.content.Intent;
import android.net.Uri;
import android.provider.Settings;
import android.os.PowerManager;
import android.app.AlarmManager;
import android.os.Build;

import com.jstyle.blesdk2301.callback.DataListener2301;
import com.jstyle.blesdk2301.Util.BleSDK;
import com.jstyle.blesdk2301.model.MyDeviceTime;
import com.jstyle.blesdk2301.constant.BleConst;
import com.jstyle.blesdk2301.constant.DeviceKey;
import android.graphics.Bitmap;
import java.io.ByteArrayOutputStream;
import androidx.annotation.Nullable;
import com.jstyle.blesdk2301.model.MyAutomaticHRMonitoring;
import com.jstyle.blesdk2301.model.AutoMode;

import java.util.HashSet;
import java.util.Set;


public class MainActivity extends FlutterFragmentActivity implements DataListener2301 {

    private static final String TAG = "MainActivity";
    private static final String CHANNEL = "com.saiwell.sw/android_native";
    
    private BleManager bleManager;
    private HashMap<String, Result> pendingResults = new HashMap<>();
    private HashMap<String, Map<String, Object>> pendingArguments = new HashMap<>();
    private CommandQueue commandQueue;

    // Map to store accumulated data for each data type
    private HashMap<String, List<Map<String, Object>>> accumulatedDataMap = new HashMap<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize BLE manager
        BleManager.init(this);
        bleManager = BleManager.getInstance();
        
        // Initialize the command queue
        commandQueue = new CommandQueue(bleManager, pendingResults);
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        GeneratedPluginRegistrant.registerWith(flutterEngine);

        // Set up method channel for Android notifications
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "saiwell/android_notifications")
            .setMethodCallHandler(
                (call, result) -> {
                    switch (call.method) {
                        case "isBatteryOptimizationDisabled":
                            isBatteryOptimizationDisabled(result);
                            break;
                        case "requestDisableBatteryOptimization":
                            requestDisableBatteryOptimization(result);
                            break;
                        default:
                            result.notImplemented();
                    }
                }
            );

        // Set up method channel for BLE
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
            .setMethodCallHandler(
                (call, result) -> {
                    // Store arguments for methods that need them
                    if (call.method.equals("setAutomaticHRMonitoringOneV2") || 
                        call.method.equals("setAutomaticHRMonitoringTwoV2") || 
                        call.method.equals("setAutomaticHRMonitoringThreeV2")) {
                        pendingArguments.put(call.method, call.arguments());
                    }
                    
                    switch (call.method) {
                        case "startScanDevicesV2":
                            startScanDevicesV2(result);
                            break;
                        case "connectToDeviceV2":
                            connectToDeviceV2(call, result);
                            break;
                        case "disconnectToDeviceV2":
                            disconnectToDeviceV2(result);
                            break;
                        case "getConnectionState":
                            getConnectionState(result);
                            break;
                        case "isDeviceConnectedV2":
                            isDeviceConnectedV2(result);
                            break;
                        case "handleGetBatteryLevelV2":
                            handleGetBatteryLevelV2(result);
                            break;
                        case "getDeviceMacV2":
                            getDeviceMacV2(result);
                            break;
                        case "getDeviceVersionV2":
                            getDeviceVersionV2(result);
                            break;
                        case "postDateTimeV2":
                            postDateTimeV2(result);
                            break;
                        case "getTotalActivityDataWithModeV2":
                            getTotalActivityDataWithModeV2(result);
                            break;

                        case "getSleepDataV2":
                            getSleepDataV2(result);
                            break;

                        case "getTemperatureDataV2":
                            getTemperatureDataV2(result);
                            break;

                        case "getHRVDataWithModeV2":
                            getHRVDataWithModeV2(result);
                            break;

                        case "getBloodOxygenV2":
                            getBloodOxygenV2(result);
                            break;

                        case "deleteHRVDataV2":
                            deleteHRVDataV2(result);
                            break;
                        case "deleteTemperatureDataV2":
                            deleteTemperatureDataV2(result);
                            break;
                        case "deleteSleepDataV2":
                            deleteSleepDataV2(result);
                            break;
                        case "deleteBloodOxygenV2":
                            deleteBloodOxygenV2(result);
                            break;
                        case "deleteTotalActivityDataV2":
                            deleteTotalActivityDataV2(result);
                            break;

                        case "startPPGWaveV2":
                            startPPGWaveV2(result);
                            break;

                        case "stopPPGWaveV2":
                            stopPPGWaveV2(result);
                            break;

                        case "setAutomaticHRMonitoringOneV2":
                            setAutomaticHRMonitoringOneV2(result);
                            break;
                        case "setAutomaticHRMonitoringTwoV2":
                            setAutomaticHRMonitoringTwoV2(result);
                            break;
                        case "setAutomaticHRMonitoringThreeV2":
                            setAutomaticHRMonitoringThreeV2(result);
                            break;
                        default:
                            result.notImplemented();
                    }
                }
            );
    }

    @Override
    public void dataCallback(Map<String, Object> maps) {
        Log.d(TAG, "====== RING DEBUG ====== Data callback received: " + maps.toString());

        if (maps == null || !maps.containsKey(DeviceKey.DataType)) {
            Log.e(TAG, "====== RING DEBUG ====== Data callback missing DataType or null map");
            return;
        }

        String dataType = (String) maps.get(DeviceKey.DataType);
        Log.d(TAG, "====== RING DEBUG ====== Data type: " + dataType);
        
        // Extra log for blood glucose data
        if (dataType.equals(BleConst.Blood_glucose_data)) {
            Log.d(TAG, "====== RING DEBUG ====== Received BLOOD GLUCOSE PPG data");
        } else if (dataType.equals(BleConst.Blood_glucose_status)) {
            Log.d(TAG, "====== RING DEBUG ====== Received BLOOD GLUCOSE PPG status");
        }

        // Check if there's an error
        if (maps.containsKey("Error")) {
            // Get the error message
            String errorMessage = "Unknown error";
            if (maps.get("Error") != null) {
                errorMessage = maps.get("Error").toString();
            }

            Log.e(TAG, "====== RING DEBUG ====== Error processing command: " + errorMessage + " for data type: " + dataType);

            // Get the pending result for this dataType
            Result pendingResult = pendingResults.remove(dataType);
            if (pendingResult != null) {
                // Return the error to Flutter
                pendingResult.error("NATIVE_ERROR", errorMessage, null);
                Log.d(TAG, "====== RING DEBUG ====== Error returned to Flutter");
            } else {
                Log.e(TAG, "====== RING DEBUG ====== No pending result for error on data type: " + dataType);
            }
            
            // Complete this command in the queue regardless of error
            commandQueue.completeCommand(dataType);
            return;
        }

        // Check if this is a multi-part response (dataEnd=false)
        boolean dataEnd = true;
        if (maps.containsKey(DeviceKey.End)) {
            dataEnd = Boolean.parseBoolean(maps.get(DeviceKey.End).toString());
        }
        
        // If this is not the end of a multi-part response, accumulate the data
        if (!dataEnd) {
            Log.d(TAG, "====== RING DEBUG ====== Received partial data for " + dataType + " (dataEnd=false)");
            
            // Accumulate data in a static map for each data type
            accumulateData(dataType, maps);
            
            // Don't complete the command yet, just return and wait for more data
            return;
        }
        
        // If we get here, either dataEnd=true or it's not specified
        // Finalize the accumulated data if there is any
        Map<String, Object> finalData = getFinalizedData(dataType, maps);
        
        // If we have finalized accumulated data, use that, otherwise use the current map
        Map<String, Object> resultMap = finalData != null ? finalData : maps;

        // Get the pending result for this dataType
        Result pendingResult = pendingResults.remove(dataType);
        if (pendingResult != null) {
            Log.d(TAG, "====== RING DEBUG ====== Processing successful result for data type: " + dataType);

            // Extract specific data values based on dataType
            if (dataType.equals(BleConst.GetDeviceBatteryLevel)) {
                if (resultMap.containsKey("dicData")) {
                    Map<String, Object> dicData = (Map<String, Object>) resultMap.get("dicData");
                    if (dicData.containsKey("batteryLevel")) {
                        Object batteryLevel = dicData.get("batteryLevel");
                        Log.d(TAG, "====== RING DEBUG ====== Battery level: " + batteryLevel);
                        pendingResult.success(batteryLevel.toString());
                        // Complete this command in the queue
                        commandQueue.completeCommand(dataType);
                        return;
                    } else {
                        Log.e(TAG, "====== RING DEBUG ====== Battery level data missing batteryLevel key");
                    }
                } else {
                    Log.e(TAG, "====== RING DEBUG ====== Battery level data missing dicData key");
                }
            } else if (dataType.equals(BleConst.GetDeviceVersion)) {
                // Extract device version for getDeviceVersionV2
                if (resultMap.containsKey("dicData")) {
                    Map<String, Object> dicData = (Map<String, Object>) resultMap.get("dicData");
                    if (dicData.containsKey("deviceVersion")) {
                        Object deviceVersion = dicData.get("deviceVersion");
                        Log.d(TAG, "====== RING DEBUG ====== Device version: " + deviceVersion);
                        pendingResult.success(deviceVersion.toString());
                        // Complete this command in the queue
                        commandQueue.completeCommand(dataType);
                        return;
                    } else {
                        Log.e(TAG, "====== RING DEBUG ====== Device version data missing deviceVersion key");
                    }
                } else {
                    Log.e(TAG, "====== RING DEBUG ====== Device version data missing dicData key");
                }
            } else if (dataType.equals(BleConst.GetDeviceMacAddress)) {
                // Extract MAC address for getDeviceMacV2
                if (resultMap.containsKey("dicData")) {
                    Map<String, Object> dicData = (Map<String, Object>) resultMap.get("dicData");
                    if (dicData.containsKey("macAddress")) {
                        Object macAddress = dicData.get("macAddress");
                        Log.d(TAG, "====== RING DEBUG ====== MAC address: " + macAddress);
                        pendingResult.success(macAddress.toString());
                        // Complete this command in the queue
                        commandQueue.completeCommand(dataType);
                        return;
                    } else {
                        Log.e(TAG, "====== RING DEBUG ====== MAC address data missing macAddress key");
                    }
                } else {
                    Log.e(TAG, "====== RING DEBUG ====== MAC address data missing dicData key");
                }
            }

            // For health data and other data types, return the full map (Flutter expects this format)
            // List of dataTypes that should receive full maps
            List<String> fullMapDataTypes = Arrays.asList(
                BleConst.GetDetailActivityData,
                BleConst.GetDetailSleepData,
                BleConst.Temperature_history,
                BleConst.GetHRVData,
                BleConst.Blood_oxygen,
                BleConst.Blood_glucose_data
            );

            if (fullMapDataTypes.contains(dataType)) {
                Log.d(TAG, "====== RING DEBUG ====== Returning full data map to Flutter for: " + dataType);
                pendingResult.success(resultMap);
            } else if (dataType.equals(BleConst.Blood_glucose_status)) {
                // For PPG/Blood glucose status, extract the useful data
                if (resultMap.containsKey(DeviceKey.Data)) {
                    Map<String, Object> statusData = (Map<String, Object>) resultMap.get(DeviceKey.Data);
                    
                    // Send the blood glucose status data to Flutter
                    Log.d(TAG, "====== RING DEBUG ====== Blood glucose status: " + statusData);
                    pendingResult.success(statusData);
                    
                    // Send events for blood glucose data as they come in
                    if (getFlutterEngine() != null) {
                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                            .invokeMethod("onPPGStatusUpdate", statusData, new MethodChannel.Result() {
                                @Override
                                public void success(Object result) {
                                    Log.d(TAG, "====== RING DEBUG ====== Successfully sent status update to Flutter");
                                }

                                @Override
                                public void error(String errorCode, String errorMessage, Object errorDetails) {
                                    Log.e(TAG, "====== RING DEBUG ====== Error sending status update to Flutter: " + errorCode + " - " + errorMessage);
                                }

                                @Override
                                public void notImplemented() {
                                    Log.w(TAG, "====== RING DEBUG ====== Method not implemented in Flutter: onPPGStatusUpdate");
                                }
                            });
                        Log.d(TAG, "====== RING DEBUG ====== Sent status update to Flutter: onPPGStatusUpdate");
                    }
                } else {
                    Log.d(TAG, "====== RING DEBUG ====== No blood glucose status data available");
                    pendingResult.success(resultMap);
                }
            } else if (dataType.equals(BleConst.ppgStop)) {
                // Handle PPG Stop response
                Log.d(TAG, "====== RING DEBUG ====== Received PPG stop response");
                pendingResult.success(true);
                
                // Ensure the Flutter side is notified that measurement has stopped
                if (getFlutterEngine() != null) {
                    new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                        .invokeMethod("onPPGStopped", null, new MethodChannel.Result() {
                            @Override
                            public void success(Object result) {
                                Log.d(TAG, "====== RING DEBUG ====== Successfully notified Flutter of PPG stop");
                            }

                            @Override
                            public void error(String errorCode, String errorMessage, Object errorDetails) {
                                Log.e(TAG, "====== RING DEBUG ====== Error notifying Flutter of PPG stop: " + errorCode + " - " + errorMessage);
                            }

                            @Override
                            public void notImplemented() {
                                Log.w(TAG, "====== RING DEBUG ====== Method not implemented in Flutter: onPPGStopped");
                            }
                        });
                }
            } else if (dataType.equals(BleConst.ppgQuit)) {
                // Handle PPG Quit response
                Log.d(TAG, "====== RING DEBUG ====== Received PPG quit response");
                // No need to send result back to Flutter, this is just for cleanup
                // Just log it for debugging
                Log.d(TAG, "====== RING DEBUG ====== PPG mode fully exited");
            } else {
                // For any other data types, try to extract just the dicData to simplify the response
                if (resultMap.containsKey("dicData")) {
                    Object dicData = resultMap.get("dicData");
                    Log.d(TAG, "====== RING DEBUG ====== Returning dicData to Flutter for: " + dataType);
                    pendingResult.success(dicData);
                } else {
                    // If no dicData is available, return the full map as fallback
                    Log.d(TAG, "====== RING DEBUG ====== No dicData found, returning full map for: " + dataType);
                    pendingResult.success(resultMap);
                }
            }
            
            // Complete this command in the queue
            commandQueue.completeCommand(dataType);
        } else {
            Log.w(TAG, "====== RING DEBUG ====== No pending result found for data type: " + dataType);
            
            // For Blood glucose data, we need to pass it to Flutter even if there's no pending result
            if (dataType.equals(BleConst.Blood_glucose_data)) {
                Log.d(TAG, "====== RING DEBUG ====== Forwarding Blood Glucose data to handleBloodGlucoseData()");
                handleBloodGlucoseData(maps);
            }
            
            // Still complete the command in case it's in the queue
            commandQueue.completeCommand(dataType);
        }
        
        // Clear accumulated data after processing
        clearAccumulatedData(dataType);
    }

    @Override
    public void dataCallback(byte[] value) {
        Log.d(TAG, "====== RING DEBUG ====== Raw data callback: " + bytesToHex(value) + " (length: " + value.length + ")");
        BleSDK.DataParsingWithData(value, this);
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString();
    }

    // Implementation of BLE methods using BleManager and BleSDK

    private void startScanDevicesV2(Result result) {
        try {
            final HashMap<String, Map<String, Object>> deviceMap = new HashMap<>();
            final Handler handler = new Handler(Looper.getMainLooper());
            
            final BleManager bleManager = BleManager.getInstance();
            if (bleManager == null) {
                Log.e(TAG, "====== RING DEBUG ====== BleManager not initialized for scan");
                result.error("SCAN_ERROR", "BleManager not initialized", null);
                return;
            }
            
            Log.d(TAG, "====== RING DEBUG ====== Starting Bluetooth scan for Ring devices");
            
            // Create dynamic proxy for OnScanResults
            Object scanCallback = null;
            try {
                Class<?> onScanResultsClass = Class.forName("com.jstyle.blesdk2301.callback.OnScanResults");
                Log.d(TAG, "====== RING DEBUG ====== Successfully loaded OnScanResults class: " + onScanResultsClass);
                
                scanCallback = java.lang.reflect.Proxy.newProxyInstance(
                        getClassLoader(),
                        new Class[] { onScanResultsClass },
                        new java.lang.reflect.InvocationHandler() {
                            @Override
                            public Object invoke(Object proxy, java.lang.reflect.Method method, Object[] args) throws Throwable {
                                String methodName = method.getName();
                                Log.d(TAG, "====== RING DEBUG ====== OnScanResults method called: " + methodName);
                                
                                if (methodName.equals("Success") && args != null && args.length > 0) {
                                    // Device found
                                    com.jstyle.blesdk2301.model.Device device = (com.jstyle.blesdk2301.model.Device) args[0];
                                    
                                    // Create device object for Flutter with the proper constructor
                                    String deviceName = device.getName() != null ? device.getName() : "Unknown";
                                    String deviceMac = device.getMac() != null ? device.getMac() : "";
                                    
                                    Log.d(TAG, "====== RING DEBUG ====== Device found: " + deviceName + " (" + deviceMac + ")");
                                    
                                    // Add to the map only if it contains "2301" and has a valid MAC address
                                    if (deviceName.contains("2301") && !deviceMac.isEmpty()) {
                                        BleDevice bleDevice = new BleDevice(deviceMac, deviceName);
                                        int rssi = device.getRiss();
                                        bleDevice.setRssi(rssi);
                                        bleDevice.setConnected(false); // Default to not connected for scanned devices
                                        
                                        // Use MAC address as key to ensure uniqueness
                                        deviceMap.put(deviceMac, bleDevice.toMap());
                                        Log.d(TAG, "====== RING DEBUG ====== Device added/updated in map. Current count: " + deviceMap.size());
                                    } else {
                                        Log.d(TAG, "====== RING DEBUG ====== Device skipped (not 2301 or empty MAC): " + deviceName);
                                    }
                                } else if (methodName.equals("Fail") && args != null && args.length > 0) {
                                    // Scan failed
                                    int errorCode = (int) args[0];
                                    Log.e(TAG, "====== RING DEBUG ====== Scan failed with error code: " + errorCode);
                                    handler.post(() -> {
                                        result.error("SCAN_ERROR", "Scan failed with error code: " + errorCode, null);
                                    });
                                }
                                return null;
                            }
                        });
            } catch (ClassNotFoundException e) {
                Log.e(TAG, "====== RING DEBUG ====== Failed to find OnScanResults class: " + e.getMessage(), e);
                result.error("CLASS_ERROR", "Failed to find OnScanResults class: " + e.getMessage(), null);
                return;
            } catch (Exception e) {
                Log.e(TAG, "====== RING DEBUG ====== Error creating proxy for OnScanResults: " + e.getMessage(), e);
                result.error("PROXY_ERROR", "Error creating proxy: " + e.getMessage(), null);
                return;
            }
            
            if (scanCallback == null) {
                Log.e(TAG, "====== RING DEBUG ====== Failed to create scan callback");
                result.error("CALLBACK_ERROR", "Failed to create scan callback", null);
                return;
            }
            
            // Start scanning for devices
            String[] deviceNames = {"2301"};  // Filter for Ring devices
            try {
                bleManager.startDeviceScan(deviceNames, (com.jstyle.blesdk2301.callback.OnScanResults) scanCallback);
            } catch (Exception e) {
                Log.e(TAG, "====== RING DEBUG ====== Error starting scan: " + e.getMessage(), e);
                result.error("SCAN_ERROR", "Error starting scan: " + e.getMessage(), null);
                return;
            }
            
            // Schedule to stop scanning after 4 seconds and return devices (optimized for Android)
            handler.postDelayed(() -> {
                Log.d(TAG, "====== RING DEBUG ====== Scan timeout reached, stopping scan");
                bleManager.stopScanDevices();

                // Convert map to list for Flutter
                List<Map<String, Object>> deviceList = new ArrayList<>(deviceMap.values());

                // Return the collected devices to Flutter
                Log.d(TAG, "====== RING DEBUG ====== Found " + deviceList.size() + " unique devices");
                result.success(deviceList);
            }, 4000); // Scan for 4 seconds (reduced from 6 for better performance)
            
        } catch (Exception e) {
            Log.e(TAG, "====== RING DEBUG ====== Error starting scan: " + e.getMessage(), e);
            result.error("SCAN_ERROR", "Error starting scan: " + e.getMessage(), null);
        }
    }

    private void connectToDeviceV2(MethodCall call, Result result) {
        try {
            // Log the received arguments for debugging
            Log.d(TAG, "connectToDeviceV2 arguments: " + call.arguments);
            
            // Extract the MAC address from arguments
            String macId;
            
            if (call.arguments instanceof String) {
                // If it's directly a string
                macId = (String) call.arguments;
            } else if (call.arguments instanceof Map) {
                // If it's a map, extract the MAC address from it
                Map<String, Object> argsMap = (Map<String, Object>) call.arguments;
                // Check for deviceMac key (sent from Flutter) or macId key (alternative name)
                if (argsMap.containsKey("deviceMac")) {
                    Object macIdObj = argsMap.get("deviceMac");
                    if (macIdObj instanceof String) {
                        macId = (String) macIdObj;
                    } else {
                        result.error("INVALID_ARGUMENT", "deviceMac is not a string", null);
                        return;
                    }
                } else if (argsMap.containsKey("macId")) {
                    Object macIdObj = argsMap.get("macId");
                    if (macIdObj instanceof String) {
                        macId = (String) macIdObj;
                    } else {
                        result.error("INVALID_ARGUMENT", "macId is not a string", null);
                        return;
                    }
                } else {
                    result.error("INVALID_ARGUMENT", "MAC address not found in arguments", null);
                    return;
                }
            } else {
                result.error("INVALID_ARGUMENT", "Invalid arguments type: " + call.arguments.getClass().getName(), null);
                return;
            }
            
            final BleManager bleManager = BleManager.getInstance();
            if (bleManager == null) {
                result.error("CONNECTION_ERROR", "BleManager not initialized", null);
                return;
            }
            
            // Check if already connected to avoid connection errors
            if (bleManager.isConnected()) {
                Log.d(TAG, "Device is already connected, returning success");
                result.success(true);
                
                // Notify Flutter about connection status to ensure state synchronization
                runOnUiThread(() -> {
                    HashMap<String, Object> stateMap = new HashMap<>();
                    stateMap.put("isConnected", true);
                    
                    if (getFlutterEngine() != null) {
                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                            .invokeMethod("onConnectionStateChange", stateMap);
                    }
                });
                
                return;
            }
            
            pendingResults.put(call.method, result);
            Log.d(TAG, "Connecting to device with MAC: " + macId);
            
            // Create dynamic proxy for BleConnectionListener
            Object connectionListener = java.lang.reflect.Proxy.newProxyInstance(
                    getClassLoader(),
                    new Class[] { Class.forName("com.jstyle.blesdk2301.callback.BleConnectionListener") },
                    new java.lang.reflect.InvocationHandler() {
                        @Override
                        public Object invoke(Object proxy, java.lang.reflect.Method method, Object[] args) throws Throwable {
                            String methodName = method.getName();
                            Log.d(TAG, "BleConnectionListener method called: " + methodName);

                            if (methodName.equals("onConnectionStateChange")) {
                                boolean isConnected = (boolean) args[0];
                                Log.d(TAG, "onConnectionStateChange: isConnected=" + isConnected + "args : " + Arrays.toString(args));
                                runOnUiThread(() -> {
                                    HashMap<String, Object> stateMap = new HashMap<>();
                                    stateMap.put("isConnected", isConnected);
                                    
                                    // Use the method channel from the engine provided in configureFlutterEngine
                                    if (getFlutterEngine() != null) {
                                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                                            .invokeMethod("onConnectionStateChange", stateMap);
                                    }
                                    
                                    if (isConnected) {
                                        // Connection successful
                                        if (pendingResults.containsKey(call.method)) {
                                            Result pendingResult = pendingResults.get(call.method);
                                            pendingResult.success(true);
                                            pendingResults.remove(call.method);
                                        }
                                    }
                                });
                            } else if (methodName.equals("ConnectionSucceeded")) {
                                // This is called when the device is fully connected (after service discovery)
                                Log.d(TAG, "ConnectionSucceeded called");
                                runOnUiThread(() -> {
                                    HashMap<String, Object> stateMap = new HashMap<>();
                                    stateMap.put("isConnected", true);
                                    
                                    // Use the method channel from the engine provided in configureFlutterEngine
                                    if (getFlutterEngine() != null) {
                                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                                            .invokeMethod("onConnectionStateChange", stateMap);
                                    }
                                    
                                    // Connection successful
                                    if (pendingResults.containsKey(call.method)) {
                                        Result pendingResult = pendingResults.get(call.method);
                                        pendingResult.success(true);
                                        pendingResults.remove(call.method);
                                    }
                                });
                            } else if (methodName.equals("ConnectionFailed")) {
                                Log.d(TAG, "ConnectionFailed called");
                                runOnUiThread(() -> {
                                    if (pendingResults.containsKey(call.method)) {
                                        Result pendingResult = pendingResults.get(call.method);
                                        pendingResult.error("CONNECTION_ERROR", "Device connection failed", null);
                                        pendingResults.remove(call.method);
                                    }
                                });
                            } else if (methodName.equals("BluetoothSwitchIsTurnedOff")) {
                                Log.d(TAG, "BluetoothSwitchIsTurnedOff called");
                                runOnUiThread(() -> {
                                    // Notify Flutter that Bluetooth is turned off
                                    if (getFlutterEngine() != null) {
                                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                                            .invokeMethod("BluetoothSwitchIsTurnedOff", null);
                                    }

                                    // Also fail the connection attempt
                                    if (pendingResults.containsKey(call.method)) {
                                        Result pendingResult = pendingResults.get(call.method);
                                        pendingResult.error("BLUETOOTH_OFF", "Bluetooth is turned off", null);
                                        pendingResults.remove(call.method);
                                    }
                                });
                            }
                            return null;
                        }
                    });
            
            // Connect to device
            bleManager.connectDevice(macId, true, (com.jstyle.blesdk2301.callback.BleConnectionListener) connectionListener);
            
        } catch (Exception e) {
            Log.e(TAG, "Error connecting to device: " + e.getMessage(), e);
            result.error("CONNECTION_ERROR", "Error connecting to device: " + e.getMessage(), null);
        }
    }

    private void disconnectToDeviceV2(Result result) {
        try {
            final BleManager bleManager = BleManager.getInstance();
            if (bleManager != null) {
                bleManager.disconnectDevice();
                result.success(true);
            } else {
                result.error("DISCONNECT_ERROR", "BleManager not initialized", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting device: " + e.getMessage(), e);
            result.error("DISCONNECT_ERROR", "Error disconnecting device: " + e.getMessage(), null);
        }
    }

    private void getConnectionState(Result result) {
        try {
            final BleManager bleManager = BleManager.getInstance();
            if (bleManager != null) {
                boolean isConnected = bleManager.isConnected();
                Log.d(TAG, "getConnectionState - Connected status: " + isConnected);
                
                // Notify Flutter about the current connection state
                runOnUiThread(() -> {
                    HashMap<String, Object> stateMap = new HashMap<>();
                    stateMap.put("isConnected", isConnected);
                    
                    if (getFlutterEngine() != null) {
                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                            .invokeMethod("onConnectionStateChange", stateMap);
                    }
                });
                
                // Return an integer-based status code for consistent interpretation in Flutter
                // 10 = connected, 0 = disconnected
                result.success(isConnected ? 10 : 0);
            } else {
                Log.e(TAG, "getConnectionState - BleManager not initialized");
                result.error("STATE_ERROR", "BleManager not initialized", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting connection state: " + e.getMessage(), e);
            result.error("STATE_ERROR", "Error getting connection state: " + e.getMessage(), null);
        }
    }

    // Helper method to send commands to the device and handle responses
    private void sendCommand(byte[] command, String responseType, Result result) {
        Log.d(TAG, "====== RING DEBUG ====== Sending command for responseType: " + responseType + ", command: " + bytesToHex(command));
        // Queue the command rather than sending immediately
        commandQueue.queueCommand(command, responseType, result);
    }

    // Device information methods   
    private void handleGetBatteryLevelV2(Result result) {
        try {
            byte[] command = BleSDK.GetDeviceBatteryLevel();
            sendCommand(command, BleConst.GetDeviceBatteryLevel, result);
        } catch (Exception e) {
            Log.e(TAG, "Error getting battery level: " + e.getMessage(), e);
            result.error("COMMAND_ERROR", "Error getting battery level: " + e.getMessage(), null);
        }
    }

    private void getDeviceMacV2(Result result) {
        byte[] command = BleSDK.GetDeviceMacAddress();
        sendCommand(command, BleConst.GetDeviceMacAddress, result);
    }

    private void getDeviceVersionV2(Result result) {
        byte[] command = BleSDK.GetDeviceVersion();
        sendCommand(command, BleConst.GetDeviceVersion, result);
    }

    private void postDateTimeV2(Result result) {
        Calendar calendar = Calendar.getInstance();
        MyDeviceTime deviceTime = new MyDeviceTime();
        deviceTime.setYear(calendar.get(Calendar.YEAR));
        deviceTime.setMonth(calendar.get(Calendar.MONTH) + 1);
        deviceTime.setDay(calendar.get(Calendar.DAY_OF_MONTH));
        deviceTime.setHour(calendar.get(Calendar.HOUR_OF_DAY));
        deviceTime.setMinute(calendar.get(Calendar.MINUTE));
        deviceTime.setSecond(calendar.get(Calendar.SECOND));
        
        byte[] command = BleSDK.SetDeviceTime(deviceTime);
        sendCommand(command, BleConst.SetDeviceTime, result);
    }

    // Health data methods
    private void getTotalActivityDataWithModeV2(Result result) {
        // Get current date in format yyyyMMdd
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String date = String.format("%04d%02d%02d", year, month, day);

        byte[] command = BleSDK.GetDetailActivityDataWithMode((byte) 0, date);
        sendCommand(command, BleConst.GetDetailActivityData, result);
    }

    private void getSleepDataV2(Result result) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String date = String.format("%04d%02d%02d", year, month, day);
        
        byte[] command = BleSDK.GetDetailSleepDataWithMode((byte) 0, date);
        sendCommand(command, BleConst.GetDetailSleepData, result);
    }

    private void getTemperatureDataV2(Result result) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String date = String.format("%04d%02d%02d", year, month, day);

        byte[] command = BleSDK.GetTemperature_historyData((byte) 0, date);
        sendCommand(command, BleConst.Temperature_history, result);
    }

    private void getHRVDataWithModeV2(Result result) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String date = String.format("%04d%02d%02d", year, month, day);
        
        byte[] command = BleSDK.GetHRVDataWithMode((byte) 0, date);
        sendCommand(command, BleConst.GetHRVData, result);
    }

    private void getBloodOxygenV2(Result result) {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        String date = String.format("%04d%02d%02d", year, month, day);
        
        byte[] command = BleSDK.Oxygen_data((byte) 0, date);
        sendCommand(command, BleConst.GetAutomaticSpo2Monitoring, result);
    }

    private void deleteHRVDataV2(Result result) {
        // Store the result for async callback
        pendingResults.put(BleConst.DeleteHrv, result);
        
        // Queue this command for execution
        commandQueue.queueCommand(
            BleSDK.GetHRVDataWithMode((byte) 0x99, ""), 
            BleConst.DeleteHrv,
            result
        );
        Log.d(TAG, "====== RING DEBUG ====== Sent deleteHRVDataV2 command");
    }

    private void deleteTemperatureDataV2(Result result) {
        // Store the result for async callback
        pendingResults.put(BleConst.deleteGetTemperature_historyDataWithMode, result);
        
        // Queue this command for execution
        commandQueue.queueCommand(
            BleSDK.GetTemperature_historyData((byte) 0x99, ""), 
            BleConst.deleteGetTemperature_historyDataWithMode,
            result
        );
        Log.d(TAG, "====== RING DEBUG ====== Sent deleteTemperatureDataV2 command");
    }

    private void deleteSleepDataV2(Result result) {
        // Store the result for async callback
        pendingResults.put(BleConst.Delete_GetDetailSleepData, result);
        
        // Queue this command for execution
        commandQueue.queueCommand(
            BleSDK.GetDetailSleepDataWithMode((byte) 0x99, ""), 
            BleConst.Delete_GetDetailSleepData,
            result
        );
        Log.d(TAG, "====== RING DEBUG ====== Sent deleteSleepDataV2 command");
    }

    private void deleteBloodOxygenV2(Result result) {
        // Store the result for async callback
        pendingResults.put(BleConst.Delete_Obtain_The_data_of_manual_blood_oxygen_test, result);
        
        // Queue this command for execution
        commandQueue.queueCommand(
            BleSDK.Oxygen_data((byte) 0x99, ""), 
            BleConst.Delete_Obtain_The_data_of_manual_blood_oxygen_test,
            result
        );
        Log.d(TAG, "====== RING DEBUG ====== Sent deleteBloodOxygenV2 command");
    }

    private void deleteTotalActivityDataV2(Result result) {
        // Store the result for async callback
        pendingResults.put(BleConst.deleteGetDetailActivityDataWithMode, result);
        // Queue this command for execution
        commandQueue.queueCommand(
            BleSDK.GetDetailActivityDataWithMode((byte) 0x99, ""),
            BleConst.deleteGetDetailActivityDataWithMode,
            result
        );
        Log.d(TAG, "====== RING DEBUG ====== Sent deleteTotalActivityDataV2 command");
    }

    // Heart rate monitoring methods
    private void setAutomaticHRMonitoringOneV2(Result result) {
        try {
            Map<String, Object> arguments = pendingArguments.get("setAutomaticHRMonitoringOneV2");
            if (arguments == null) {
                result.error("ARGUMENTS_ERROR", "No arguments found for SPO2 monitoring", null);
                return;
            }
            
            // Create and setup MyAutomaticHRMonitoring object
            MyAutomaticHRMonitoring monitoring = new MyAutomaticHRMonitoring();
            
            // Set monitoring parameters from Flutter arguments
            monitoring.setOpen(2); // 1 for open/enable
            monitoring.setTime((Integer) arguments.get("intervalTime"));
            monitoring.setStartHour((Integer) arguments.get("startHour"));
            monitoring.setStartMinute((Integer) arguments.get("startMinute"));
            monitoring.setEndHour((Integer) arguments.get("endHour"));
            monitoring.setEndMinute((Integer) arguments.get("endMinute"));
            
            // Calculate week value from weekday booleans
            Map<String, Boolean> weeks = (Map<String, Boolean>) arguments.get("weeks");
            int weekValue = 0;
            if (weeks != null) {
                if (Boolean.TRUE.equals(weeks.get("sunday"))) weekValue |= 0x01;
                if (Boolean.TRUE.equals(weeks.get("monday"))) weekValue |= 0x02;
                if (Boolean.TRUE.equals(weeks.get("tuesday"))) weekValue |= 0x04;
                if (Boolean.TRUE.equals(weeks.get("wednesday"))) weekValue |= 0x08;
                if (Boolean.TRUE.equals(weeks.get("thursday"))) weekValue |= 0x10;
                if (Boolean.TRUE.equals(weeks.get("friday"))) weekValue |= 0x20;
                if (Boolean.TRUE.equals(weeks.get("saturday"))) weekValue |= 0x40;
            }
            monitoring.setWeek(weekValue);
            
            // Use SPO2 mode instead of heart rate
            byte[] command = BleSDK.SetAutomaticHRMonitoring(monitoring, AutoMode.AutoSpo2);
            sendCommand(command, BleConst.SetAutomatic, result);
            
            // Clean up arguments after use
            pendingArguments.remove("setAutomaticHRMonitoringOneV2");
        } catch (Exception e) {
            Log.e(TAG, "Error setting automatic SPO2 monitoring: " + e.getMessage());
            result.error("SET_SPO2_ERROR", "Error setting SPO2 monitoring: " + e.getMessage(), null);
        }
    }

    private void setAutomaticHRMonitoringTwoV2(Result result) {
        try {
            Map<String, Object> arguments = pendingArguments.get("setAutomaticHRMonitoringTwoV2");
            if (arguments == null) {
                result.error("ARGUMENTS_ERROR", "No arguments found for temperature monitoring", null);
                return;
            }
            
            // Create and setup MyAutomaticHRMonitoring object
            MyAutomaticHRMonitoring monitoring = new MyAutomaticHRMonitoring();
            
            // Set monitoring parameters from Flutter arguments
            monitoring.setOpen(2); // 1 for open/enable
            monitoring.setTime((Integer) arguments.get("intervalTime"));
            monitoring.setStartHour((Integer) arguments.get("startHour"));
            monitoring.setStartMinute((Integer) arguments.get("startMinute"));
            monitoring.setEndHour((Integer) arguments.get("endHour"));
            monitoring.setEndMinute((Integer) arguments.get("endMinute"));
            
            // Calculate week value from weekday booleans
            Map<String, Boolean> weeks = (Map<String, Boolean>) arguments.get("weeks");
            int weekValue = 0;
            if (weeks != null) {
                if (Boolean.TRUE.equals(weeks.get("sunday"))) weekValue |= 0x01;
                if (Boolean.TRUE.equals(weeks.get("monday"))) weekValue |= 0x02;
                if (Boolean.TRUE.equals(weeks.get("tuesday"))) weekValue |= 0x04;
                if (Boolean.TRUE.equals(weeks.get("wednesday"))) weekValue |= 0x08;
                if (Boolean.TRUE.equals(weeks.get("thursday"))) weekValue |= 0x10;
                if (Boolean.TRUE.equals(weeks.get("friday"))) weekValue |= 0x20;
                if (Boolean.TRUE.equals(weeks.get("saturday"))) weekValue |= 0x40;
            }
            monitoring.setWeek(weekValue);
            
            // Use temperature mode instead of heart rate
            byte[] command = BleSDK.SetAutomaticHRMonitoring(monitoring, AutoMode.AutoTemp);
            sendCommand(command, BleConst.SetAutomatic, result);
            
            // Clean up arguments after use
            pendingArguments.remove("setAutomaticHRMonitoringTwoV2");
        } catch (Exception e) {
            Log.e(TAG, "Error setting automatic temperature monitoring: " + e.getMessage());
            result.error("SET_TEMP_ERROR", "Error setting temperature monitoring: " + e.getMessage(), null);
        }
    }

    private void setAutomaticHRMonitoringThreeV2(Result result) {
        try {
            Map<String, Object> arguments = pendingArguments.get("setAutomaticHRMonitoringThreeV2");
            if (arguments == null) {
                result.error("ARGUMENTS_ERROR", "No arguments found for HRV monitoring", null);
                return;
            }
            
            // Create and setup MyAutomaticHRMonitoring object
            MyAutomaticHRMonitoring monitoring = new MyAutomaticHRMonitoring();
            
            // Set monitoring parameters from Flutter arguments
            monitoring.setOpen(2); // 1 for open/enable
            monitoring.setTime((Integer) arguments.get("intervalTime"));
            monitoring.setStartHour((Integer) arguments.get("startHour"));
            monitoring.setStartMinute((Integer) arguments.get("startMinute"));
            monitoring.setEndHour((Integer) arguments.get("endHour"));
            monitoring.setEndMinute((Integer) arguments.get("endMinute"));
            
            // Calculate week value from weekday booleans
            Map<String, Boolean> weeks = (Map<String, Boolean>) arguments.get("weeks");
            int weekValue = 0;
            if (weeks != null) {
                if (Boolean.TRUE.equals(weeks.get("sunday"))) weekValue |= 0x01;
                if (Boolean.TRUE.equals(weeks.get("monday"))) weekValue |= 0x02;
                if (Boolean.TRUE.equals(weeks.get("tuesday"))) weekValue |= 0x04;
                if (Boolean.TRUE.equals(weeks.get("wednesday"))) weekValue |= 0x08;
                if (Boolean.TRUE.equals(weeks.get("thursday"))) weekValue |= 0x10;
                if (Boolean.TRUE.equals(weeks.get("friday"))) weekValue |= 0x20;
                if (Boolean.TRUE.equals(weeks.get("saturday"))) weekValue |= 0x40;
            }
            monitoring.setWeek(weekValue);
            
            // Use HRV mode instead of heart rate
            byte[] command = BleSDK.SetAutomaticHRMonitoring(monitoring, AutoMode.AutoHrv);
            sendCommand(command, BleConst.SetAutomatic, result);
            
            // Clean up arguments after use
            pendingArguments.remove("setAutomaticHRMonitoringThreeV2");
        } catch (Exception e) {
            Log.e(TAG, "Error setting automatic HRV monitoring: " + e.getMessage());
            result.error("SET_HRV_ERROR", "Error setting HRV monitoring: " + e.getMessage(), null);
        }
    }

    // Add the missing isDeviceConnectedV2 method
    private void isDeviceConnectedV2(Result result) {
        try {
            final BleManager bleManager = BleManager.getInstance();
            if (bleManager != null) {
                boolean isConnected = bleManager.isConnected();
                Log.d(TAG, "isDeviceConnectedV2 - Connected status: " + isConnected);
                
                // Send additional connection state update to ensure Flutter state is synchronized
                if (isConnected) {
                    runOnUiThread(() -> {
                        HashMap<String, Object> stateMap = new HashMap<>();
                        stateMap.put("isConnected", true);
                        
                        if (getFlutterEngine() != null) {
                            new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                                .invokeMethod("onConnectionStateChange", stateMap);
                        }
                    });
                }
                
                // Use the same integer coding as getConnectionState for consistency
                result.success(isConnected ? 10 : 0);
            } else {
                Log.e(TAG, "isDeviceConnectedV2 - BleManager not initialized");
                result.error("STATE_ERROR", "BleManager not initialized", null);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking connection state: " + e.getMessage(), e);
            result.error("STATE_ERROR", "Error checking connection state: " + e.getMessage(), null);
        }
    }

    /**
     * Accumulates data for multi-part responses (when dataEnd=false)
     */
    private void accumulateData(String dataType, Map<String, Object> data) {
        // Get or create the list for this data type
        List<Map<String, Object>> dataList = accumulatedDataMap.get(dataType);
        if (dataList == null) {
            dataList = new ArrayList<>();
            accumulatedDataMap.put(dataType, dataList);
        }
        
        // Add the current data to the list
        dataList.add(new HashMap<>(data));
        
        Log.d(TAG, "====== RING DEBUG ====== Accumulated data for " + dataType + 
              ", total parts: " + dataList.size());
              
        // Specifically for PPG data (Blood_glucose_data), forward each chunk to Flutter
        // instead of waiting for the final chunk (dataEnd=true)
        if (dataType.equals(BleConst.Blood_glucose_data)) {
            Log.d(TAG, "====== RING DEBUG ====== Processing partial PPG data to forward to Flutter");
            handleBloodGlucoseData(data);
        }
    }
    
    /**
     * Gets the finalized data by combining all accumulated data parts
     */
    private Map<String, Object> getFinalizedData(String dataType, Map<String, Object> finalPart) {
        List<Map<String, Object>> dataList = accumulatedDataMap.get(dataType);
        if (dataList == null || dataList.isEmpty()) {
            // No accumulated data, just use the final part
            return finalPart;
        }
        
        // Add the final part to the accumulated data
        dataList.add(new HashMap<>(finalPart));
        
        // Create a new result map with combined data
        Map<String, Object> result = new HashMap<>();
        
        // Copy over the basic fields from the final part
        for (Map.Entry<String, Object> entry : finalPart.entrySet()) {
            if (!entry.getKey().equals("dicData")) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        
        // Combine all dicData entries
        List<Object> combinedDicData = new ArrayList<>();
        for (Map<String, Object> part : dataList) {
            if (part.containsKey("dicData")) {
                Object dicData = part.get("dicData");
                if (dicData instanceof List) {
                    combinedDicData.addAll((List<?>) dicData);
                } else if (dicData instanceof Map) {
                    // If dicData is a Map, add it directly
                    combinedDicData.add(dicData);
                }
            }
        }
        
        // Set the combined dicData in the result
        result.put("dicData", combinedDicData);
        
        Log.d(TAG, "====== RING DEBUG ====== Finalized data for " + dataType + 
              ", combined " + dataList.size() + " parts with " + 
              combinedDicData.size() + " dicData entries");
        
        return result;
    }
    
    /**
     * Clears accumulated data for a specific data type
     */
    private void clearAccumulatedData(String dataType) {
        accumulatedDataMap.remove(dataType);
        Log.d(TAG, "====== RING DEBUG ====== Cleared accumulated data for " + dataType);
    }

    private void startPPGWaveV2(Result result) {
        Log.d(TAG, "====== RING DEBUG ====== Starting PPG Wave V2 (Blood Glucose Test)");
        Log.d(TAG, "====== RING DEBUG ====== BleManager connected state: " + bleManager.isConnected());
        Log.d(TAG, "====== RING DEBUG ====== BleManager instance: " + (bleManager != null ? "valid" : "null"));
        
        // Check if device is connected
        if (!bleManager.isConnected()) {
            Log.e(TAG, "====== RING DEBUG ====== Cannot start PPG wave - device not connected");
            result.error("DEVICE_ERROR", "Device not connected", null);
            return;
        }
        
        // Clear any pending results to avoid conflicts
        pendingResults.remove(BleConst.Blood_glucose_status);
        pendingResults.remove(BleConst.Blood_glucose_data);
        pendingResults.remove(BleConst.ppgStop);
        pendingResults.remove(BleConst.ppgQuit);
        
        // Store the result for async callback
        String responseType = BleConst.Blood_glucose_status;
        pendingResults.put(responseType, result);
        
        // Clear any accumulated data for blood glucose data
        clearAccumulatedData(BleConst.Blood_glucose_data);
        
        Log.d(TAG, "====== RING DEBUG ====== Queueing PPG command with mode 1, param 0");
        // Queue this command for execution
        commandQueue.queueCommand(
            BleSDK.ppgWithMode(1, 0),
            responseType,
            result
        );
        
        Log.d(TAG, "====== RING DEBUG ====== Sent startPPGWaveV2 command");
    }
    
    /**
     * Handle blood glucose (PPG) data from the device
     * This method processes and forwards PPG data to Flutter
     */
    private void handleBloodGlucoseData(Map<String, Object> data) {
        Log.d(TAG, "====== RING DEBUG ====== Processing blood glucose data: " + data);
        
        if (data.containsKey("dicData")) {
            Object dicDataObj = data.get("dicData");
            Log.d(TAG, "====== RING DEBUG ====== dicData found: " + dicDataObj);
            
            // Check if we have PPG wave data in dicData
            if (dicDataObj instanceof Map) {
                Map<String, Object> dicData = (Map<String, Object>) dicDataObj;
                Log.d(TAG, "====== RING DEBUG ====== dicData keys: " + dicData.keySet());
                
                if (dicData.containsKey("PPG")) {
                    // Extract the PPG wave data
                    Object ppgData = dicData.get("PPG");
                    Log.d(TAG, "====== RING DEBUG ====== PPG data found of type: " + ppgData.getClass().getName());
                    
                    List<Integer> ppgValues = new ArrayList<>();
                    
                    if (ppgData instanceof List) {
                        // If it's already a list, use it directly
                        ppgValues = (List<Integer>) ppgData;
                    } else if (ppgData instanceof String) {
                        // If it's a string, parse it as a list
                        String ppgString = (String) ppgData;
                        
                        // Remove brackets and spaces
                        ppgString = ppgString.trim();
                        if (ppgString.startsWith("[")) {
                            ppgString = ppgString.substring(1);
                        }
                        if (ppgString.endsWith("]")) {
                            ppgString = ppgString.substring(0, ppgString.length() - 1);
                        }
                        
                        // Split by comma
                        String[] values = ppgString.split(",");
                        for (String value : values) {
                            try {
                                // Parse each value as integer
                                ppgValues.add(Integer.parseInt(value.trim()));
                            } catch (NumberFormatException e) {
                                Log.e(TAG, "====== RING DEBUG ====== Error parsing PPG value: " + value, e);
                            }
                        }
                        
                        Log.d(TAG, "====== RING DEBUG ====== Successfully parsed String PPG data into list of " + ppgValues.size() + " values");
                    } else {
                        Log.e(TAG, "====== RING DEBUG ====== PPG data is not a List or String but is: " + ppgData.getClass().getName());
                        return;
                    }
                    
                    Log.d(TAG, "====== RING DEBUG ====== PPG values size: " + ppgValues.size());
                    if (!ppgValues.isEmpty()) {
                        Log.d(TAG, "====== RING DEBUG ====== PPG values range: first=" + ppgValues.get(0) + 
                              ", last=" + ppgValues.get(ppgValues.size()-1) + 
                              ", sample values=" + ppgValues.subList(0, Math.min(5, ppgValues.size())));
                        
                        // Convert to a format suitable for the method channel
                        final List<Integer> ppgValuesCopy = new ArrayList<>(ppgValues);
                        
                        try {
                            // Send the data to Flutter on the main thread
                            new Handler(Looper.getMainLooper()).post(() -> {
                                try {
                                    if (getFlutterEngine() != null) {
                                        Log.d(TAG, "====== RING DEBUG ====== Sending " + ppgValuesCopy.size() + " PPG data points to Flutter via onPPGDataReceived");
                                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                                            .invokeMethod("onPPGDataReceived", ppgValuesCopy, new MethodChannel.Result() {
                                                @Override
                                                public void success(Object result) {
                                                    Log.d(TAG, "====== RING DEBUG ====== Successfully sent PPG data to Flutter");
                                                }

                                                @Override
                                                public void error(String errorCode, String errorMessage, Object errorDetails) {
                                                    Log.e(TAG, "====== RING DEBUG ====== Error sending PPG data to Flutter: " + errorCode + " - " + errorMessage);
                                                }

                                                @Override
                                                public void notImplemented() {
                                                    Log.w(TAG, "====== RING DEBUG ====== Method not implemented in Flutter: onPPGDataReceived");
                                                }
                                            });
                                    } else {
                                        Log.e(TAG, "====== RING DEBUG ====== getFlutterEngine() returned null, cannot send PPG data");
                                    }
                                } catch (Exception e) {
                                    Log.e(TAG, "====== RING DEBUG ====== Exception while sending PPG data to Flutter", e);
                                }
                            });
                        } catch (Exception e) {
                            Log.e(TAG, "====== RING DEBUG ====== Failed to post to main thread", e);
                        }
                    } else {
                        Log.d(TAG, "====== RING DEBUG ====== PPG values list is empty");
                    }
                } else {
                    Log.e(TAG, "====== RING DEBUG ====== No PPG key found in dicData. Available keys: " + dicData.keySet());
                }
            } else {
                Log.e(TAG, "====== RING DEBUG ====== dicData is not a Map but is: " + dicDataObj.getClass().getName());
            }
        } else {
            Log.e(TAG, "====== RING DEBUG ====== No dicData key found. Available keys: " + data.keySet());
        }
    }
    
    /**
     * Stop the PPG wave measurement (blood glucose test)
     */
    private void stopPPGWaveV2(Result result) {
        Log.d(TAG, "====== RING DEBUG ====== Stopping PPG Wave V2 (Blood Glucose Test)");
        
        // Store the result for callback
        pendingResults.put(BleConst.ppgStop, result);


        
        // Then queue the stop command (mode 3)
        // This follows the BleSDK API pattern
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            Log.d(TAG, "====== RING DEBUG ====== Sending stop command: BleSDK.ppgWithMode(3, 0)");
            bleManager.writeValue(BleSDK.ppgWithMode(3, 0));
            
            // Return success after a short delay since the device may not respond properly
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                // Reset progress to 0
                if (getFlutterEngine() != null) {
                    new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                        .invokeMethod("onPPGProgressUpdate", 0.0, null);
                    Log.d(TAG, "====== RING DEBUG ====== Sent reset progress update to Flutter");
                }
                
                // Return success to Flutter
                Result pendingResult = pendingResults.remove(BleConst.ppgStop);
                if (pendingResult != null) {
                    pendingResult.success(true);
                    Log.d(TAG, "====== RING DEBUG ====== Returned success for stopPPGWaveV2");
                    
                    // Notify Flutter that PPG measurement has stopped
                    if (getFlutterEngine() != null) {
                        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                            .invokeMethod("onPPGStopped", null);
                    }
                }
                
                // Reset command state
                if (commandQueue != null) {
                    commandQueue.completeCommand(BleConst.Blood_glucose_status);
                    commandQueue.completeCommand(BleConst.Blood_glucose_data);
                    commandQueue.completeCommand(BleConst.ppgStop);
                    commandQueue.completeCommand(BleConst.ppgQuit);
                }
                
                // Make sure no pending results are left
                pendingResults.remove(BleConst.Blood_glucose_status);
                pendingResults.remove(BleConst.Blood_glucose_data);
                pendingResults.remove(BleConst.ppgQuit);
                
                // Clear accumulated data
                clearAccumulatedData(BleConst.Blood_glucose_data);
                
            }, 1000); // Wait 1000ms before returning success - increased from 500ms
        }, 500); // Wait 500ms before sending stop command - increased from 200ms
    }

    // Android notification helper methods
    private void isBatteryOptimizationDisabled(Result result) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
                String packageName = getPackageName();
                boolean isIgnoringBatteryOptimizations = powerManager.isIgnoringBatteryOptimizations(packageName);
                result.success(isIgnoringBatteryOptimizations);
            } else {
                // Battery optimization not available on older versions
                result.success(true);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking battery optimization: " + e.getMessage(), e);
            result.error("BATTERY_CHECK_ERROR", "Error checking battery optimization: " + e.getMessage(), null);
        }
    }

    private void requestDisableBatteryOptimization(Result result) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
                String packageName = getPackageName();

                if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                    Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                    intent.setData(Uri.parse("package:" + packageName));
                    startActivity(intent);
                    result.success(true);
                } else {
                    result.success(true);
                }
            } else {
                result.success(true);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error requesting battery optimization disable: " + e.getMessage(), e);
            result.error("BATTERY_REQUEST_ERROR", "Error requesting battery optimization disable: " + e.getMessage(), null);
        }
    }


}