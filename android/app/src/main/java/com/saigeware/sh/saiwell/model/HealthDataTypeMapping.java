package com.saigeware.sh.saiwell.model;

import java.util.Map;
import java.util.HashMap;

public class HealthDataTypeMapping {

    private static final Map<String, Integer> healthDataTypeMap = new HashMap<>();
    private static final Map<String, Integer> deleteHealthDataTypeMap = new HashMap<>();

    static {
        healthDataTypeMap.put("sport", 1);
        healthDataTypeMap.put("sleep", 2);
        healthDataTypeMap.put("heart_rate", 3);
        healthDataTypeMap.put("blood_pressure", 4);
        healthDataTypeMap.put("combined_data", 5);
        healthDataTypeMap.put("comprehensive", 10);

        deleteHealthDataTypeMap.put("sport", 101);
        deleteHealthDataTypeMap.put("sleep", 102);
        deleteHealthDataTypeMap.put("heart_rate", 103);
        deleteHealthDataTypeMap.put("blood_pressure", 104);
        deleteHealthDataTypeMap.put("combined_data", 105);
        deleteHealthDataTypeMap.put("comprehensive", 110);
    }

    public static Map<String, Integer> getHealthDataTypeMap() {
        return healthDataTypeMap;
    }

    public static Map<String, Integer> getDeleteHealthDataTypeMap() {
        return deleteHealthDataTypeMap;
    }

}
