package com.saigeware.sh.saiwell.model;

import java.util.Map;
import java.util.HashMap;

public class BleDevice {

    private String macId;
    private String name;
    private int rssi = 0;
    private boolean isConnected = false;

    public BleDevice(String macId, String name) {
        this.name = name;
        this.macId = macId;
    }

    public String getMacId() {
        return macId;
    }

    public String getName() {
        return name;
    }

    public int getRssi() {
        return rssi;
    }

    public void setRssi(int rssi) {
        this.rssi = rssi;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public void setConnected(boolean connected) {
        this.isConnected = connected;
    }

    @Override
    public String toString() {
        return "{ name: " + name + ", macId: " + macId + ", rssi: " + rssi + ", connected: " + isConnected + " }";
    }

    public Map<String, Object> toMap() {
        Map<String, Object> deviceMap = new HashMap<>();
        deviceMap.put("macId", macId);
        deviceMap.put("name", name);
        deviceMap.put("rssi", rssi);
        deviceMap.put("isConnected", isConnected);
        return deviceMap;
    }

}
