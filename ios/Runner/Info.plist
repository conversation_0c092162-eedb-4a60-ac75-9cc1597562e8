<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.saiwell.refresh</string>
		<string>com.saiwell.process</string>
		<string>task-identifier</string>
		<string>com.saiwell.sw.healthkit.background</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>SAiWELL</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>SAiWELL</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Our app uses Bluetooth to find, connect, and transfer data between different devices.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>The app uses Bluetooth to find, connect, and transfer data between different devices.</string>
	<key>NSCameraUsageDescription</key>
	<string>Our app use camera when needed</string>
	<key>NSHealthShareUsageDescription</key>
	<string>We will sync your data with the Apple Health app to give you better insights</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>We will sync your data with the Apple Health app to give you better insights</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>SAiWELL uses your location for health tracking features and to associate your health data with your current location, both while using the app and in the background.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>SAiWELL needs background location access to continuously track health metrics and provide personalized insights based on your location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>SAiWELL uses your location to enhance your health tracking experience and provide location-relevant insights while you're using the app.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>SAiWELL needs microphone access to record your voice for health assessments and analysis of respiratory patterns when using the recording feature.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<string>YES</string>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>SAiWELL requires this permission due to third-party SDK integration. We do not access or use your music library content.</string>
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>saiwell</string>
            </array>
        </dict>
    </array>
</dict>
</plist>
