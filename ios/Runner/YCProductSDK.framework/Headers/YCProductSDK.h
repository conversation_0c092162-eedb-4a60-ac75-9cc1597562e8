//
//  YCProductSDK.h
//  YCProductSDK
//
//  Created by macos on 2021/11/29.
//

#import <Foundation/Foundation.h>

//! Project version number for YCProductSDK.
FOUNDATION_EXPORT double YCProductSDKVersionNumber;

//! Project version string for YCProductSDK.
FOUNDATION_EXPORT const unsigned char YCProductSDKVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <YCProductSDK/PublicHeader.h>

#import "YCWatchFaceManager.h"
#import "YCECGAlgorithmManager.h"

