#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 5.7.2 (swiftlang-5.7.2.135.5 clang-1400.0.29.51)
#ifndef YCPRODUCTSDK_SWIFT_H
#define YCPRODUCTSDK_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wduplicate-method-match"
#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...)
# endif
#endif

#if __has_attribute(objc_runtime_name)
# define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
#else
# define SWIFT_RUNTIME_NAME(X)
#endif
#if __has_attribute(swift_name)
# define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
#else
# define SWIFT_COMPILE_NAME(X)
#endif
#if __has_attribute(objc_method_family)
# define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
#else
# define SWIFT_METHOD_FAMILY(X)
#endif
#if __has_attribute(noescape)
# define SWIFT_NOESCAPE __attribute__((noescape))
#else
# define SWIFT_NOESCAPE
#endif
#if __has_attribute(ns_consumed)
# define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
#else
# define SWIFT_RELEASES_ARGUMENT
#endif
#if __has_attribute(warn_unused_result)
# define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
#else
# define SWIFT_WARN_UNUSED_RESULT
#endif
#if __has_attribute(noreturn)
# define SWIFT_NORETURN __attribute__((noreturn))
#else
# define SWIFT_NORETURN
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif

#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif

#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif

#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if defined(__has_attribute) && __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility)
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if __has_feature(attribute_diagnose_if_objc)
# define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
#else
# define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if defined(__cplusplus)
#if !defined(SWIFT_NOEXCEPT)
# define SWIFT_NOEXCEPT noexcept
#endif
#else
#if !defined(SWIFT_NOEXCEPT)
# define SWIFT_NOEXCEPT 
#endif
#endif
#if defined(__cplusplus)
#if !defined(SWIFT_CXX_INT_DEFINED)
#define SWIFT_CXX_INT_DEFINED
namespace swift {
using Int = ptrdiff_t;
using UInt = size_t;
}
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreBluetooth;
@import CoreFoundation;
@import Foundation;
@import ObjectiveC;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="YCProductSDK",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)
@class YCProductFunctionSupportItems;
@class YCDeviceBasicInfo;
enum YCDeviceMCUType : uint8_t;
@class NSString;
@class NSData;

@interface CBPeripheral (SWIFT_EXTENSION(YCProductSDK))
/// 支持功能列表
@property (nonatomic, readonly, strong) YCProductFunctionSupportItems * _Nonnull supportItems;
/// 基本信息
@property (nonatomic, strong) YCDeviceBasicInfo * _Nonnull basicInfo;
/// 获取硬件平台
@property (nonatomic, readonly) enum YCDeviceMCUType mcu;
/// 信号值
@property (nonatomic, readonly) NSInteger rssiValue;
/// mac地址
@property (nonatomic, readonly, copy) NSString * _Nonnull macAddress;
/// 设备型号
@property (nonatomic, copy) NSString * _Nonnull deviceModel;
/// 搜索的广播包数据
@property (nonatomic, readonly, copy) NSData * _Nonnull advDataManufacturerData;
/// 打印信息
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
@end

/// App 启动测量数据的测量方式
typedef SWIFT_ENUM(uint8_t, YCAppControlHealthDataMeasureType, open) {
  YCAppControlHealthDataMeasureTypeOff = 0,
  YCAppControlHealthDataMeasureTypeSingle = 1,
  YCAppControlHealthDataMeasureTypeMonitor = 2,
};

/// App 启动测量结果
typedef SWIFT_ENUM(uint8_t, YCAppControlMeasureHealthDataResult, open) {
  YCAppControlMeasureHealthDataResultExit = 0,
  YCAppControlMeasureHealthDataResultSuccess = 1,
  YCAppControlMeasureHealthDataResultFail = 2,
  YCAppControlMeasureHealthDataResultEnter = 3,
};

/// App 启动测量数据类型
typedef SWIFT_ENUM(uint8_t, YCAppControlMeasureHealthDataType, open) {
  YCAppControlMeasureHealthDataTypeHeartRate = 0,
  YCAppControlMeasureHealthDataTypeBloodPressure = 1,
  YCAppControlMeasureHealthDataTypeBloodOxygen = 2,
  YCAppControlMeasureHealthDataTypeRespirationRate = 3,
  YCAppControlMeasureHealthDataTypeBodyTemperature = 4,
  YCAppControlMeasureHealthDataTypeBloodGlucose = 5,
  YCAppControlMeasureHealthDataTypeUricAcid = 6,
  YCAppControlMeasureHealthDataTypeBloodKetone = 7,
  YCAppControlMeasureHealthDataTypeEda = 8,
  YCAppControlMeasureHealthDataTypeClinicalMode = 0x70,
  YCAppControlMeasureHealthDataTypeUnknow = 0xFF,
};

/// 测量模式
typedef SWIFT_ENUM(uint8_t, YCAppControlMeasureMode, open) {
  YCAppControlMeasureModeOff = 0,
  YCAppControlMeasureModeSingle = 1,
  YCAppControlMeasureModeMonitor = 2,
};

/// 血糖校准模式
typedef SWIFT_ENUM(uint8_t, YCBloodGlucoseCalibrationaMode, open) {
  YCBloodGlucoseCalibrationaModeFasting = 0,
  YCBloodGlucoseCalibrationaModeAfterBreakfast = 1,
  YCBloodGlucoseCalibrationaModeBeforeLunch = 2,
  YCBloodGlucoseCalibrationaModeAfterLunch = 3,
  YCBloodGlucoseCalibrationaModeBeforeDinner = 4,
  YCBloodGlucoseCalibrationaModeAfterDinner = 5,
};

/// 二维码颜色
typedef SWIFT_ENUM(uint8_t, YCBodyTemperatureQRCodeColor, open) {
  YCBodyTemperatureQRCodeColorGreen = 0,
  YCBodyTemperatureQRCodeColorRed = 1,
  YCBodyTemperatureQRCodeColorOrange = 2,
};

/// 名片类型
typedef SWIFT_ENUM(uint8_t, YCBusinessCardType, open) {
  YCBusinessCardTypeWechat = 0,
  YCBusinessCardTypeQq = 1,
  YCBusinessCardTypeFacebook = 2,
  YCBusinessCardTypeTwitter = 3,
  YCBusinessCardTypeWhatsapp = 4,
  YCBusinessCardTypeInstagram = 5,
  YCBusinessCardTypeSnCode = 0xF0,
  YCBusinessCardTypeStaticCode = 0xF1,
  YCBusinessCardTypeDynamicCode = 0xF2,
};

enum YCCollectDataType : uint8_t;

/// 历史采集数据的基本信息
SWIFT_CLASS("_TtC12YCProductSDK22YCCollectDataBasicInfo")
@interface YCCollectDataBasicInfo : NSObject
/// 类型
@property (nonatomic, readonly) enum YCCollectDataType dataType;
/// 序号
@property (nonatomic, readonly) uint16_t index;
/// 时间戳
@property (nonatomic, readonly) uint32_t timeStamp;
/// 采样率
@property (nonatomic, readonly) uint16_t sampleRate;
/// 样本位数
@property (nonatomic, readonly) uint8_t samplesCount;
/// 总字节数
@property (nonatomic, readonly) uint32_t totalBytes;
/// 总包数
@property (nonatomic, readonly) uint16_t packages;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class NSMutableData;

/// 历史采集数据信息
SWIFT_CLASS("_TtC12YCProductSDK17YCCollectDataInfo")
@interface YCCollectDataInfo : NSObject
/// 基础类型
@property (nonatomic, readonly, strong) YCCollectDataBasicInfo * _Nonnull basicInfo;
/// 进度
@property (nonatomic, readonly) double progress;
/// 已经传输的字节数
@property (nonatomic, readonly, strong) NSMutableData * _Nonnull receivcedData;
/// 是否传输完成
@property (nonatomic, readonly) BOOL isFinished;
/// 响应数据
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull data;
/// 衍生数据 (由 data 加工得到)
@property (nonatomic, readonly, copy) NSArray * _Nonnull processedData;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 采集数据类型
typedef SWIFT_ENUM(uint8_t, YCCollectDataType, open) {
  YCCollectDataTypeEcg = 0,
  YCCollectDataTypePpg = 1,
  YCCollectDataTypeTriaxialAcceleration = 2,
  YCCollectDataTypeSixAxisSensor = 3,
  YCCollectDataTypeNineAxisSensor = 4,
  YCCollectDataTypeTriaxialMagnetometer = 5,
  YCCollectDataTypeInflationBloodPressure = 6,
  YCCollectDataTypePpi = 7,
  YCCollectDataTypeEda = 8,
};

enum YCCustomCGMDeviceInfoType : uint8_t;

/// 设备信息
SWIFT_CLASS("_TtC12YCProductSDK21YCCustomCGMDeviceInfo")
@interface YCCustomCGMDeviceInfo : NSObject
/// 设备信息
@property (nonatomic, readonly) enum YCCustomCGMDeviceInfoType infoType;
/// 时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 序列号
@property (nonatomic, readonly, copy) NSString * _Nonnull serialNumber;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 查询乔图CGM的设备信息
typedef SWIFT_ENUM(uint8_t, YCCustomCGMDeviceInfoType, open) {
  YCCustomCGMDeviceInfoTypeNone = 0,
  YCCustomCGMDeviceInfoTypeStartTimeAndSerialNumber = 1,
};


/// CGM相关值
SWIFT_CLASS("_TtC12YCProductSDK21YCCustomDeviceCGMData")
@interface YCCustomDeviceCGMData : NSObject
/// 轮次
@property (nonatomic, readonly) NSInteger round;
/// 血糖值
@property (nonatomic, readonly) double bloodGlucose;
/// 原始值
@property (nonatomic, readonly) double quality;
/// 显示字符串
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 定制设备数据类型
typedef SWIFT_ENUM(uint8_t, YCCustomDeviceDataType, open) {
  YCCustomDeviceDataTypeNone = 0,
  YCCustomDeviceDataTypeCgmBloodGlucose = 1,
  YCCustomDeviceDataTypePhysiotherapyRecords = 2,
};

enum YCDevicePhysiotherapyOperationType : uint8_t;
enum YCDevicePhysiotherapyStartupType : uint8_t;

/// 理疗记录
SWIFT_CLASS("_TtC12YCProductSDK33YCCustomDevicePhysiotherapyRecord")
@interface YCCustomDevicePhysiotherapyRecord : NSObject
/// 开始时间 (时间戳 秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 理疗时长 (秒)
@property (nonatomic, readonly) NSInteger duration;
/// 理疗运行类型
@property (nonatomic, readonly) enum YCDevicePhysiotherapyOperationType operationType;
/// 理疗启动类型
@property (nonatomic, readonly) enum YCDevicePhysiotherapyStartupType startupType;
/// 理疗强度等级
@property (nonatomic, readonly) uint8_t powerLevel;
/// 理疗强度等级
@property (nonatomic, readonly) uint8_t durationLevel;
/// 显示字符串
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 删除健康数据的类型定义
typedef SWIFT_ENUM(uint8_t, YCDeleteHealthDataType, open) {
  YCDeleteHealthDataTypeStep = 0x40,
  YCDeleteHealthDataTypeSleep = 0x41,
  YCDeleteHealthDataTypeHeartRate = 0x42,
  YCDeleteHealthDataTypeBloodPressure = 0x43,
  YCDeleteHealthDataTypeCombinedData = 0x44,
  YCDeleteHealthDataTypeBloodOxygen = 0x45,
  YCDeleteHealthDataTypeTemperatureHumidity = 0x46,
  YCDeleteHealthDataTypeBodyTemperature = 0x47,
  YCDeleteHealthDataTypeAmbientLight = 0x48,
  YCDeleteHealthDataTypeWearState = 0x49,
  YCDeleteHealthDataTypeHealthMonitoringData = 0x4A,
  YCDeleteHealthDataTypeSportModeHistoryData = 0x4B,
  YCDeleteHealthDataTypeInvasiveComprehensiveData = 0x4C,
};

/// 日程的时间类型
typedef SWIFT_ENUM(uint8_t, YCDevcieScheduleEventType, open) {
  YCDevcieScheduleEventTypeGetUp = 0,
  YCDevcieScheduleEventTypeBreakfast = 1,
  YCDevcieScheduleEventTypeBaskInTheSun = 2,
  YCDevcieScheduleEventTypeLunch = 3,
  YCDevcieScheduleEventTypeLunchBreak = 4,
  YCDevcieScheduleEventTypeSports = 5,
  YCDevcieScheduleEventTypeDinner = 6,
  YCDevcieScheduleEventTypeSleep = 7,
  YCDevcieScheduleEventTypeCustomize = 8,
};

/// 指定消息类型
typedef SWIFT_ENUM(uint8_t, YCDevcieSpecifyMessageType, open) {
  YCDevcieSpecifyMessageTypeOk = 0,
  YCDevcieSpecifyMessageTypeWarning = 1,
  YCDevcieSpecifyMessageTypeAlert = 2,
  YCDevcieSpecifyMessageTypeMessage = 3,
};

enum YCDeviceAlarmType : uint8_t;
@class NSNumber;

/// 闹钟信息
SWIFT_CLASS("_TtC12YCProductSDK17YCDeviceAlarmInfo")
@interface YCDeviceAlarmInfo : NSObject
/// 设备允许的最多闹钟数量
@property (nonatomic, readonly) uint8_t limitCount;
/// 闹钟类型
@property (nonatomic, readonly) enum YCDeviceAlarmType alarmType;
/// 闹钟中的小时 (24小时制)
@property (nonatomic, readonly) uint8_t hour;
/// 闹钟中的分钟
@property (nonatomic, readonly) uint8_t minute;
/// 重复时间集合(和 repeat` 取值相同) YCDeviceWeekRepeat 类型
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull repeatArray;
/// 贪睡时间 min
@property (nonatomic, readonly) uint8_t snoozeTime;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 闹钟操作类型
typedef SWIFT_ENUM(uint8_t, YCDeviceAlarmOperatoreType, open) {
  YCDeviceAlarmOperatoreTypeQuery = 0,
  YCDeviceAlarmOperatoreTypeAdd = 1,
  YCDeviceAlarmOperatoreTypeDelete = 2,
  YCDeviceAlarmOperatoreTypeModify = 3,
};

/// 闹钟类型
typedef SWIFT_ENUM(uint8_t, YCDeviceAlarmType, open) {
  YCDeviceAlarmTypeWakeUp = 0,
  YCDeviceAlarmTypeSleep = 1,
  YCDeviceAlarmTypeExercise = 2,
  YCDeviceAlarmTypeMedicine = 3,
  YCDeviceAlarmTypeAppointment = 4,
  YCDeviceAlarmTypeParty = 5,
  YCDeviceAlarmTypeMeeting = 6,
  YCDeviceAlarmTypeCustom = 7,
};

/// 支付宝状态
typedef SWIFT_ENUM(uint8_t, YCDeviceAlipayState, open) {
  YCDeviceAlipayStateNotActive = 0,
  YCDeviceAlipayStateActivated = 1,
};


/// 查询环境光
SWIFT_CLASS("_TtC12YCProductSDK20YCDeviceAmbientLight")
@interface YCDeviceAmbientLight : NSObject
/// 是否正在测量
@property (nonatomic, readonly) BOOL isMeasuring;
/// 环境光
@property (nonatomic, readonly) uint16_t ambientLight;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// *******/8 防丢提醒设置
typedef SWIFT_ENUM(uint8_t, YCDeviceAntiLostType, open) {
  YCDeviceAntiLostTypeOff = 0,
  YCDeviceAntiLostTypeCloseRange = 1,
  YCDeviceAntiLostTypeMiddleDistance = 2,
  YCDeviceAntiLostTypeLongDistance = 3,
};

@class YCDeviceVersionInfo;
enum YCDeviceBatterystate : uint8_t;
enum YCDeviceType : uint8_t;

/// 基本信息
SWIFT_CLASS("_TtC12YCProductSDK17YCDeviceBasicInfo")
@interface YCDeviceBasicInfo : NSObject
/// 设备ID
@property (nonatomic, readonly) uint16_t deviceID;
/// 主控MCU
@property (nonatomic, readonly, strong) YCDeviceVersionInfo * _Nonnull mcuFirmware;
/// 电池状态
@property (nonatomic, readonly) enum YCDeviceBatterystate batterystatus;
/// 电池电量
@property (nonatomic, readonly) uint8_t batteryPower;
/// 是否绑定 (保留参数)
@property (nonatomic, readonly) BOOL isBind;
/// 是否需要同步 (保留参数)
@property (nonatomic, readonly) BOOL needSync;
/// 内部通讯通讯协议版本(内部使用)
@property (nonatomic, readonly, strong) YCDeviceVersionInfo * _Nonnull innerProtocol;
/// 血压固件信息
@property (nonatomic, readonly, strong) YCDeviceVersionInfo * _Nonnull bloodPressureFirmware;
/// TP固件信息
@property (nonatomic, readonly, strong) YCDeviceVersionInfo * _Nonnull touchPanelFirmware;
/// 血糖固件信息
@property (nonatomic, readonly, strong) YCDeviceVersionInfo * _Nonnull bloodGlucoseFirmware;
/// UI信息
@property (nonatomic, readonly, strong) YCDeviceVersionInfo * _Nonnull uiInfo;
/// 设备类型
@property (nonatomic, readonly) enum YCDeviceType deviceType;
/// 显示信息
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 电池电量状态
typedef SWIFT_ENUM(uint8_t, YCDeviceBatterystate, open) {
  YCDeviceBatterystateNormal = 0,
  YCDeviceBatterystateLow = 1,
  YCDeviceBatterystateCharging = 2,
  YCDeviceBatterystateFull = 3,
};

/// 血糖/血脂单位
typedef SWIFT_ENUM(uint8_t, YCDeviceBloodGlucoseType, open) {
  YCDeviceBloodGlucoseTypeMillimolePerLiter = 0,
  YCDeviceBloodGlucoseTypeMilligramsPerDeciliter = 1,
};


/// 查询血氧
SWIFT_CLASS("_TtC12YCProductSDK19YCDeviceBloodOxygen")
@interface YCDeviceBloodOxygen : NSObject
/// 是否正在测量
@property (nonatomic, readonly) BOOL isMeasuring;
/// 血氧
@property (nonatomic, readonly) uint8_t bloodOxygen;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 3.5.2.23 血压范围设置(等级)
typedef SWIFT_ENUM(uint8_t, YCDeviceBloodPressureLevel, open) {
  YCDeviceBloodPressureLevelLow = 0,
  YCDeviceBloodPressureLevelNormal = 1,
  YCDeviceBloodPressureLevelSlightlyHigh = 2,
  YCDeviceBloodPressureLevelModeratelyHigh = 3,
  YCDeviceBloodPressureLevelSevereHigh = 4,
};

/// *******4 息屏时间
typedef SWIFT_ENUM(uint8_t, YCDeviceBreathScreenInterval, open) {
  YCDeviceBreathScreenIntervalFive = 0,
  YCDeviceBreathScreenIntervalTen = 1,
  YCDeviceBreathScreenIntervalFifteen = 2,
  YCDeviceBreathScreenIntervalThirty = 3,
  YCDeviceBreathScreenIntervalTwenty = 4,
  YCDeviceBreathScreenIntervalTwentyFive = 5,
};

/// 自检类型
typedef SWIFT_ENUM(uint16_t, YCDeviceCheckType, open) {
  YCDeviceCheckTypeAuto = 0,
  YCDeviceCheckTypeGSensor = 1,
  YCDeviceCheckTypePpg = 2,
  YCDeviceCheckTypeEcg = 3,
  YCDeviceCheckTypeTemperature = 4,
};


/// 精准血压测量结束测量返回信息
SWIFT_CLASS("_TtC12YCProductSDK54YCDeviceControlAccurateBloodPressureMeasurementEndInfo")
@interface YCDeviceControlAccurateBloodPressureMeasurementEndInfo : NSObject
/// 是否结束
@property (nonatomic, readonly) BOOL isSuccess;
/// 测量收缩压
@property (nonatomic, readonly) uint8_t measureSystolicBloodPressure;
/// 测量舒张压
@property (nonatomic, readonly) uint8_t measureDiastolicBloodPressure;
/// 测量心率
@property (nonatomic, readonly) uint8_t measureHeartRate;
/// 用户输入收缩压
@property (nonatomic, readonly) uint8_t inputSystolicBloodPressure;
/// 用户输入舒张压
@property (nonatomic, readonly) uint8_t inputDiastolicBloodPressure;
/// 用户输入心率
@property (nonatomic, readonly) uint8_t inputHeartRate;
/// 字符串表示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 设备允许连接状态
typedef SWIFT_ENUM(uint8_t, YCDeviceControlAllowConnectionState, open) {
  YCDeviceControlAllowConnectionStateAgree = 0,
  YCDeviceControlAllowConnectionStateRefuse = 1,
};

/// 设备控制的运动模式
typedef SWIFT_ENUM(uint8_t, YCDeviceControlDeviceSportState, open) {
  YCDeviceControlDeviceSportStateRunning = 0,
  YCDeviceControlDeviceSportStateFreeMove = 1,
  YCDeviceControlDeviceSportStateStop = 2,
  YCDeviceControlDeviceSportStateStart = 3,
  YCDeviceControlDeviceSportStatePause = 4,
  YCDeviceControlDeviceSportStateContinue = 5,
  YCDeviceControlDeviceSportStateUnknow = 0xFF,
};

/// 设备动态码状态
typedef SWIFT_ENUM(uint8_t, YCDeviceControlDynamicCodeState, open) {
  YCDeviceControlDynamicCodeStateRequestCode = 0,
  YCDeviceControlDynamicCodeStateEnd = 1,
};

enum YCEmbeddedPeripheralFirmwareType : uint8_t;

/// 嵌入式外设固件下载状态
SWIFT_CLASS("_TtC12YCProductSDK57YCDeviceControlEmbeddedPeripheralFirmwareUpgradeStateInfo")
@interface YCDeviceControlEmbeddedPeripheralFirmwareUpgradeStateInfo : NSObject
/// 固件类型
@property (nonatomic, readonly) enum YCEmbeddedPeripheralFirmwareType firmwareType;
/// 是否成功
@property (nonatomic, readonly) BOOL isSuccess;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 有创测量状态
typedef SWIFT_ENUM(uint8_t, YCDeviceControlInvasiveMeasurementState, open) {
  YCDeviceControlInvasiveMeasurementStatePlugIn = 0,
  YCDeviceControlInvasiveMeasurementStatePlugOut = 1,
  YCDeviceControlInvasiveMeasurementStateTestStripIn = 2,
  YCDeviceControlInvasiveMeasurementStateTestStripOut = 3,
  YCDeviceControlInvasiveMeasurementStateMeasuredValue = 4,
  YCDeviceControlInvasiveMeasurementStateEepromReadError = 5,
  YCDeviceControlInvasiveMeasurementStateEepromWriteError = 6,
  YCDeviceControlInvasiveMeasurementStateTemperatureOutOfBounds = 7,
  YCDeviceControlInvasiveMeasurementStateMeasurementInterruption = 8,
  YCDeviceControlInvasiveMeasurementStateParameterError = 9,
  YCDeviceControlInvasiveMeasurementStateCommunicationError = 10,
  YCDeviceControlInvasiveMeasurementStateWrongTestStrip = 11,
  YCDeviceControlInvasiveMeasurementStateMeasurementCountdown = 12,
};


/// 有创测量状态信息
SWIFT_CLASS("_TtC12YCProductSDK43YCDeviceControlInvasiveMeasurementStateInfo")
@interface YCDeviceControlInvasiveMeasurementStateInfo : NSObject
/// 测量方式
@property (nonatomic, readonly) enum YCAppControlMeasureHealthDataType dataType;
/// 外挂状态
@property (nonatomic, readonly) enum YCDeviceControlInvasiveMeasurementState state;
/// 测量值集合
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull values;
/// 测量值
@property (nonatomic, readonly) id _Nonnull measredValue;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 单次测量结果
SWIFT_CLASS("_TtC12YCProductSDK42YCDeviceControlMeasureHealthDataResultInfo")
@interface YCDeviceControlMeasureHealthDataResultInfo : NSObject
@property (nonatomic, readonly) enum YCAppControlMeasureHealthDataResult state;
@property (nonatomic, readonly) enum YCAppControlMeasureHealthDataType dataType;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 设备控制拍照
typedef SWIFT_ENUM(uint8_t, YCDeviceControlPhotoState, open) {
  YCDeviceControlPhotoStateExit = 0,
  YCDeviceControlPhotoStateEnter = 1,
  YCDeviceControlPhotoStatePhoto = 2,
};


/// 设备上取的预警值
SWIFT_CLASS("_TtC12YCProductSDK37YCDeviceControlReportWarningValueInfo")
@interface YCDeviceControlReportWarningValueInfo : NSObject
@property (nonatomic, readonly) enum YCAppControlMeasureHealthDataType dataType;
/// 值的集合
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull values;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

enum YCDeviceSportState : uint8_t;
enum YCDeviceSportType : uint8_t;

/// 控制运动模式信息
SWIFT_CLASS("_TtC12YCProductSDK35YCDeviceControlSportModeControlInfo")
@interface YCDeviceControlSportModeControlInfo : NSObject
@property (nonatomic, readonly) enum YCDeviceSportState state;
@property (nonatomic, readonly) enum YCDeviceSportType sportType;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 设备控制的一般状态
typedef SWIFT_ENUM(uint8_t, YCDeviceControlState, open) {
  YCDeviceControlStateStop = 0,
  YCDeviceControlStateStart = 1,
};

/// 设备控制的数据类型
typedef SWIFT_ENUM(uint8_t, YCDeviceControlType, open) {
  YCDeviceControlTypeFindPhone = 0,
  YCDeviceControlTypeAntiLost = 0x01,
  YCDeviceControlTypePhoto = 0x03,
  YCDeviceControlTypeSos = 0x05,
  YCDeviceControlTypeAllowConnection = 0x07,
  YCDeviceControlTypeSportMode = 0x08,
  YCDeviceControlTypeReset = 0x0A,
  YCDeviceControlTypeStopRealTimeECGMeasurement = 0x0B,
  YCDeviceControlTypeSportModeControl = 0x0C,
  YCDeviceControlTypeSwitchWatchFace = 0x0D,
  YCDeviceControlTypeHealthDataMeasurementResult = 0x0E,
  YCDeviceControlTypeReportWarningValue = 0x0F,
  YCDeviceControlTypeAccurateBloodPressureMeasurementStop = 0x10,
  YCDeviceControlTypeDeviceFirmwareDownloadState = 0x11,
  YCDeviceControlTypePpi = 0x12,
  YCDeviceControlTypeInvasiveMeasurementState = 0x13,
  YCDeviceControlTypeAlipayActivationState = 0x14,
  YCDeviceControlTypeDynamicCodeState = 0x15,
};


/// 查询当前血压
SWIFT_CLASS("_TtC12YCProductSDK28YCDeviceCurrentBloodPressure")
@interface YCDeviceCurrentBloodPressure : NSObject
/// 是否正在测量
@property (nonatomic, readonly) BOOL isMeasuring;
/// 收缩压
@property (nonatomic, readonly) uint8_t systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) uint8_t diastolicBloodPressure;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 当前运动
SWIFT_CLASS("_TtC12YCProductSDK23YCDeviceCurrentExercise")
@interface YCDeviceCurrentExercise : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 查询当前心率
SWIFT_CLASS("_TtC12YCProductSDK24YCDeviceCurrentHeartRate")
@interface YCDeviceCurrentHeartRate : NSObject
/// 是否正在测量
@property (nonatomic, readonly) BOOL isMeasuring;
/// 心率值
@property (nonatomic, readonly) uint8_t heartRate;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 3.5.2.28 数据采集配置
typedef SWIFT_ENUM(uint8_t, YCDeviceDataCollectionType, open) {
  YCDeviceDataCollectionTypePpg = 0,
  YCDeviceDataCollectionTypeAcceleration = 1,
  YCDeviceDataCollectionTypeEcg = 2,
  YCDeviceDataCollectionTypeTemperatureHumidity = 3,
  YCDeviceDataCollectionTypeAmbientLight = 4,
  YCDeviceDataCollectionTypeBodyTemperature = 5,
  YCDeviceDataCollectionTypeHeartRate = 6,
};

/// 自检错误码
typedef SWIFT_ENUM(NSUInteger, YCDeviceDetectionErrorType, open) {
  YCDeviceDetectionErrorTypeGSensor = 1,
  YCDeviceDetectionErrorTypePpg = 2,
  YCDeviceDetectionErrorTypePpgG = 4,
  YCDeviceDetectionErrorTypePpgR = 8,
  YCDeviceDetectionErrorTypePpgIR = 16,
  YCDeviceDetectionErrorTypeEcg = 32,
  YCDeviceDetectionErrorTypeTemp = 64,
  YCDeviceDetectionErrorTypeLcd = 128,
};

/// 3.5.2.21 显示屏亮度设置
typedef SWIFT_ENUM(uint8_t, YCDeviceDisplayBrightnessLevel, open) {
  YCDeviceDisplayBrightnessLevelLow = 0,
  YCDeviceDisplayBrightnessLevelMiddle = 1,
  YCDeviceDisplayBrightnessLevelHigh = 2,
  YCDeviceDisplayBrightnessLevelAutomatic = 3,
  YCDeviceDisplayBrightnessLevelLower = 4,
  YCDeviceDisplayBrightnessLevelHigher = 5,
};

enum YCDeviceScreenType : NSInteger;

/// 显示参数信息
SWIFT_CLASS("_TtC12YCProductSDK29YCDeviceDisplayParametersInfo")
@interface YCDeviceDisplayParametersInfo : NSObject
/// 屏幕类型
@property (nonatomic) enum YCDeviceScreenType screenType;
/// 宽度（像素）
@property (nonatomic) uint16_t widthPixels;
/// 高度（像素）
@property (nonatomic) uint16_t heightPixels;
/// 圆角半径 (像素)
@property (nonatomic) uint16_t filletRadiusPixels;
/// 缩略图宽度 (像素)
@property (nonatomic) uint16_t thumbnailWidthPixels;
/// 缩略图高度 (像素)
@property (nonatomic) uint16_t thumbnailHeightPixels;
/// 缩略图半径 (像素)
@property (nonatomic) uint16_t thumbnailRadiusPixels;
/// 打印信息
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 距离单位
typedef SWIFT_ENUM(uint8_t, YCDeviceDistanceType, open) {
  YCDeviceDistanceTypeKm = 0,
  YCDeviceDistanceTypeMile = 1,
};

/// 心电电极位置
typedef SWIFT_ENUM(uint8_t, YCDeviceElectrodePosition, open) {
  YCDeviceElectrodePositionRight = 0,
  YCDeviceElectrodePositionBottom = 1,
  YCDeviceElectrodePositionBothSides = 2,
  YCDeviceElectrodePositionFullEncirclement = 3,
};

enum YCDeviceEventInterval : uint8_t;

/// 事件信息
SWIFT_CLASS("_TtC12YCProductSDK17YCDeviceEventInfo")
@interface YCDeviceEventInfo : NSObject
/// 事件ID
@property (nonatomic, readonly) uint8_t eventID;
/// 是否开启
@property (nonatomic, readonly) BOOL isEnable;
/// 时间
@property (nonatomic, readonly) uint8_t hour;
/// 分钟
@property (nonatomic, readonly) uint8_t minute;
/// 重复时间集合(和 repeat` 取值相同) YCDeviceWeekRepeat 类型
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull repeatArray;
/// 间隔
@property (nonatomic, readonly) enum YCDeviceEventInterval interval;
/// 名称
@property (nonatomic, readonly, copy) NSString * _Nonnull name;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// *******4 事件间隔时间
typedef SWIFT_ENUM(uint8_t, YCDeviceEventInterval, open) {
  YCDeviceEventIntervalNone = 0,
  YCDeviceEventIntervalTen = 10,
  YCDeviceEventIntervalTwenty = 20,
  YCDeviceEventIntervalThirty = 30,
};

/// 运动状态类型
typedef SWIFT_ENUM(uint8_t, YCDeviceExerciseHeartRateType, open) {
  YCDeviceExerciseHeartRateTypeRetreat = 0,
  YCDeviceExerciseHeartRateTypeCasualwarmup = 1,
  YCDeviceExerciseHeartRateTypeCardiorespiratoryStrengthening = 2,
  YCDeviceExerciseHeartRateTypeReduceFatShape = 3,
  YCDeviceExerciseHeartRateTypeSportsLimit = 4,
  YCDeviceExerciseHeartRateTypeEmptyState = 5,
};

/// 时间方向位置
typedef SWIFT_ENUM(uint8_t, YCDeviceFaceTimePosition, open) {
  YCDeviceFaceTimePositionTop = 1,
  YCDeviceFaceTimePositionBottom = 2,
  YCDeviceFaceTimePositionLeft = 3,
  YCDeviceFaceTimePositionRight = 4,
  YCDeviceFaceTimePositionLeftTop = 5,
  YCDeviceFaceTimePositionRightTop = 6,
  YCDeviceFaceTimePositionLeftBottom = 7,
  YCDeviceFaceTimePositionRightBottom = 8,
  YCDeviceFaceTimePositionCenter = 9,
};

/// 3.5.2.4 用户信息设置
typedef SWIFT_ENUM(uint8_t, YCDeviceGender, open) {
  YCDeviceGenderMale = 0,
  YCDeviceGenderFemale = 1,
};


/// 历史记录概要信息
SWIFT_CLASS("_TtC12YCProductSDK22YCDeviceHistorySummary")
@interface YCDeviceHistorySummary : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 3.5.2.11 通知提醒类型
typedef SWIFT_ENUM(NSUInteger, YCDeviceInfoPushType, open) {
  YCDeviceInfoPushTypeCall = 0,
  YCDeviceInfoPushTypeSms = 1,
  YCDeviceInfoPushTypeEmail = 2,
  YCDeviceInfoPushTypeWechat = 3,
  YCDeviceInfoPushTypeQq = 4,
  YCDeviceInfoPushTypeWeibo = 5,
  YCDeviceInfoPushTypeFacebook = 6,
  YCDeviceInfoPushTypeTwitter = 7,
  YCDeviceInfoPushTypeMessenger = 8,
  YCDeviceInfoPushTypeWhatsAPP = 9,
  YCDeviceInfoPushTypeLinkedIn = 10,
  YCDeviceInfoPushTypeInstagram = 11,
  YCDeviceInfoPushTypeSkype = 12,
  YCDeviceInfoPushTypeLine = 13,
  YCDeviceInfoPushTypeSnapchat = 14,
  YCDeviceInfoPushTypeTelegram = 15,
  YCDeviceInfoPushTypeOther = 16,
  YCDeviceInfoPushTypeViber = 17,
  YCDeviceInfoPushTypeZoom = 18,
  YCDeviceInfoPushTypeTiktok = 19,
  YCDeviceInfoPushTypeKakaoTalk = 20,
};

/// 3.5.2.19 语言设置
typedef SWIFT_ENUM(uint8_t, YCDeviceLanguageType, open) {
  YCDeviceLanguageTypeEnglish = 0x00,
  YCDeviceLanguageTypeChineseSimplified = 0x01,
  YCDeviceLanguageTypeRussian = 0x02,
  YCDeviceLanguageTypeGerman = 0x03,
  YCDeviceLanguageTypeFrench = 0x04,
  YCDeviceLanguageTypeJapanese = 0x05,
  YCDeviceLanguageTypeSpanish = 0x06,
  YCDeviceLanguageTypeItalian = 0x07,
  YCDeviceLanguageTypePortuguese = 0x08,
  YCDeviceLanguageTypeKorean = 0x09,
  YCDeviceLanguageTypePoland = 0x0A,
  YCDeviceLanguageTypeMalay = 0x0B,
  YCDeviceLanguageTypeChineseTradition = 0x0C,
  YCDeviceLanguageTypeThai = 0x0D,
  YCDeviceLanguageTypeVietnamese = 0x0F,
  YCDeviceLanguageTypeHungarian = 0x10,
  YCDeviceLanguageTypeArabic = 0x1A,
  YCDeviceLanguageTypeGreek = 0x1B,
  YCDeviceLanguageTypeMalaysian = 0x1C,
  YCDeviceLanguageTypeHebrew = 0x1D,
  YCDeviceLanguageTypeFinnish = 0x1E,
  YCDeviceLanguageTypeCzech = 0x1F,
  YCDeviceLanguageTypeCroatian = 0x20,
  YCDeviceLanguageTypePersian = 0x24,
  YCDeviceLanguageTypeUkrainian = 0x27,
  YCDeviceLanguageTypeTurkish = 0x28,
  YCDeviceLanguageTypeDanish = 0x2B,
  YCDeviceLanguageTypeSwedish = 0x2C,
  YCDeviceLanguageTypeNorwegian = 0x2D,
  YCDeviceLanguageTypeRomanian = 0x32,
  YCDeviceLanguageTypeSlovak = 0x34,
};

/// MCU
typedef SWIFT_ENUM(uint8_t, YCDeviceMCUType, open) {
  YCDeviceMCUTypeNrf52832 = 0,
  YCDeviceMCUTypeRtk8762c = 1,
  YCDeviceMCUTypeRtk8762d = 2,
  YCDeviceMCUTypeJl701n = 3,
  YCDeviceMCUTypeJl632n = 4,
};

/// 马达震动类型
typedef SWIFT_ENUM(uint8_t, YCDeviceMotorVibrationType, open) {
  YCDeviceMotorVibrationTypeAlarm = 0,
};

/// 理疗运行类型
typedef SWIFT_ENUM(uint8_t, YCDevicePhysiotherapyOperationType, open) {
  YCDevicePhysiotherapyOperationTypeContinuous = 0,
  YCDevicePhysiotherapyOperationTypePulse = 1,
};

/// 理疗启动类型
typedef SWIFT_ENUM(uint8_t, YCDevicePhysiotherapyStartupType, open) {
  YCDevicePhysiotherapyStartupTypeManual = 0,
  YCDevicePhysiotherapyStartupTypeAutomatic = 1,
};

/// 设备实时状态
typedef SWIFT_ENUM(uint8_t, YCDeviceRealState, open) {
  YCDeviceRealStateGood = 0,
  YCDeviceRealStateFallof = 1,
  YCDeviceRealStateFault = 2,
};

/// 提醒设置
typedef SWIFT_ENUM(uint8_t, YCDeviceRemindType, open) {
  YCDeviceRemindTypeDeviceDisconnected = 0,
  YCDeviceRemindTypeSportsCompliance = 1,
};

/// 提醒设置状态
typedef SWIFT_ENUM(uint8_t, YCDeviceReminderSettingState, open) {
  YCDeviceReminderSettingStateOff = 0,
  YCDeviceReminderSettingStateOn = 1,
};


/// 日程信息
SWIFT_CLASS("_TtC12YCProductSDK20YCDeviceScheduleInfo")
@interface YCDeviceScheduleInfo : NSObject
@property (nonatomic, readonly) uint8_t scheduleIndex;
@property (nonatomic, readonly) BOOL scheduleEnable;
@property (nonatomic, readonly) uint8_t eventIndex;
@property (nonatomic, readonly) BOOL eventEnable;
@property (nonatomic, readonly) uint32_t eventTime;
@property (nonatomic, readonly) enum YCDevcieScheduleEventType eventType;
@property (nonatomic, readonly, copy) NSString * _Nonnull eventName;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 获取屏幕显示信息
SWIFT_CLASS("_TtC12YCProductSDK25YCDeviceScreenDisplayInfo")
@interface YCDeviceScreenDisplayInfo : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 基本信息
SWIFT_CLASS("_TtC12YCProductSDK18YCDeviceScreenInfo")
@interface YCDeviceScreenInfo : NSObject
/// 屏宽度(单位:像素)
@property (nonatomic, readonly) uint16_t screenWidth;
/// 屏高度(单位:像素)
@property (nonatomic, readonly) uint16_t screenHeight;
/// 字体宽度(单位:像素)
@property (nonatomic, readonly) uint16_t fontWidth;
/// 字体高度(单位:像素)
@property (nonatomic, readonly) uint16_t fontHeight;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// *******4 亮屏时间
typedef SWIFT_ENUM(uint8_t, YCDeviceScreenTimeInterval, open) {
  YCDeviceScreenTimeIntervalFiveSeconds = 0,
  YCDeviceScreenTimeIntervalTenSeconds = 1,
  YCDeviceScreenTimeIntervalFifteenSeconds = 2,
  YCDeviceScreenTimeIntervalThirtySeconds = 3,
  YCDeviceScreenTimeIntervalTwentySeconds = 4,
  YCDeviceScreenTimeIntervalTwentyFiveSeconds = 5,
};

/// 设备屏幕类型
typedef SWIFT_ENUM(NSInteger, YCDeviceScreenType, open) {
  YCDeviceScreenTypeRound = 0,
  YCDeviceScreenTypeSquare = 1,
};

/// 数据存储
typedef SWIFT_ENUM(uint8_t, YCDeviceSenserSaveDataType, open) {
  YCDeviceSenserSaveDataTypePpg = 0,
  YCDeviceSenserSaveDataTypeAcceleration = 1,
  YCDeviceSenserSaveDataTypeEcg = 2,
  YCDeviceSenserSaveDataTypeTemperatureHumidity = 3,
  YCDeviceSenserSaveDataTypeAmbientLight = 4,
  YCDeviceSenserSaveDataTypeBodyTemperature = 5,
};


/// 查询传感器采样信息
SWIFT_CLASS("_TtC12YCProductSDK24YCDeviceSensorSampleInfo")
@interface YCDeviceSensorSampleInfo : NSObject
/// 是否开启
@property (nonatomic, readonly) BOOL isOn;
/// 单次采集时长 单位:s
@property (nonatomic, readonly) uint16_t acquisitionTime;
/// 采集间隔  单位:min
@property (nonatomic, readonly) uint16_t acquisitionInterval;
/// 显示内容
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 3.5.2.22 肤色设置
typedef SWIFT_ENUM(uint8_t, YCDeviceSkinColorLevel, open) {
  YCDeviceSkinColorLevelWhite = 0,
  YCDeviceSkinColorLevelWhiteYellow = 1,
  YCDeviceSkinColorLevelYellow = 2,
  YCDeviceSkinColorLevelBrown = 3,
  YCDeviceSkinColorLevelDarkBrown = 4,
  YCDeviceSkinColorLevelBlack = 5,
  YCDeviceSkinColorLevelOther = 7,
};

/// 运动开关
typedef SWIFT_ENUM(uint8_t, YCDeviceSportState, open) {
  YCDeviceSportStateStop = 0,
  YCDeviceSportStateStart = 1,
  YCDeviceSportStatePause = 2,
  YCDeviceSportStateContinue = 3,
};

/// 运动类型定义
typedef SWIFT_ENUM(uint8_t, YCDeviceSportType, open) {
  YCDeviceSportTypeNone = 0,
  YCDeviceSportTypeRun = 0x01,
  YCDeviceSportTypeSwimming = 0x02,
  YCDeviceSportTypeRiding = 0x03,
  YCDeviceSportTypeFitness = 0x04,
  YCDeviceSportTypeRopeskipping = 0x06,
  YCDeviceSportTypePlayball = 0x07,
  YCDeviceSportTypeWalk = 0x08,
  YCDeviceSportTypeBadminton = 0x09,
  YCDeviceSportTypeFootball = 0x0A,
  YCDeviceSportTypeMountaineering = 0x0B,
  YCDeviceSportTypePingPang = 0x0C,
  YCDeviceSportTypeFreeMode = 0x0D,
  YCDeviceSportTypeIndoorRunning = 0x0E,
  YCDeviceSportTypeOutdoorRunning = 0x0F,
  YCDeviceSportTypeOutdoorWalking = 0x10,
  YCDeviceSportTypeIndoorWalking = 0x11,
  YCDeviceSportTypeRunMode = 0x12,
  YCDeviceSportTypeIndoorRiding = 0x13,
  YCDeviceSportTypeStepper = 0x14,
  YCDeviceSportTypeRowingMachine = 0x15,
  YCDeviceSportTypeRealTimeMonitoring = 0x16,
  YCDeviceSportTypeSitups = 0x17,
  YCDeviceSportTypeJumping = 0x18,
  YCDeviceSportTypeWeightTraining = 0x19,
  YCDeviceSportTypeYoga = 0x1A,
  YCDeviceSportTypeOnfoot = 0x1B,
  YCDeviceSportTypeVolleyball = 0x1C,
  YCDeviceSportTypeKayak = 0x1D,
  YCDeviceSportTypeRollerSkating = 0x1E,
  YCDeviceSportTypeTennis = 0x1F,
  YCDeviceSportTypeGolf = 0x20,
  YCDeviceSportTypeEllipticalMachine = 0x21,
  YCDeviceSportTypeDance = 0x22,
  YCDeviceSportTypeRockClimbing = 0x23,
  YCDeviceSportTypeAerobics = 0x24,
  YCDeviceSportTypeOtherSports = 0x25,
};

/// 设备模式
typedef SWIFT_ENUM(uint8_t, YCDeviceSystemOperator, open) {
  YCDeviceSystemOperatorShutDown = 1,
  YCDeviceSystemOperatorTransportation = 2,
  YCDeviceSystemOperatorResetRestart = 3,
};


/// 查询温湿度
SWIFT_CLASS("_TtC12YCProductSDK27YCDeviceTemperatureHumidity")
@interface YCDeviceTemperatureHumidity : NSObject
/// 是否正在测量
@property (nonatomic, readonly) BOOL isMeasuring;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 湿度
@property (nonatomic, readonly) double humidity;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 温度单位
typedef SWIFT_ENUM(uint8_t, YCDeviceTemperatureType, open) {
  YCDeviceTemperatureTypeCelsius = 0,
  YCDeviceTemperatureTypeFahrenheit = 1,
};


/// 主题信息
SWIFT_CLASS("_TtC12YCProductSDK13YCDeviceTheme")
@interface YCDeviceTheme : NSObject
/// 总数
@property (nonatomic, readonly) NSInteger themeCount;
/// 当前主题索引
@property (nonatomic, readonly) NSInteger themeIndex;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 时间格式
typedef SWIFT_ENUM(uint8_t, YCDeviceTimeType, open) {
  YCDeviceTimeTypeHour24 = 0,
  YCDeviceTimeTypeHour12 = 1,
};

/// 设备类型
typedef SWIFT_ENUM(uint8_t, YCDeviceType, open) {
  YCDeviceTypeWatch = 0,
  YCDeviceTypeRing = 1,
  YCDeviceTypeTouchRing = 2,
  YCDeviceTypeBodyTemperatureSticker = 3,
  YCDeviceTypeEcgStickers = 4,
};

/// 尿酸单位
typedef SWIFT_ENUM(uint8_t, YCDeviceUricAcidType, open) {
  YCDeviceUricAcidTypeMicromolePerLiter = 0,
  YCDeviceUricAcidTypeMilligramsPerDeciliter = 1,
};


/// 版本信息
SWIFT_CLASS("_TtC12YCProductSDK19YCDeviceVersionInfo")
@interface YCDeviceVersionInfo : NSObject
/// 版本
@property (nonatomic, readonly, copy) NSString * _Nonnull version;
/// 主版本
@property (nonatomic, readonly) uint8_t majorVersion;
/// 子版本
@property (nonatomic, readonly) uint8_t subVersion;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 3.5.2.9 左右手佩戴设置
typedef SWIFT_ENUM(uint8_t, YCDeviceWearingPositionType, open) {
  YCDeviceWearingPositionTypeLeft = 0,
  YCDeviceWearingPositionTypeRight = 1,
};

/// 3.5.2.6 时间重复格式
typedef SWIFT_ENUM(uint8_t, YCDeviceWeekRepeat, open) {
  YCDeviceWeekRepeatMonday = 1,
  YCDeviceWeekRepeatTuesday = 2,
  YCDeviceWeekRepeatWednesday = 4,
  YCDeviceWeekRepeatThursday = 8,
  YCDeviceWeekRepeatFriday = 16,
  YCDeviceWeekRepeatSaturday = 32,
  YCDeviceWeekRepeatSunday = 64,
  YCDeviceWeekRepeatEnable = 128,
};

/// 体重单位
typedef SWIFT_ENUM(uint8_t, YCDeviceWeightType, open) {
  YCDeviceWeightTypeKg = 0,
  YCDeviceWeightTypeLb = 1,
};

/// 工作模式
typedef SWIFT_ENUM(uint8_t, YCDeviceWorkModeType, open) {
  YCDeviceWorkModeTypeNormal = 0,
  YCDeviceWorkModeTypeCare = 1,
  YCDeviceWorkModeTypePowerSaving = 2,
  YCDeviceWorkModeTypeCustom = 3,
};


/// 下载数据进度信息
SWIFT_CLASS("_TtC12YCProductSDK22YCDownloadProgressInfo")
@interface YCDownloadProgressInfo : NSObject
/// 进度
@property (nonatomic, readonly) float progress;
/// 已下载数据大小
@property (nonatomic, readonly) NSInteger downloaded;
/// 下载总数据的大小
@property (nonatomic, readonly) NSInteger total;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// HRV指数等级
typedef SWIFT_ENUM(NSUInteger, YCECGBodyIndexLevel, open) {
  YCECGBodyIndexLevelAbnormal = 0,
  YCECGBodyIndexLevelMild = 1,
  YCECGBodyIndexLevelNormal = 2,
};

enum YCECGHeavyLoadLevel : NSUInteger;
enum YCECGPressureLevel : NSUInteger;
enum YCECGHrvNormLevel : NSUInteger;
enum YCECGSympatheticNervousActivityLevel : NSUInteger;

/// 身体结果参数
SWIFT_CLASS("_TtC12YCProductSDK20YCECGBodyIndexResult")
@interface YCECGBodyIndexResult : NSObject
/// 是否可用
@property (nonatomic, readonly) BOOL isAvailable;
/// 负荷指数
@property (nonatomic) float heavyLoad;
/// 负荷指数等级
@property (nonatomic, readonly) enum YCECGHeavyLoadLevel headvyLoadLevel;
/// 压力指数
@property (nonatomic) float pressure;
/// 压力等级
@property (nonatomic, readonly) enum YCECGPressureLevel pressureLevel;
/// HRV指数
@property (nonatomic) float hrvNorm;
/// HRV指数等级
@property (nonatomic, readonly) enum YCECGHrvNormLevel hrvNormLevel;
/// 身体指数
@property (nonatomic) float body;
/// 身体指数等级
@property (nonatomic, readonly) enum YCECGBodyIndexLevel bodyLevel;
/// 交感神经指数
@property (nonatomic) float sympatheticActivityIndex;
@property (nonatomic, readonly) enum YCECGSympatheticNervousActivityLevel sympatheticActivityIndexLevel;
/// 呼吸率
@property (nonatomic) NSUInteger respiratoryRate;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 负荷等级
typedef SWIFT_ENUM(NSUInteger, YCECGHeavyLoadLevel, open) {
  YCECGHeavyLoadLevelWeak = 0,
  YCECGHeavyLoadLevelSuitable = 1,
  YCECGHeavyLoadLevelStrong = 2,
};

/// HRV指数等级
typedef SWIFT_ENUM(NSUInteger, YCECGHrvNormLevel, open) {
  YCECGHrvNormLevelAbnormal = 0,
  YCECGHrvNormLevelMild = 1,
  YCECGHrvNormLevelNormal = 2,
};


/// ECG处理数据与结果
SWIFT_CLASS("_TtC12YCProductSDK12YCECGManager")
@interface YCECGManager : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class YCECGMeasurementResult;

@interface YCECGManager (SWIFT_EXTENSION(YCProductSDK))
/// 获取ECG结果
/// \param peripheral 已连接设备
///
/// \param deviceHeartRate 设备使用的心率, 没有使用 0或负数
///
/// \param deviceHRV 设备的HRV，没有使用0或负数
///
/// \param completion 测试结果
///
- (void)getECGMeasurementResult:(CBPeripheral * _Nullable)peripheral deviceHeartRate:(NSInteger)deviceHeartRate deviceHRV:(NSInteger)deviceHRV completion:(void (^ _Nonnull)(YCECGMeasurementResult * _Nonnull))completion;
/// 获取情绪指数
- (YCECGBodyIndexResult * _Nonnull)getPhysicalIndexParameters SWIFT_WARN_UNUSED_RESULT;
@end

@class NSMutableArray;

@interface YCECGManager (SWIFT_EXTENSION(YCProductSDK))
/// 算法初始化
- (void)setupManagerInfoWithRr:(void (^ _Nullable)(float, NSInteger))rr hrv:(void (^ _Nullable)(NSInteger))hrv;
/// 处理每个ECG数据
- (float)processECGData:(NSInteger)data SWIFT_WARN_UNUSED_RESULT;
/// 获取画ECG的图形 (普通开发调用)
/// \param ecgatas 原始数据
///
/// \param gridSize 每个格子的大小
///
/// \param count 上下每一侧的最大格子数
///
///
/// returns:
/// 图形数据
+ (NSMutableArray * _Nonnull)getDrawECGLineData:(NSArray<NSNumber *> * _Nonnull)ecgatas gridSize:(float)gridSize count:(NSInteger)count SWIFT_WARN_UNUSED_RESULT;
/// 转换康源数据
/// \param ecgDatas <#ecgDatas description#>
///
///
/// returns:
/// <#description#>
+ (NSMutableArray * _Nonnull)converKanYanECGData:(NSArray<NSNumber *> * _Nonnull)ecgDatas SWIFT_WARN_UNUSED_RESULT;
@end

enum YCECGResultType : NSUInteger;

/// ECG测量结果
SWIFT_CLASS("_TtC12YCProductSDK22YCECGMeasurementResult")
@interface YCECGMeasurementResult : NSObject
/// 心率
@property (nonatomic, readonly) NSInteger hearRate;
/// hrv值
@property (nonatomic, readonly) NSInteger hrv;
/// qrsType(兼容旧代码使用)
@property (nonatomic, readonly) NSInteger qrsType;
/// qrsType(兼容旧代码使用)
@property (nonatomic, readonly) BOOL afflag;
/// ECG结果
@property (nonatomic, readonly) enum YCECGResultType ecgMeasurementType;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 压力指数等级
typedef SWIFT_ENUM(NSUInteger, YCECGPressureLevel, open) {
  YCECGPressureLevelRelax = 0,
  YCECGPressureLevelMild = 1,
  YCECGPressureLevelModerate = 2,
  YCECGPressureLevelSevere = 3,
};

/// ECG结果
typedef SWIFT_ENUM(NSUInteger, YCECGResultType, open) {
/// 测量失败
  YCECGResultTypeFailed = 0,
/// 房颤
  YCECGResultTypeAtrialFibrillation = 1,
/// 房性早博
  YCECGResultTypeEarlyHeartbeat = 2,
/// 室性早博
  YCECGResultTypeSupraventricularHeartbeat = 3,
/// 心率过缓
  YCECGResultTypeAtrialBradycardia = 4,
/// 心率过快
  YCECGResultTypeAtrialTachycardia = 5,
/// 窦性心率不齐
  YCECGResultTypeAtrialArrhythmi = 6,
/// 正常心电图
  YCECGResultTypeNormal = 7,
};

/// 交感神经指数
typedef SWIFT_ENUM(NSUInteger, YCECGSympatheticNervousActivityLevel, open) {
  YCECGSympatheticNervousActivityLevelMild = 0,
  YCECGSympatheticNervousActivityLevelModerate = 1,
  YCECGSympatheticNervousActivityLevelSevere = 2,
};

/// 嵌入式外设固件类型
typedef SWIFT_ENUM(uint8_t, YCEmbeddedPeripheralFirmwareType, open) {
  YCEmbeddedPeripheralFirmwareTypeNone = 0,
  YCEmbeddedPeripheralFirmwareTypeBloodpressure = 1,
  YCEmbeddedPeripheralFirmwareTypeTouchPanel = 2,
};

enum YCHealthDataMeasureMode : uint8_t;

/// 环境光
SWIFT_CLASS("_TtC12YCProductSDK24YCHealthDataAmbientLight")
@interface YCHealthDataAmbientLight : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 模式
@property (nonatomic, readonly) enum YCHealthDataMeasureMode mode;
/// 环境光
@property (nonatomic, readonly) double ambientLight;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 血氧健康数据
SWIFT_CLASS("_TtC12YCProductSDK23YCHealthDataBloodOxygen")
@interface YCHealthDataBloodOxygen : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 模式
@property (nonatomic, readonly) enum YCHealthDataMeasureMode mode;
/// 血氧值
@property (nonatomic, readonly) NSInteger bloodOxygen;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

enum YCHealthDataBloodPressureMode : uint8_t;

/// 血压健康数据
SWIFT_CLASS("_TtC12YCProductSDK25YCHealthDataBloodPressure")
@interface YCHealthDataBloodPressure : NSObject
/// 开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 测量方式
@property (nonatomic, readonly) enum YCHealthDataBloodPressureMode mode;
/// 收缩压
@property (nonatomic, readonly) NSInteger systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) NSInteger diastolicBloodPressure;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 测量模式
typedef SWIFT_ENUM(uint8_t, YCHealthDataBloodPressureMode, open) {
  YCHealthDataBloodPressureModeSingle = 0,
  YCHealthDataBloodPressureModeMonitor = 1,
  YCHealthDataBloodPressureModeInflated = 2,
};


/// 体温
SWIFT_CLASS("_TtC12YCProductSDK27YCHealthDataBodyTemperature")
@interface YCHealthDataBodyTemperature : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 模式
@property (nonatomic, readonly) enum YCHealthDataMeasureMode mode;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 组合数据
SWIFT_CLASS("_TtC12YCProductSDK24YCHealthDataCombinedData")
@interface YCHealthDataCombinedData : NSObject
/// 开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 步数 (步)
@property (nonatomic, readonly) NSInteger step;
/// 心率值
@property (nonatomic, readonly) NSInteger heartRate;
/// 收缩压
@property (nonatomic, readonly) NSInteger systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) NSInteger diastolicBloodPressure;
/// 血氧值
@property (nonatomic, readonly) NSInteger bloodOxygen;
/// 呼吸率值
@property (nonatomic, readonly) NSInteger respirationRate;
/// HRV
@property (nonatomic, readonly) NSInteger hrv;
/// CVRR
@property (nonatomic, readonly) NSInteger cvrr;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 温度是否有效
@property (nonatomic, readonly) BOOL temperatureValid;
/// 体脂
@property (nonatomic, readonly) double fat;
/// 血糖
@property (nonatomic, readonly) double bloodGlucose;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 心率健康数据
SWIFT_CLASS("_TtC12YCProductSDK21YCHealthDataHeartRate")
@interface YCHealthDataHeartRate : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 模式
@property (nonatomic, readonly) enum YCHealthDataMeasureMode mode;
/// 心率值
@property (nonatomic, readonly) NSInteger heartRate;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

enum YCHealthDataInvasiveComprehensiveDataMode : uint8_t;

/// 有创测量组合数据
SWIFT_CLASS("_TtC12YCProductSDK37YCHealthDataInvasiveComprehensiveData")
@interface YCHealthDataInvasiveComprehensiveData : NSObject
/// 开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 血糖模式
@property (nonatomic, readonly) enum YCHealthDataInvasiveComprehensiveDataMode bloodGlucoseMode;
/// 血糖
@property (nonatomic, readonly) double bloodGlucose;
/// 尿酸模式
@property (nonatomic, readonly) enum YCHealthDataInvasiveComprehensiveDataMode uricAcidMode;
/// 尿酸
@property (nonatomic, readonly) uint16_t uricAcid;
/// 血酮模式
@property (nonatomic, readonly) enum YCHealthDataInvasiveComprehensiveDataMode bloodKetoneMode;
/// 血酮
@property (nonatomic, readonly) double bloodKetone;
/// 血酯模式
@property (nonatomic, readonly) enum YCHealthDataInvasiveComprehensiveDataMode bloodFatMode;
/// 总胆固醇
@property (nonatomic, readonly) double totalCholesterol;
/// 高密度脂蛋白胆固醇
@property (nonatomic, readonly) double hdlCholesterol;
/// 低密度脂蛋白胆固醇
@property (nonatomic, readonly) double ldlCholesterol;
/// 甘油三酯
@property (nonatomic, readonly) double triglycerides;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 有创数据测量模式(保留参数)
typedef SWIFT_ENUM(uint8_t, YCHealthDataInvasiveComprehensiveDataMode, open) {
  YCHealthDataInvasiveComprehensiveDataModeSingle = 0,
  YCHealthDataInvasiveComprehensiveDataModeMonitor = 1,
  YCHealthDataInvasiveComprehensiveDataModeInflated = 2,
};

/// 健康数据测量模式
typedef SWIFT_ENUM(uint8_t, YCHealthDataMeasureMode, open) {
  YCHealthDataMeasureModeSingle = 0,
  YCHealthDataMeasureModeMonitor = 1,
};


/// 健康监测数据
SWIFT_CLASS("_TtC12YCProductSDK19YCHealthDataMonitor")
@interface YCHealthDataMonitor : NSObject
/// 开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 步数 (步)
@property (nonatomic, readonly) NSInteger step;
/// 心率值
@property (nonatomic, readonly) NSInteger heartRate;
/// 收缩压
@property (nonatomic, readonly) NSInteger systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) NSInteger diastolicBloodPressure;
/// 血氧值
@property (nonatomic, readonly) NSInteger bloodOxygen;
/// 呼吸率值
@property (nonatomic, readonly) NSInteger respirationRate;
/// HRV
@property (nonatomic, readonly) NSInteger hrv;
/// CVRR
@property (nonatomic, readonly) NSInteger cvrr;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 温度是否有效
@property (nonatomic, readonly) BOOL temperatureValid;
/// 湿度
@property (nonatomic, readonly) double humidity;
/// 环境光
@property (nonatomic, readonly) double ambientLight;
/// 运动模式
@property (nonatomic, readonly) enum YCDeviceSportType sport;
/// 距离 (米)
@property (nonatomic, readonly) NSInteger distance;
/// 卡路里(千卡)
@property (nonatomic, readonly) NSInteger calories;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class YCHealthDataSleepDetail;

/// 睡眠数据
SWIFT_CLASS("_TtC12YCProductSDK17YCHealthDataSleep")
@interface YCHealthDataSleep : NSObject
/// 开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 结束时间戳
@property (nonatomic, readonly) NSInteger endTimeStamp;
/// 深睡次数 旧协议 (新格式为0xFFFF, 用于区分新旧协议)
@property (nonatomic, readonly) NSInteger deepSleepCount;
/// 深睡总时长 (单位:分) 旧协议
@property (nonatomic, readonly) NSInteger deepSleepMinutes;
/// 快速眼动时长 (单位:分) 旧协议
@property (nonatomic, readonly) NSInteger remSleepMinutes;
/// 浅睡次数 旧协议
@property (nonatomic, readonly) NSInteger lightSleepCount;
/// 浅睡总时长 (单位:分) 旧协议
@property (nonatomic, readonly) NSInteger lightSleepMinutes;
/// 深睡总时长 (单位:秒) deepSleepCount == 0xFFFF 有效
@property (nonatomic, readonly) NSInteger deepSleepSeconds;
/// 快速眼动时长 (单位:秒) deepSleepCount == 0xFFFF 有效
@property (nonatomic, readonly) NSInteger remSleepSeconds;
/// 浅睡总时长 (单位:秒) deepSleepCount == 0xFFFF 有效
@property (nonatomic, readonly) NSInteger lightSleepSeconds;
/// 睡眠详细数据
@property (nonatomic, readonly, copy) NSArray<YCHealthDataSleepDetail *> * _Nonnull sleepDetailDatas;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

enum YCHealthDataSleepType : NSInteger;

/// 睡眠详细数据
SWIFT_CLASS("_TtC12YCProductSDK23YCHealthDataSleepDetail")
@interface YCHealthDataSleepDetail : NSObject
/// 睡眠状态标记
@property (nonatomic, readonly) enum YCHealthDataSleepType sleepType;
/// 睡眠开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 睡眠时长 单位: 秒
@property (nonatomic, readonly) NSInteger duration;
/// 显示字符串
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 睡眠状态标记
typedef SWIFT_ENUM(NSInteger, YCHealthDataSleepType, open) {
  YCHealthDataSleepTypeUnknow = 0,
  YCHealthDataSleepTypeDeepSleep = 0xF1,
  YCHealthDataSleepTypeLightSleep = 0xF2,
  YCHealthDataSleepTypeRem = 0xF3,
  YCHealthDataSleepTypeAwake = 0xF4,
};

enum YCHealthDataSportModeStartMethod : uint8_t;

/// 运动历史数据
SWIFT_CLASS("_TtC12YCProductSDK28YCHealthDataSportModeHistory")
@interface YCHealthDataSportModeHistory : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 结束时间戳(秒)
@property (nonatomic, readonly) NSInteger endTimeStamp;
/// 步数 (步)
@property (nonatomic, readonly) NSInteger step;
/// 距离 (米)
@property (nonatomic, readonly) NSInteger distance;
/// 卡路里(千卡)
@property (nonatomic, readonly) NSInteger calories;
/// 运动方式
@property (nonatomic, readonly) enum YCDeviceSportType sport;
/// 启动方式
@property (nonatomic, readonly) enum YCHealthDataSportModeStartMethod flag;
/// 心率
@property (nonatomic, readonly) NSInteger heartRate;
/// 运动时间(有效运动时间)
@property (nonatomic, readonly) NSInteger sportTime;
/// 最小心率
@property (nonatomic, readonly) NSInteger minimumHeartRate;
/// 最大心率
@property (nonatomic, readonly) NSInteger maximumHeartRate;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 运动启动方式
typedef SWIFT_ENUM(uint8_t, YCHealthDataSportModeStartMethod, open) {
  YCHealthDataSportModeStartMethodApp = 0,
  YCHealthDataSportModeStartMethodDevice = 1,
};


/// 步数信息
SWIFT_CLASS("_TtC12YCProductSDK16YCHealthDataStep")
@interface YCHealthDataStep : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 结束时间戳(秒)
@property (nonatomic, readonly) NSInteger endTimeStamp;
/// 步数 (步)
@property (nonatomic, readonly) NSInteger step;
/// 距离 (米)
@property (nonatomic, readonly) NSInteger distance;
/// 卡路里(千卡)
@property (nonatomic, readonly) NSInteger calories;
/// 打印内容
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 温湿度
SWIFT_CLASS("_TtC12YCProductSDK31YCHealthDataTemperatureHumidity")
@interface YCHealthDataTemperatureHumidity : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 模式
@property (nonatomic, readonly) enum YCHealthDataMeasureMode mode;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 湿度
@property (nonatomic, readonly) double humidity;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 佩戴脱落状态
typedef SWIFT_ENUM(uint8_t, YCHealthDataWearState, open) {
  YCHealthDataWearStateWear = 0,
  YCHealthDataWearStateFallOff = 1,
};


/// 佩戴脱落数据
SWIFT_CLASS("_TtC12YCProductSDK28YCHealthDataWearStateHistory")
@interface YCHealthDataWearStateHistory : NSObject
/// 开始时间戳(秒)
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 状态
@property (nonatomic, readonly) enum YCHealthDataWearState state;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 预警信息
typedef SWIFT_ENUM(uint8_t, YCHealthParametersState, open) {
  YCHealthParametersStateOff = 0,
  YCHealthParametersStateEffect = 1,
  YCHealthParametersStateInvalid = 0xFF,
};

/// 健康状态
typedef SWIFT_ENUM(uint8_t, YCHealthState, open) {
  YCHealthStateUnknow = 0,
  YCHealthStateExcellent = 1,
  YCHealthStateGood = 2,
  YCHealthStateGeneral = 3,
  YCHealthStatePoor = 4,
  YCHealthStateSick = 5,
  YCHealthStateInvalid = 0xFF,
};

/// 智趣功能类型
typedef SWIFT_ENUM(uint8_t, YCIntelligentFunctionType, open) {
  YCIntelligentFunctionTypeShortVideo = 1,
  YCIntelligentFunctionTypeMusic = 2,
  YCIntelligentFunctionTypeRead = 3,
  YCIntelligentFunctionTypeTakePhotoOrVideo = 4,
  YCIntelligentFunctionTypeSos = 5,
  YCIntelligentFunctionTypeSlides = 6,
};

/// 定位信息类型
typedef SWIFT_ENUM(uint8_t, YCLocationInformationType, open) {
  YCLocationInformationTypeLongitudeAndLatitude = 0,
  YCLocationInformationTypeDetailedAddress = 1,
};

/// 健康数据回写类型
typedef SWIFT_ENUM(uint8_t, YCMeasurementDataType, open) {
  YCMeasurementDataTypeHeartRate = 0,
  YCMeasurementDataTypeBloodPressure = 1,
  YCMeasurementDataTypeBloodOxygen = 2,
  YCMeasurementDataTypeRespirationRate = 3,
  YCMeasurementDataTypeHrv = 4,
  YCMeasurementDataTypeBloodGlucose = 5,
  YCMeasurementDataTypeTemperature = 6,
};

/// 马达震动强度等级
typedef SWIFT_ENUM(uint8_t, YCMotorVibrationStrengthLevel, open) {
  YCMotorVibrationStrengthLevelNone = 0,
  YCMotorVibrationStrengthLevelLevel1 = 1,
  YCMotorVibrationStrengthLevelLevel2 = 2,
  YCMotorVibrationStrengthLevelLevel3 = 3,
};

/// 多通道PPG样本组合类型
typedef SWIFT_ENUM(uint8_t, YCMultiChannelPPGCompositeType, open) {
  YCMultiChannelPPGCompositeTypeGreenIrRed = 0,
  YCMultiChannelPPGCompositeTypeGreenIr = 1,
  YCMultiChannelPPGCompositeTypeIrRed = 2,
};

/// 定时提醒类型
typedef SWIFT_ENUM(uint8_t, YCPeriodicReminderType, open) {
  YCPeriodicReminderTypeDrinkWater = 0,
  YCPeriodicReminderTypeTakeMedicine = 1,
};

/// 个人信息类型
typedef SWIFT_ENUM(uint8_t, YCPersonalInfoType, open) {
  YCPersonalInfoTypeInsurance = 0,
  YCPersonalInfoTypeVip = 1,
};

/// ******** 手机系统类型
typedef SWIFT_ENUM(uint8_t, YCPhoneSystemType, open) {
  YCPhoneSystemTypeAndroid = 0,
  YCPhoneSystemTypeIos = 1,
};

enum YCProductState : NSInteger;

/// SDK的公共类
SWIFT_CLASS("_TtC12YCProductSDK9YCProduct")
@interface YCProduct : NSObject
/// 单例
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) YCProduct * _Nonnull shared;)
+ (YCProduct * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
/// 启动回连标记 (固件升级可能会用上)
@property (nonatomic) BOOL isReconnectEnable;
/// 正在升级
@property (nonatomic) BOOL isUpgrading;
/// 生产连接使用(普通状态无须使用)
SWIFT_CLASS_PROPERTY(@property (nonatomic, class) BOOL isConnectForProduct;)
+ (BOOL)isConnectForProduct SWIFT_WARN_UNUSED_RESULT;
+ (void)setIsConnectForProduct:(BOOL)value;
/// 过滤搜索设备的产商ID
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, copy) NSArray<NSNumber *> * _Nonnull filterProductID;)
+ (NSArray<NSNumber *> * _Nonnull)filterProductID SWIFT_WARN_UNUSED_RESULT;
+ (void)setFilterProductID:(NSArray<NSNumber *> * _Nonnull)value;
/// 外设管理器状态
@property (nonatomic) enum YCProductState centralManagerState;
/// 当前的连接的外设
@property (nonatomic, readonly, strong) CBPeripheral * _Nullable currentPeripheral;
/// 当前连接的所有的外设 (保留参数，扩展使用)
@property (nonatomic, readonly, copy) NSArray<CBPeripheral *> * _Nonnull connectedPeripherals;
/// 初始化配置
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// 回连
- (void)reconnectedDevice;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 设置智趣功能
/// \param peripheral 连接设备
///
/// \param isEnable 使能
///
/// \param functionType 智趣功能
///
/// \param completion <#completion description#>
///
+ (void)setIntelligentFunctions:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable functionType:(enum YCIntelligentFunctionType)functionType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


enum YCProductLogLevel : NSInteger;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 设置SDK的日志等级开关(默认都是关闭)
/// \param printLevel 控制台打印日志等级
///
/// \param saveLevel 保存日志等级
///
+ (void)setLogLevel:(enum YCProductLogLevel)printLevel saveLevel:(enum YCProductLogLevel)saveLevel;
@end



@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 检测戒指设备的硬件状态
+ (void)checkRingDeviceHardwareState:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end

@class UIImage;
@class YCWatchFaceDataBmpInfo;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 生成自定义表盘数据
/// \param dialData 原始表盘数据
///
/// \param backgroundImage 背景图片
///
/// \param thumbnail 缩略图片
///
/// \param timePosition 时间显示位置坐标
///
/// \param redColor 0 ~ 255
///
/// \param greenColor 0 ~ 255
///
/// \param blueColor 0 ~ 255
///
/// \param isFlipColor 是否要翻转颜色
///
///
/// returns:
/// 表盘文件
+ (NSData * _Nonnull)generateCustomDialData:(NSData * _Nonnull)dialData backgroundImage:(UIImage * _Nullable)backgroundImage thumbnail:(UIImage * _Nullable)thumbnail timePosition:(CGPoint)timePosition redColor:(uint8_t)redColor greenColor:(uint8_t)greenColor blueColor:(uint8_t)blueColor isFlipColor:(BOOL)isFlipColor SWIFT_WARN_UNUSED_RESULT;
+ (YCWatchFaceDataBmpInfo * _Nonnull)queryDeviceBmpInfo:(NSData * _Nonnull)dialData SWIFT_WARN_UNUSED_RESULT;
@end

enum YCQueryHealthDataType : uint8_t;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 查询健康数据
/// \param peripheral 当前设备
///
/// \param dataType 数据类型
///
/// \param completion 调用结果
///
+ (void)queryHealthData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCQueryHealthDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 删除健康数据
/// \param peripheral 当前连接的设备
///
/// \param dataType 删除的数据类型
///
/// \param completion 删除是否成功
///
+ (void)deleteHealthData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCDeleteHealthDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end



@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 预置表盘下载任务
/// \param peripheral 连接的设备
///
/// \param isEnable 开启或关闭
///
/// \param completion 结果
///
+ (void)presetWathcFaceTask:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 开机Logo下载任务
/// \param peripheral 连接的设备
///
/// \param isEnable 开启或关闭
///
/// \param completion 结果
///
+ (void)bootLogoTask:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 开始ECG测量
/// \param peripheral 连接设备
///
/// \param completion 是否开启成功
///
+ (void)startECGMeasurement:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 关闭ECG测量
/// \param peripheral 连接设备
///
/// \param completion 是否关闭成功
///
+ (void)stopECGMeasurement:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 查询录音文件列表的数量
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceRecordingListFileCount:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询指定区域的文件信息
/// \param peripheral 连接设备
///
/// \param index 起始位置(从1开始)
///
/// \param count 获取文件的数量
///
/// \param completion 结果
///
+ (void)queryDeviceRecordingListFilesInfo:(CBPeripheral * _Nullable)peripheral index:(uint16_t)index count:(uint16_t)count completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 同步录音数据
/// \param peripheral 连接外设
///
/// \param fileName 文件名称
///
/// \param offset 断点位置
///
/// \param completion 结果
///
+ (void)synchronizeRecordingData:(CBPeripheral * _Nullable)peripheral fileName:(NSString * _Nonnull)fileName offset:(NSInteger)offset completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 查询CGM的信息
/// \param peripheral 连接设备
///
/// \param infoType 信息类型
///
/// \param completion 结果
///
+ (void)queryCustomCGMDeviceInfo:(CBPeripheral * _Nullable)peripheral infoType:(enum YCCustomCGMDeviceInfoType)infoType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询设备的数据
/// \param peripheral 连接设备
///
/// \param dataType 数据类型
///
/// \param completion 结果
///
+ (void)queryCustomDeviceData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCustomDeviceDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 删除设备的健康数据
/// \param peripheral 连接设备
///
/// \param dataType 数据类型
///
/// \param completion 结果
///
+ (void)deleteCustomDeviceData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCustomDeviceDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 清理队列
- (void)clearQueue;
@end

enum YCWeatherPeriodType : uint8_t;
enum YCWeatherCodeType : uint8_t;
enum YCWeatherMoonType : uint8_t;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 发送天气
/// \param peripheral 连接外设
///
/// \param whichDay 今天还是明天的天气
///
/// \param lowestTemperature 最低温度 摄氏度
///
/// \param highestTemperature 最高温度 摄氏度
///
/// \param realTimeTemperature 当前天气温度 摄氏度
///
/// \param weatherType 天气类型
///
/// \param windDirection 风向
///
/// \param windPower 风力
///
/// \param location 城市
///
/// \param moonType 月相
///
/// \param completion 发送结果
///
+ (void)sendWeatherData:(CBPeripheral * _Nullable)peripheral whichDay:(enum YCWeatherPeriodType)whichDay lowestTemperature:(int8_t)lowestTemperature highestTemperature:(int8_t)highestTemperature realTimeTemperature:(int8_t)realTimeTemperature weatherType:(enum YCWeatherCodeType)weatherType windDirection:(NSString * _Nonnull)windDirection windPower:(NSString * _Nonnull)windPower location:(NSString * _Nonnull)location moonType:(enum YCWeatherMoonType)moonType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 开启通讯录同步
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)startSendAddressBook:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 退出通讯录同步
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)stopSendAddressBook:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 同步通讯录详情数据
/// \param peripheral 连接设备
///
/// \param phone 电话号码
///
/// \param name 用户名，不超过8个中文
///
/// \param completion 结果
///
+ (void)sendAddressBook:(CBPeripheral * _Nullable)peripheral phone:(NSString * _Nonnull)phone name:(NSString * _Nonnull)name completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 外设固件升级
/// \param peripheral 连接的设备
///
/// \param isEnable 开启或关闭
///
/// \param firmwareType 固件类型
///
/// \param data 数据
///
/// \param completion 下载进度
///
+ (void)embeddedPeripheralFirmwareUpgrade:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable firmwareType:(enum YCEmbeddedPeripheralFirmwareType)firmwareType data:(NSData * _Nonnull)data completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@class CBCentralManager;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK)) <CBCentralManagerDelegate>
/// 连接设备
/// \param central <#central description#>
///
/// \param peripheral <#peripheral description#>
///
- (void)centralManager:(CBCentralManager * _Nonnull)central didConnectPeripheral:(CBPeripheral * _Nonnull)peripheral;
/// 断开连接
/// \param central <#central description#>
///
/// \param peripheral <#peripheral description#>
///
/// \param error <#error description#>
///
- (void)centralManager:(CBCentralManager * _Nonnull)central didDisconnectPeripheral:(CBPeripheral * _Nonnull)peripheral error:(NSError * _Nullable)error;
/// 连接失败
/// \param central <#central description#>
///
/// \param peripheral <#peripheral description#>
///
/// \param error <#error description#>
///
- (void)centralManager:(CBCentralManager * _Nonnull)central didFailToConnectPeripheral:(CBPeripheral * _Nonnull)peripheral error:(NSError * _Nullable)error;
/// 扫描到设备
/// \param central 中央处理器
///
/// \param peripheral 外设
///
/// \param advertisementData 数据
///
/// \param RSSI 信号
///
- (void)centralManager:(CBCentralManager * _Nonnull)central didDiscoverPeripheral:(CBPeripheral * _Nonnull)peripheral advertisementData:(NSDictionary<NSString *, id> * _Nonnull)advertisementData RSSI:(NSNumber * _Nonnull)RSSI;
/// 状态变化
/// \param central <#central description#>
///
- (void)centralManagerDidUpdateState:(CBCentralManager * _Nonnull)central;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 下载UI文件
/// \param peripheral 连接的设备
///
/// \param data UI数据
///
/// \param completion 下载进度
///
+ (void)downloadUIFile:(CBPeripheral * _Nullable)peripheral data:(NSData * _Nonnull)data completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end

@class NSError;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 通过UUID来获取设备
/// \param uuidString <#uuidString description#>
///
///
/// returns:
/// <#description#>
+ (CBPeripheral * _Nullable)getDeviceByUUID:(NSString * _Nonnull)uuidString SWIFT_WARN_UNUSED_RESULT;
/// 连接设备
/// \param peripheral <#peripheral description#>
///
+ (void)connectDevice:(CBPeripheral * _Nonnull)peripheral completion:(void (^ _Nullable)(enum YCProductState, NSError * _Nullable))completion;
/// 断开连接
/// \param peripheral <#peripheral description#>
///
+ (void)disconnectDevice:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, NSError * _Nullable))completion;
/// 开始扫描设备
/// \param delayTime 延时停止，默认3秒
///
/// \param completion 返回结果
///
+ (void)scanningDeviceWithDelayTime:(NSTimeInterval)delayTime completion:(void (^ _Nullable)(NSArray<CBPeripheral *> * _Nonnull, NSError * _Nullable))completion;
/// 停止扫描(SDK会自动调用)
+ (void)stopSearchDevice;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 查询本地历史采集数据的基本信息
/// \param peripheral 连接设备
///
/// \param dataType 数据类型
///
/// \param completion 信息记录
///
+ (void)queryCollectDataBasicinfo:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCollectDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)queryCollectDataCount:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCollectDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 通过索引获取数据
/// \param peripheral 连接设备
///
/// \param dataType 数据类型
///
/// \param index 索引
///
/// \param uploadEnable 是否上报数据
///
/// \param completion 接收到的数据
///
+ (void)queryCollectDataInfo:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCollectDataType)dataType index:(uint16_t)index uploadEnable:(BOOL)uploadEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 通过时间戳查询 (有些手环会出现莫名奇妙的问题，统一用索引比较好)
+ (void)queryCollectDataInfo:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCollectDataType)dataType timeStamp:(uint32_t)timeStamp uploadEnable:(BOOL)uploadEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 按索引删除
/// \param peripheral 连接设备
///
/// \param dataType 数据类型
///
/// \param index 记录索引
///
/// \param completion 删除结果
///
+ (void)deleteCollectData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCollectDataType)dataType index:(uint16_t)index completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 按时间戳删除
/// \param peripheral <#peripheral description#>
///
/// \param dataType <#dataType description#>
///
/// \param timeStamp <#timeStamp description#>
///
/// \param completion <#completion description#>
///
+ (void)deleteCollectData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCCollectDataType)dataType timeStamp:(uint32_t)timeStamp completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end

@class CBCharacteristic;
@class CBService;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK)) <CBPeripheralDelegate>
/// 接收到外设的数据
/// \param peripheral <#peripheral description#>
///
/// \param characteristic <#characteristic description#>
///
/// \param error <#error description#>
///
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didUpdateValueForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
/// \param peripheral <#peripheral description#>
///
/// \param characteristic <#characteristic description#>
///
/// \param error <#error description#>
///
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didUpdateNotificationStateForCharacteristic:(CBCharacteristic * _Nonnull)characteristic error:(NSError * _Nullable)error;
/// 发现外设特征下的服务
/// \param peripheral <#peripheral description#>
///
/// \param service <#service description#>
///
/// \param error <#error description#>
///
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didDiscoverCharacteristicsForService:(CBService * _Nonnull)service error:(NSError * _Nullable)error;
/// 发现外设服务
/// \param peripheral <#peripheral description#>
///
/// \param error <#error description#>
///
- (void)peripheral:(CBPeripheral * _Nonnull)peripheral didDiscoverServices:(NSError * _Nullable)error;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 下载表盘
/// \param peripheral 连接的设备
///
/// \param isEnable 开启或关闭
///
/// \param data 表盘数据
///
/// \param dialID 表盘id
///
/// \param blockCount 表盘断点
///
/// \param dialVersion 表盘版本
///
/// \param completion 下载进度
///
+ (void)downloadWatchFace:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable data:(NSData * _Nonnull)data dialID:(uint32_t)dialID blockCount:(uint16_t)blockCount dialVersion:(uint16_t)dialVersion completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询表盘信息
/// \param peripheral <#peripheral description#>
///
/// \param completion <#completion description#>
///
+ (void)queryWatchFaceInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 切换表盘
/// \param peripheral <#peripheral description#>
///
/// \param dialID <#dialID description#>
///
/// \param completion <#completion description#>
///
+ (void)changeWatchFace:(CBPeripheral * _Nullable)peripheral dialID:(uint32_t)dialID completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 删除表盘
/// \param peripheral <#peripheral description#>
///
/// \param dialID <#dialID description#>
///
/// \param completion <#completion description#>
///
+ (void)deleteWatchFace:(CBPeripheral * _Nullable)peripheral dialID:(uint32_t)dialID completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 状态的key
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull connecteStateKey;)
+ (NSString * _Nonnull)connecteStateKey SWIFT_WARN_UNUSED_RESULT;
/// 连接设备的key
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull connectDeviceKey;)
+ (NSString * _Nonnull)connectDeviceKey SWIFT_WARN_UNUSED_RESULT;
/// 连接状态的ObjcKey(补充OC的过渡参数)
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull connecteStateKeyObjc;)
+ (NSString * _Nonnull)connecteStateKeyObjc SWIFT_WARN_UNUSED_RESULT;
/// 心电电极状态
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull ecgElectrodesStateKey;)
+ (NSString * _Nonnull)ecgElectrodesStateKey SWIFT_WARN_UNUSED_RESULT;
/// 光电传感器状态
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull photoelectricSensorStateKey;)
+ (NSString * _Nonnull)photoelectricSensorStateKey SWIFT_WARN_UNUSED_RESULT;
/// 设备状态变化的通知
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) NSNotificationName _Nonnull deviceStateNotification;)
+ (NSNotificationName _Nonnull)deviceStateNotification SWIFT_WARN_UNUSED_RESULT;
/// 接收实时数据的通知
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) NSNotificationName _Nonnull receivedRealTimeNotification;)
+ (NSNotificationName _Nonnull)receivedRealTimeNotification SWIFT_WARN_UNUSED_RESULT;
/// 设备控制通知
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) NSNotificationName _Nonnull deviceControlNotification;)
+ (NSNotificationName _Nonnull)deviceControlNotification SWIFT_WARN_UNUSED_RESULT;
/// 取消操作
+ (void)cancel;
@end

@class NSSet;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 查询闹钟
/// \param peripheral 连接设备
///
/// \param completion 闹钟信息
///
+ (void)queryDeviceAlarmInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 添加闹钟
/// \param peripheral 连接设备
///
/// \param alarmType 闹钟类型
///
/// \param hour 小时  0 ~ 23
///
/// \param minute 分钟 0 ~ 59
///
/// \param repeat 重复时间 YCDeviceWeekRepeat
///
/// \param snoozeTime 贪睡时长 0~59分钟
///
/// \param completion 设置结果
///
+ (void)addDeviceAlarm:(CBPeripheral * _Nullable)peripheral alarmType:(enum YCDeviceAlarmType)alarmType hour:(uint8_t)hour minute:(uint8_t)minute repeat:(NSSet * _Nonnull)repeat snoozeTime:(uint8_t)snoozeTime completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 删除闹钟
/// \param peripheral 连接设备
///
/// \param hour 小时
///
/// \param minute 分钟
///
/// \param completion 结果
///
+ (void)deleteDeviceAlarm:(CBPeripheral * _Nullable)peripheral hour:(uint8_t)hour minute:(uint8_t)minute completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 修改闹钟
/// \param peripheral 连接设备
///
/// \param oldHour 闹钟原来的小时
///
/// \param oldMinute 闹钟原来的分钟
///
/// \param hour 闹钟新的小时
///
/// \param minute 闹钟新的分钟
///
/// \param alarmType 闹钟类型
///
/// \param repeat 重复时间 YCDeviceWeekRepeat
///
/// \param snoozeTime 贪睡时长
///
/// \param completion 结果
///
+ (void)modifyDeviceAlarm:(CBPeripheral * _Nullable)peripheral oldHour:(uint8_t)oldHour oldMinute:(uint8_t)oldMinute hour:(uint8_t)hour minute:(uint8_t)minute alarmType:(enum YCDeviceAlarmType)alarmType repeat:(NSSet * _Nonnull)repeat snoozeTime:(uint8_t)snoozeTime completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 事件开关
/// \param peripheral 连接
///
/// \param isEnable 是否开启
///
/// \param completion 设置结果
///
+ (void)setDeviceEventEnable:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 添加事件
/// \param peripheral 连接设备
///
/// \param name 事件名称
///
/// \param isEnable 当前事件是否使能
///
/// \param hour 事件小时 0 ~ 23
///
/// \param minute 事件分钟 0 ~ 59
///
/// \param interval 重复提醒间隔
///
/// \param repeat 重复星期 YCDeviceWeekRepeat
///
/// \param completion 结果
///
+ (void)addDeviceEvent:(CBPeripheral * _Nullable)peripheral name:(NSString * _Nonnull)name isEnable:(BOOL)isEnable hour:(uint8_t)hour minute:(uint8_t)minute interval:(enum YCDeviceEventInterval)interval repeat:(NSSet * _Nonnull)repeat completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 删除事件
/// \param peripheral 连接设备
///
/// \param eventID 事件id
///
/// \param completion 结果
///
+ (void)deleteDeviceEvent:(CBPeripheral * _Nullable)peripheral eventID:(uint8_t)eventID completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 修改事件
/// \param peripheral 连接设备
///
/// \param name 事件名称
///
/// \param eventID 事件id
///
/// \param isEnable 是否开启
///
/// \param hour 事件的小时
///
/// \param minute 事件的分钟
///
/// \param interval 时间的间隔
///
/// \param repeat 重复
///
/// \param completion 结果
///
+ (void)modifyDeviceEvent:(CBPeripheral * _Nullable)peripheral name:(NSString * _Nonnull)name eventID:(uint8_t)eventID isEnable:(BOOL)isEnable hour:(uint8_t)hour minute:(uint8_t)minute interval:(enum YCDeviceEventInterval)interval repeat:(NSSet * _Nonnull)repeat completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询事件
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceEventnfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)setDeviceScheduleEnable:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 增加新的日程
+ (void)addDeviceSchedule:(CBPeripheral * _Nullable)peripheral scheduleEnable:(BOOL)scheduleEnable scheduleIndex:(uint8_t)scheduleIndex eventIndex:(uint8_t)eventIndex eventType:(enum YCDevcieScheduleEventType)eventType eventEnable:(BOOL)eventEnable eventTime:(NSInteger)eventTime eventName:(NSString * _Nonnull)eventName completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 修改日程
+ (void)modifyDeviceSchedule:(CBPeripheral * _Nullable)peripheral scheduleEnable:(BOOL)scheduleEnable scheduleIndex:(uint8_t)scheduleIndex eventIndex:(uint8_t)eventIndex eventType:(enum YCDevcieScheduleEventType)eventType eventEnable:(BOOL)eventEnable eventTime:(NSInteger)eventTime eventName:(NSString * _Nonnull)eventName completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 删除日程
+ (void)deleteDeviceSchedule:(CBPeripheral * _Nullable)peripheral scheduleIndex:(uint8_t)scheduleIndex eventIndex:(uint8_t)eventIndex eventType:(enum YCDevcieScheduleEventType)eventType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询日程
+ (void)queryDeviceScheduleInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// ******* 获取设备基本信息
+ (void)queryDeviceBasicInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******* 获取设备 MAC 地址
+ (void)queryDeviceMacAddress:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******* 获取设备型号
+ (void)queryDeviceModel:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******* 获取设备功能开关状态
+ (void)queryDeviceFunctionEnableState:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******* 获取当前心率
/// \param peripheral 连接的设备
///
/// \param completion 结果
///
+ (void)queryDeviceCurrentHeartRate:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******* 获取当前血压
/// <ul>
///   <li>
///     Parameters:
///   </li>
///   <li>
///     peripheral: 连接的设备
///   </li>
///   <li>
///     completion: 结果
///   </li>
/// </ul>
+ (void)queryDeviceCurrentBloodPressure:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******* 获取用户配置
+ (void)queryDeviceUserConfiguration:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取设备主界面样式配置
+ (void)queryDeviceTheme:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取心电右电极位置
+ (void)queryDeviceElectrodePosition:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******2 获取显示屏分辨率和字库字体分辨率
+ (void)queryDeviceScreenInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******3 获取当前实时运动数据
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceCurrentExerciseInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******4 获取历史记录概要信息
/// \param peripheral 连接设备
///
/// \param completion 回调
///
+ (void)queryDeviceHistorySummary:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 获取实时温度
/// \param peripheral 连接设备
///
/// \param completion 测量温度
///
+ (void)queryDeviceRealTimeTemperature:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******6 获取屏幕显示信息
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceScreenDisplayInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******8 获取当前血氧
/// <ul>
///   <li>
///     Parameters:
///   </li>
///   <li>
///     peripheral: 连接设备
///   </li>
///   <li>
///     completion: 结果
///   </li>
/// </ul>
+ (void)queryDeviceBloodOxygen:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******9 获取当前环境光
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceAmbientLight:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取当前环境温湿度
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceTemperatureHumidity:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取传感器采样信息
/// \param peripheral 连接的设备
///
/// \param dataType 数据类型
///
/// \param completion 结果
///
+ (void)queryDeviceSensorSampleInfo:(CBPeripheral * _Nullable)peripheral dataType:(enum YCDeviceDataCollectionType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取当前系统工作模式
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceWorkMode:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取上传提醒配置信息
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)queryDeviceUploadReminderInfo:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// ******** 获取当前手环芯片方案
+ (void)queryDeviceMCU:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// *******1 获取手环提醒设置信息
/// \param peripheral 连接设备
///
/// \param dataType 提醒类型
///
/// \param completion 结果
///
+ (void)queryDeviceRemindSettingInfo:(CBPeripheral * _Nullable)peripheral dataType:(enum YCDeviceRemindType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询设备支付宝激活状态
/// \param peripheral <#peripheral description#>
///
/// \param completion <#completion description#>
///
+ (void)queryDeviceAliPayActivationStatus:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询设备的显示屏参数
/// \param peripheral <#peripheral description#>
///
/// \param completion <#completion description#>
///
+ (void)queryDeviceDisplayParameters:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询名片信息
/// \param peripheral 已连接设备
///
/// \param businessCardType 名片类型
///
/// \param completion 结果
///
+ (void)queryBusinessCardContent:(CBPeripheral * _Nullable)peripheral businessCardType:(enum YCBusinessCardType)businessCardType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end

enum YCRealTimeDataType : uint8_t;
enum YCQuerySampleRateType : uint8_t;
enum YCWaveUploadState : uint8_t;
enum YCWaveDataType : uint8_t;
enum YCWarningInformationType : uint8_t;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 找设备
/// \param peripheral 连接设备
///
/// \param remindCount 提醒次数
///
/// \param remindInterval 间隔秒数
///
/// \param completion 结果
///
+ (void)findDevice:(CBPeripheral * _Nullable)peripheral remindCount:(uint8_t)remindCount remindInterval:(uint8_t)remindInterval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 血压校准
/// \param peripheral 连接手环
///
/// \param systolicBloodPressure 收缩压
///
/// \param diastolicBloodPressure 舒张压
///
/// \param completion 结果
///
+ (void)deviceBloodPressureCalibration:(CBPeripheral * _Nullable)peripheral systolicBloodPressure:(uint8_t)systolicBloodPressure diastolicBloodPressure:(uint8_t)diastolicBloodPressure completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 实时数据上传
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启或关闭
///
/// \param dataType 数据类型
///
/// \param interval 间隔 1 ~ 240秒
///
/// \param completion 设置结果
///
+ (void)realTimeDataUplod:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable dataType:(enum YCRealTimeDataType)dataType interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 查询采样率
+ (void)querySampleRate:(CBPeripheral * _Nullable)peripheral dataType:(enum YCQuerySampleRateType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 波形上传控制
/// \param peripheral 连接设备
///
/// \param state 是否开关
///
/// \param dataType 波形类型
///
/// \param completion 开启结果
///
+ (void)waveDataUpload:(CBPeripheral * _Nullable)peripheral state:(enum YCWaveUploadState)state dataType:(enum YCWaveDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 控制设备进入运动模式
/// \param peripheral 连接设备
///
/// \param state 运动状态
///
/// \param sportType 运动类型
///
/// \param completion 结果
///
+ (void)controlSport:(CBPeripheral * _Nullable)peripheral state:(enum YCDeviceSportState)state sportType:(enum YCDeviceSportType)sportType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 手机控制进入与退出拍照模式
/// \param peripheral 连接设备
///
/// \param isEnable 是否启动拍照模式
///
/// \param completion 结果
///
+ (void)takephotoByPhone:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送健康参数
/// \param peripheral 连接设备
///
/// \param warningState 预警状态
///
/// \param healthState 健康状态
///
/// \param healthIndex 健康指数 0 ~ 120
///
/// \param othersWarningState 亲友预警是否生效
///
/// \param completion 结果
///
+ (void)sendHealthParameters:(CBPeripheral * _Nullable)peripheral warningState:(enum YCHealthParametersState)warningState healthState:(enum YCHealthState)healthState healthIndex:(uint8_t)healthIndex othersWarningState:(enum YCHealthParametersState)othersWarningState completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 系统操作
/// \param peripheral 连接设备
///
/// \param mode 模式
///
/// \param completion 无
///
+ (void)deviceSystemOperator:(CBPeripheral * _Nullable)peripheral mode:(enum YCDeviceSystemOperator)mode completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 温度校准
/// \param peripheral 连接设备
///
/// \param completion 校准结果
///
+ (void)deviceTemperatureCalibration:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 腋测温度
/// \param peripheral 连接设备
///
/// \param completion 结果
///
+ (void)deviceArmpitTemperatureMeasurement:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 显示亲友消息
/// \param peripheral 连接设备
///
/// \param index 表情序号
///
/// \param hour 发送小时 0 ~ 23
///
/// \param minute 发送分钟 0 ~ 59
///
/// \param name 亲友名称
///
/// \param completion 结果
///
+ (void)deviceShowFriendMessage:(CBPeripheral * _Nullable)peripheral index:(uint8_t)index hour:(uint8_t)hour minute:(uint8_t)minute name:(NSString * _Nonnull)name completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 健康值回写
/// \param peripheral 连接设备
///
/// \param healthValue 健康值
///
/// \param statusDescription 描述信息
///
/// \param completion 结果
///
+ (void)deviceHealthValueWriteBack:(CBPeripheral * _Nullable)peripheral healthValue:(uint8_t)healthValue statusDescription:(NSString * _Nonnull)statusDescription completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 睡眠数据回写
/// \param peripheral 连接设备
///
/// \param deepSleepHour 深睡时长 小时 0 ~ 23
///
/// \param deepSleepMinute 深睡时长 分钟 0 ~ 23
///
/// \param lightSleepHour 浅睡时长 小时 0 ~ 23
///
/// \param lightSleepMinute 浅睡时长 分钟 0 ~ 59
///
/// \param totalSleepHour 浅睡时长 小时 0 ~ 23
///
/// \param totalSleepMinute 总共睡眠时长 分钟 0 ~ 59
///
/// \param completion 结果
///
+ (void)deviceSleepDataWriteBack:(CBPeripheral * _Nullable)peripheral deepSleepHour:(uint8_t)deepSleepHour deepSleepMinute:(uint8_t)deepSleepMinute lightSleepHour:(uint8_t)lightSleepHour lightSleepMinute:(uint8_t)lightSleepMinute totalSleepHour:(uint8_t)totalSleepHour totalSleepMinute:(uint8_t)totalSleepMinute completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 个人数据回写
/// \param peripheral 连接设备
///
/// \param infoType 用户信息
///
/// \param information 描述信息
///
/// \param completion 结果
///
+ (void)devicePersonalInfoWriteBack:(CBPeripheral * _Nullable)peripheral infoType:(enum YCPersonalInfoType)infoType information:(NSString * _Nonnull)information completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 升级提醒
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启提醒
///
/// \param percentage 当前进度 0 ~ 100
///
/// \param completion 结果
///
+ (void)deviceUpgradeReminderWriteBack:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable percentage:(uint8_t)percentage completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 环境光测量
+ (void)deviceAmbientLightMeasurement:(CBPeripheral * _Nullable)peripheral mode:(enum YCAppControlMeasureMode)mode completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 修改体温二维码颜色
/// \param peripheral 连接设备
///
/// \param color 颜色
///
/// \param completion 设置结果
///
+ (void)changeDeviceBodyTemperatureQRCodeColor:(CBPeripheral * _Nullable)peripheral color:(enum YCBodyTemperatureQRCodeColor)color completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 环境温湿度测量
+ (void)deviceAmbientTemperatureHumidityMeasuremen:(CBPeripheral * _Nullable)peripheral mode:(enum YCAppControlMeasureMode)mode completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设备传感器数据
/// \param peripheral 连接设备
///
/// \param dataType 传感器类型
///
/// \param isEable 是否开启
///
/// \param completion 结果
///
+ (void)deviceSenserSaveData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCDeviceSenserSaveDataType)dataType isEable:(BOOL)isEable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送手机型号
/// \param peripheral 连接设备
///
/// \param mode 手机型号 如iPhone 13
///
/// \param completion 结果
///
+ (void)sendPhoneModeInfo:(CBPeripheral * _Nullable)peripheral mode:(NSString * _Nonnull)mode completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 运动数据回写
/// \param peripheral 连接设备
///
/// \param step 运动步数
///
/// \param state 运动状态
///
/// \param completion 结果
///
+ (void)deviceSportDataWriteBack:(CBPeripheral * _Nullable)peripheral step:(uint32_t)step state:(enum YCDeviceExerciseHeartRateType)state completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 计算心率发送到设备
/// \param peripheral 连接设备
///
/// \param heartRate 计算心率
///
/// \param completion 结果
///
+ (void)sendCaclulateHeartRate:(CBPeripheral * _Nullable)peripheral heartRate:(uint8_t)heartRate completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 预警信息
/// \param peripheral 连接设备
///
/// \param infoType 信息类型
///
/// \param message 信息内容
///
/// \param completion 结果
///
+ (void)sendWarningInformation:(CBPeripheral * _Nullable)peripheral infoType:(enum YCWarningInformationType)infoType message:(NSString * _Nullable)message completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送信息
/// \param peripheral 连接设备
///
/// \param index 信息标号
///
/// \param content 信息内容
///
/// \param completion 结果
///
+ (void)sendShowMessage:(CBPeripheral * _Nullable)peripheral index:(uint8_t)index content:(NSString * _Nullable)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 温湿度校准
/// \param peripheral 连接设备
///
/// \param temperaturerInteger 温度整数
///
/// \param temperaturerDecimal 温度小数
///
/// \param humidityInteger 湿度整数
///
/// \param humidityDecimal 湿度小数
///
/// \param completion 结果
///
+ (void)deviceTemperatureHumidityCalibration:(CBPeripheral * _Nullable)peripheral temperaturerInteger:(int8_t)temperaturerInteger temperaturerDecimal:(int8_t)temperaturerDecimal humidityInteger:(int8_t)humidityInteger humidityDecimal:(int8_t)humidityDecimal completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 生理周期
+ (void)sendMenstrualCycle:(CBPeripheral * _Nullable)peripheral time:(NSInteger)time duration:(uint8_t)duration cycle:(uint8_t)cycle completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 健康数据回写
/// \param peripheral 连接设备
///
/// \param dataType 测量数据类型
///
/// \param values 测量值集合
///
/// \param completion 结果
///
+ (void)deviceMeasurementDataWriteBack:(CBPeripheral * _Nullable)peripheral dataType:(enum YCMeasurementDataType)dataType values:(NSArray<NSNumber *> * _Nonnull)values completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 开启或关闭精准血压实时测量
/// \param peripheral 连接外设
///
/// \param isEnable 开启或关闭
///
/// \param systolicBloodPressure 收缩压
///
/// \param diastolicBloodPressure 舒张压
///
/// \param heartRate 心率
///
/// \param height 身高 单位: cm
///
/// \param weight 体重 单位: kg
///
/// \param age 年龄
///
/// \param gender 性别
///
/// \param completion 设置结果
///
+ (void)controlAccurateBloodPessureRealTimeMeasure:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable systolicBloodPressure:(uint8_t)systolicBloodPressure diastolicBloodPressure:(uint8_t)diastolicBloodPressure heartRate:(uint8_t)heartRate height:(uint8_t)height weight:(uint8_t)weight age:(uint8_t)age gender:(enum YCDeviceGender)gender completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 启动健康数据测量
/// \param peripheral 连接设备
///
/// \param measureType 测试方式
///
/// \param dataType 测量数据
///
/// \param completion 是否开启成功
///
+ (void)controlMeasureHealthData:(CBPeripheral * _Nullable)peripheral measureType:(enum YCAppControlHealthDataMeasureType)measureType dataType:(enum YCAppControlMeasureHealthDataType)dataType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送指定信息
/// \param peripheral <#peripheral description#>
///
/// \param messageType <#messageType description#>
///
/// \param title <#title description#>
///
/// \param content <#content description#>
///
/// \param completion <#completion description#>
///
+ (void)sendSpecifyMessage:(CBPeripheral * _Nullable)peripheral messageType:(enum YCDevcieSpecifyMessageType)messageType title:(NSString * _Nonnull)title content:(NSString * _Nonnull)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 血糖校准
/// \param peripheral 连接设备
///
/// \param bloodGlucoseInteger 血糖整数
///
/// \param bloodGlucoseDecimal 血糖小数
///
/// \param mode 校准模式
///
/// \param completion 结果
///
+ (void)bloodGlucoseCalibration:(CBPeripheral * _Nullable)peripheral bloodGlucoseInteger:(int8_t)bloodGlucoseInteger bloodGlucoseDecimal:(int8_t)bloodGlucoseDecimal mode:(enum YCBloodGlucoseCalibrationaMode)mode completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送PD值到设备(客户定制)
/// \param peripheral 连接设备
///
/// \param title 标题
///
/// \param unit 单位
///
/// \param values 24小时值(值 + 小时)
///
/// \param completion <#completion description#>
///
+ (void)sendPDValue:(CBPeripheral * _Nullable)peripheral title:(NSString * _Nonnull)title unit:(NSString * _Nonnull)unit values:(NSArray<NSNumber *> * _Nonnull)values latestValue:(int8_t)latestValue completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送定位信息值到设备(客户定制)
/// \param peripheral 连接设备
///
/// \param title 标题
///
/// \param unit 单位
///
/// \param values 24小时值(值 + 小时)
///
/// \param completion <#completion description#>
///
+ (void)sendLocationInformation:(CBPeripheral * _Nullable)peripheral locationType:(enum YCLocationInformationType)locationType content:(NSString * _Nonnull)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送名片到设备
/// \param peripheral 连接设备
///
/// \param businessCardType 名片类型
///
/// \param content 内容
///
/// \param completion 发送结果
///
+ (void)sendBusinessCard:(CBPeripheral * _Nullable)peripheral businessCardType:(enum YCBusinessCardType)businessCardType content:(NSString * _Nonnull)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送测试健康数据
/// \param peripheral 连接设备
///
/// \param dataType 数据类型
///
/// \param time 测量时间（时间戳）
///
/// \param values 值集合 [整数,小数], [sbp, dbp]
///
/// \param completion 结果
///
+ (void)sendMeasuredHealthData:(CBPeripheral * _Nullable)peripheral dataType:(enum YCMeasurementDataType)dataType time:(NSUInteger)time values:(NSArray<NSNumber *> * _Nonnull)values completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送生产批号信息
/// \param peripheral 连接设备
///
/// \param content 生产批号(充气血医疗批次号)
///
/// \param completion <#completion description#>
///
+ (void)sendProductionInformation:(CBPeripheral * _Nullable)peripheral content:(NSString * _Nonnull)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 尿酸校准
/// \param peripheral 连接设备
///
/// \param value 尿酸值 (umol/L)
///
/// \param completion 校准结果
///
+ (void)uricAcidCalibration:(CBPeripheral * _Nullable)peripheral value:(uint16_t)value completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 血酯校准
/// \param peripheral 连接设备
///
/// \param cholesterol 胆固醇 (mmol/L)
///
/// \param completion 设置
///
+ (void)bloodFatCalibration:(CBPeripheral * _Nullable)peripheral cholesterol:(double)cholesterol completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 发送手机UUID信息
/// \param peripheral 连接设备
///
/// \param content 手机的UUID
///
/// \param completion <#completion description#>
///
+ (void)sendPhoneUUIDToDevice:(CBPeripheral * _Nullable)peripheral content:(NSString * _Nonnull)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end

enum YCWeekDay : uint8_t;
enum YCSettingTemperatureModeType : uint8_t;
enum YCTemperatureType : uint8_t;

@interface YCProduct (SWIFT_EXTENSION(YCProductSDK))
/// 时间设置
/// \param peripheral 外设
///
/// \param year 年 2000+
///
/// \param month 月 1 ~ 12
///
/// \param day 日 1 ~ 31
///
/// \param hour 时(24) 0 ~ 23
///
/// \param minute 分 0 ~ 59
///
/// \param second 分 0 ~ 59
///
/// \param weekDay 星期
///
/// \param completion <#completion description#>
///
+ (void)setDeviceTime:(CBPeripheral * _Nullable)peripheral year:(uint16_t)year month:(uint8_t)month day:(uint8_t)day hour:(uint8_t)hour minute:(uint8_t)minute second:(uint8_t)second weekDay:(enum YCWeekDay)weekDay completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置手环与手机相同公历时间(注意：只支持公历)
+ (void)setDeviceSyncPhoneTime:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 当前系统是12小时制
///
/// returns:
/// <#description#>
+ (BOOL)isHour12Format SWIFT_WARN_UNUSED_RESULT;
/// 设置步数目标
/// \param peripheral 连接设备
///
/// \param step 步数目标
///
/// \param completion 设置结果
///
+ (void)setDeviceStepGoal:(CBPeripheral * _Nullable)peripheral step:(uint32_t)step completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置卡路里目标
/// \param peripheral 连接设备
///
/// \param calories 卡路里目标
///
/// \param completion 设置结果
///
+ (void)setDeviceCaloriesGoal:(CBPeripheral * _Nullable)peripheral calories:(uint32_t)calories completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置距离目标
/// \param peripheral 连接设备
///
/// \param calories 距离目标(米)
///
/// \param completion 设置结果
///
+ (void)setDeviceDistanceGoal:(CBPeripheral * _Nullable)peripheral distance:(uint32_t)distance completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置睡眠目标
/// \param peripheral 连接设备
///
/// \param hour 时
///
/// \param minute 分
///
/// \param completion 设置结果
///
+ (void)setDeviceSleepGoal:(CBPeripheral * _Nullable)peripheral hour:(uint8_t)hour minute:(uint8_t)minute completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 运动时间目标
/// <ul>
///   <li>
///     Parameters:
///   </li>
///   <li>
///     peripheral: 连接
///   </li>
///   <li>
///     hour: 运动时间 小时部分
///   </li>
///   <li>
///     minute: 运动时间 分钟部分
///   </li>
///   <li>
///     completion: 设置结果
///   </li>
/// </ul>
+ (void)setDeviceSportTimeGoal:(CBPeripheral * _Nullable)peripheral hour:(uint8_t)hour minute:(uint8_t)minute completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 有效步数目标
/// \param peripheral 连接设备
///
/// \param effectiveSteps 有效步数目标 (步)
///
/// \param completion 设置结果
///
+ (void)setDeviceEffectiveStepsGoal:(CBPeripheral * _Nullable)peripheral effectiveSteps:(uint32_t)effectiveSteps completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 用户信息设置
/// \param peripheral 连接设备
///
/// \param height 身高
///
/// \param weight 体重
///
/// \param gender 性别
///
/// \param age 年龄
///
/// \param completion 设置结果
///
+ (void)setDeviceUserInfo:(CBPeripheral * _Nullable)peripheral height:(uint8_t)height weight:(uint8_t)weight gender:(enum YCDeviceGender)gender age:(uint8_t)age completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 单位设置
/// \param peripheral 连接设备
///
/// \param distance 距离单位
///
/// \param weight 体重单位
///
/// \param temperature 温度单位
///
/// \param timeFormat 时间格式12小时制/24小时制
///
/// \param bloodGlucose 血糖单位
///
/// \param completion 设置结果
///
+ (void)setDeviceUnit:(CBPeripheral * _Nullable)peripheral distance:(enum YCDeviceDistanceType)distance weight:(enum YCDeviceWeightType)weight temperature:(enum YCDeviceTemperatureType)temperature timeFormat:(enum YCDeviceTimeType)timeFormat bloodGlucoseOrBloodFat:(enum YCDeviceBloodGlucoseType)bloodGlucoseOrBloodFat uricAcid:(enum YCDeviceUricAcidType)uricAcid completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 久坐提醒(生成objc)
/// \param peripheral 连接时间
///
/// \param startHour1 开始时间1 小时 0 ~ 23
///
/// \param startMinute1 开始时间1 分钟: 0 ~ 59
///
/// \param endHour1 结束时间1 小时 0 ~ 23
///
/// \param endMinute1 结束时间1 分钟: 0 ~ 59
///
/// \param startHour2 开始时间2 小时 0 ~ 23
///
/// \param startMinute2 开始时间2 分钟: 0 ~ 59
///
/// \param endHour2 结束时间2 小时 0 ~ 23
///
/// \param endMinute2 结束时间2 分钟: 0 ~ 59
///
/// \param interval 15 ~ 45 分钟
///
/// \param repeat 重复值  <YCDeviceWeekRepeat>
///
/// \param completion 设置结果
///
+ (void)setDeviceSedentary:(CBPeripheral * _Nullable)peripheral startHour1:(uint8_t)startHour1 startMinute1:(uint8_t)startMinute1 endHour1:(uint8_t)endHour1 endMinute1:(uint8_t)endMinute1 startHour2:(uint8_t)startHour2 startMinute2:(uint8_t)startMinute2 endHour2:(uint8_t)endHour2 endMinute2:(uint8_t)endMinute2 interval:(uint8_t)interval repeat:(NSSet * _Nonnull)repeat completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 防丢设置
/// \param peripheral 连接设备
///
/// \param antiLostType 防丢类型
///
/// \param completion 设置结果
///
+ (void)setDeviceAntiLost:(CBPeripheral * _Nullable)peripheral antiLostType:(enum YCDeviceAntiLostType)antiLostType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)setDeviceAntiLost:(CBPeripheral * _Nullable)peripheral antiLostType:(enum YCDeviceAntiLostType)antiLostType rssi:(int8_t)rssi delay:(uint8_t)delay supportDisconnectDelay:(BOOL)supportDisconnectDelay repeat:(BOOL)repeat completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 左右手佩戴设置
/// \param peripheral 连接设备
///
/// \param wearingPosition 佩戴位置
///
/// \param completion 设置结果
///
///
/// returns:
/// <#description#>
+ (void)setDeviceWearingPosition:(CBPeripheral * _Nullable)peripheral wearingPosition:(enum YCDeviceWearingPositionType)wearingPosition completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置手机系统信息
/// \param peripheral 连接设备
///
/// \param phoneSystemType 手机系统
///
/// \param systemVersion 系统版本
///
/// \param completion 结果
///
+ (void)setPhoneSystemInfo:(CBPeripheral * _Nullable)peripheral phoneSystemType:(enum YCPhoneSystemType)phoneSystemType systemVersion:(NSString * _Nonnull)systemVersion completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置消息提醒类型(Objc)
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param infoPushType 消息类型成员数组 YCDeviceInfoPushType 成员 [NSSet setWithObjects:@(YCDeviceInfoPushTypeQq), @(YCDeviceInfoPushTypeWechat), … , nil]
///
/// \param completion 结果
///
+ (void)setDeviceInfoPush:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable infoPushType:(NSSet * _Nonnull)infoPushType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 心率报警
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param maxHeartRate 心率告警上限 100 ~ 240
///
/// \param minHeartRate 心率告警下限 30 ~ 60
///
/// \param completion 设置结果
///
+ (void)setDeviceHeartRateAlarm:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable maxHeartRate:(uint8_t)maxHeartRate minHeartRate:(uint8_t)minHeartRate completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 健康监测
/// \param peripheral 连接设备
///
/// \param isEnable 是否使能
///
/// \param interval 监测间隔 1 ~ 60分钟
///
/// \param completion <#completion description#>
///
+ (void)setDeviceHealthMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)setDeviceHeartRateMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion SWIFT_DEPRECATED_MSG("use func setDeviceHealthMonitoringMode instand of it", "setDeviceHealthMonitoringMode:isEnable:interval:completion:");
+ (void)setDeviceFindPhone:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 恢复出厂设置
/// \param peripheral 连接设备
///
/// \param completion 无
///
+ (void)setDeviceReset:(CBPeripheral * _Nullable)peripheral completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 勿扰模式设置
/// <ul>
///   <li>
///     Parameters:
///   </li>
///   <li>
///     peripheral: 连接设备
///   </li>
///   <li>
///     isEable: 是否使用
///   </li>
///   <li>
///     startHour: 开始时间 小时 0 ~ 23
///   </li>
///   <li>
///     startMinute: 开始时间 分钟 0 ~ 59
///   </li>
///   <li>
///     endHour: 结束时间 小时 0 ~ 23
///   </li>
///   <li>
///     endMinute: 结束时间 分钟 0 ~ 59
///   </li>
///   <li>
///     completion: 设置结果
///   </li>
/// </ul>
+ (void)setDeviceNotDisturb:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable startHour:(uint8_t)startHour startMinute:(uint8_t)startMinute endHour:(uint8_t)endHour endMinute:(uint8_t)endMinute completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)setDeviceAncsEnable:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置语言
/// \param peripheral 连接设备
///
/// \param language 语言
///
/// \param completion 设置结果
///
+ (void)setDeviceLanguage:(CBPeripheral * _Nullable)peripheral language:(enum YCDeviceLanguageType)language completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 抬腕亮屏开关
/// \param peripheral 连接设备
///
/// \param isEnable 是否使能
///
/// \param completion 执行回调
///
+ (void)setDeviceWristBrightScreen:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 屏幕亮度设置
/// \param peripheral 连接设备
///
/// \param level 亮度等级
///
/// \param completion 设置结果
///
+ (void)setDeviceDisplayBrightness:(CBPeripheral * _Nullable)peripheral level:(enum YCDeviceDisplayBrightnessLevel)level completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 肤色设置
/// \param peripheral 连接设备
///
/// \param level 肤色
///
/// \param completion 设置结果
///
+ (void)setDeviceSkinColor:(CBPeripheral * _Nullable)peripheral level:(enum YCDeviceSkinColorLevel)level completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 血压范围设置
/// \param peripheral 连接设备
///
/// \param level 血压范围
///
/// \param completion 设置结果
///
+ (void)setDeviceBloodPressureRange:(CBPeripheral * _Nullable)peripheral level:(enum YCDeviceBloodPressureLevel)level completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置蓝牙名称
/// \param peripheral 连接设备
///
/// \param name 新名称
///
/// \param completion 设置结果
///
+ (void)setDeviceName:(CBPeripheral * _Nullable)peripheral name:(NSString * _Nonnull)name completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置传感器采样率
/// \param peripheral 连接设备
///
/// \param ppg PPG采样率 HZ
///
/// \param ecg ECG采样率 HZ
///
/// \param gSensor G-Sensor采样率 HZ
///
/// \param tempeatureSensor 温度传感器 HZ
///
/// \param completion 设置结果
///
+ (void)setDeviceSensorSamplingRate:(CBPeripheral * _Nullable)peripheral ppg:(uint16_t)ppg ecg:(uint16_t)ecg gSensor:(uint16_t)gSensor tempeatureSensor:(uint16_t)tempeatureSensor completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 主题设置
/// \param peripheral 连接设置
///
/// \param index 主题索引
///
/// \param completion 设置结果
///
+ (void)setDeviceTheme:(CBPeripheral * _Nullable)peripheral index:(uint8_t)index completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 睡眠提醒时间
/// \param peripheral 连接设备
///
/// \param hour 小时 0 ~ 23
///
/// \param minute 分钟: 0 ~ 59
///
/// \param repeat 重复 YCDeviceWeekRepeat
///
/// \param completion 设置结果
///
+ (void)setDeviceSleepReminder:(CBPeripheral * _Nullable)peripheral hour:(uint8_t)hour minute:(uint8_t)minute repeat:(NSSet * _Nonnull)repeat completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 数据采集配置
/// \param peripheral 连接设备
///
/// \param isEnable 是否使能
///
/// \param dataType 数据类型
///
/// \param acquisitionTime 采集时长 单位: 秒, 关闭时使用0。
///
/// \param acquisitionInterval 采集间隔 单位分, 关闭时使用0。
///
/// \param completion 设置成功
///
+ (void)setDeviceDataCollection:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable dataType:(enum YCDeviceDataCollectionType)dataType acquisitionTime:(uint8_t)acquisitionTime acquisitionInterval:(uint8_t)acquisitionInterval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)setDeviceBloodPressureMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion SWIFT_DEPRECATED_MSG("No need to use this method");
+ (void)setDeviceTemperatureMode:(CBPeripheral * _Nullable)peripheral mode:(enum YCSettingTemperatureModeType)mode temperature:(int8_t)temperature completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置机主信息
/// \param peripheral 已连接设备
///
/// \param name 姓名
///
/// \param className 班级
///
/// \param identifier 身份标示
///
/// \param completion 设置结果
///
+ (void)setDeviceOwnerInfo:(CBPeripheral * _Nullable)peripheral name:(NSString * _Nonnull)name className:(NSString * _Nonnull)className identifier:(NSString * _Nonnull)identifier completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 温度报警
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param temperatureType 温度类型 YCTemperatureType
///
/// \param highTemperatureIntegerValue 温度上限整数值 36 ~ 100 摄氏度
///
/// \param highTemperatureDecimalValue 温度上限小数值 0 ~ 9 摄氏度
///
/// \param lowTemperatureIntegerValue 温度下限 -127 ~ 36 摄氏度
///
/// \param lowTemperatureDecimalValue 温度下限小数值 0 ~ 9 摄氏度
///
/// \param completion 设置结果
///
+ (void)setDeviceTemperatureAlarm:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable temperatureType:(enum YCTemperatureType)temperatureType highTemperatureIntegerValue:(uint8_t)highTemperatureIntegerValue highTemperatureDecimalValue:(uint8_t)highTemperatureDecimalValue lowTemperatureIntegerValue:(int8_t)lowTemperatureIntegerValue lowTemperatureDecimalValue:(uint8_t)lowTemperatureDecimalValue completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
+ (void)setDeviceTemperatureMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion SWIFT_DEPRECATED_MSG("use func setDeviceHealthMonitoringMode instand of it", "setDeviceHealthMonitoringMode:isEnable:interval:completion:");
+ (void)setDeviceBreathScreen:(CBPeripheral * _Nullable)peripheral interval:(enum YCDeviceBreathScreenInterval)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion SWIFT_DEPRECATED_MSG("use setDeviceScreenTime instand of it", "setDeviceScreenTime:interval:completion:");
/// 亮屏时间设置
/// \param peripheral 连接设备
///
/// \param interval 时间间隔
///
/// \param completion 设置结果
///
+ (void)setDeviceScreenTime:(CBPeripheral * _Nullable)peripheral interval:(enum YCDeviceScreenTimeInterval)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 环境光监测模式设置
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param interval 时间间隔 1 ~ 60分钟
///
/// \param completion 设置结果
///
+ (void)setDeviceAmbientLightMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 工作模式
/// \param peripheral 连接设备
///
/// \param mode 工作模式
///
/// \param completion 设置结果
///
+ (void)setDeviceWorkMode:(CBPeripheral * _Nullable)peripheral mode:(enum YCDeviceWorkModeType)mode completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 意外监测
/// \param peripheral 连接
///
/// \param isEnable 是否开启
///
/// \param completion 设置结果
///
+ (void)setDeviceAccidentMonitoring:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设备提醒类型设置
/// \param peripheral 连接设备
///
/// \param isEnable 是否使能
///
/// \param remindType 提醒类型
///
/// \param completion 设置结果
///
+ (void)setDeviceReminderType:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable remindType:(enum YCDeviceRemindType)remindType completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 血氧模式监测
/// \param peripheral 连接设备
///
/// \param isEnable 是否开始
///
/// \param interval 间隔
///
/// \param completion 设置结果
///
+ (void)setDeviceBloodOxygenMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 环境温湿度监测模式设置
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param interval 间隔
///
/// \param completion 设置结果
///
+ (void)setDeviceTemperatureHumidityMonitoringMode:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable interval:(uint8_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 计步时间设置
/// \param peripheral 连接设备
///
/// \param time 时间 分钟
///
/// \param completion 设置结果
///
+ (void)setDevicePedometerTime:(CBPeripheral * _Nullable)peripheral time:(uint8_t)time completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 上传提醒
/// \param peripheral 连接设备
///
/// \param isEnable 是否开始
///
/// \param threshold 提醒值
///
/// \param completion 设置结果
///
+ (void)setDeviceUploadReminder:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable threshold:(uint8_t)threshold completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 蓝牙广播传输间隔
/// \param peripheral 连接设备
///
/// \param interval 间隔 20 ~ 10240ms
///
/// \param completion 设置结果
///
+ (void)setDeviceBroadcastInterval:(CBPeripheral * _Nullable)peripheral interval:(uint16_t)interval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置蓝牙发射功率设置
/// \param peripheral 连接设备
///
/// \param power 功率
///
/// \param completion 设置结果
///
+ (void)setDeviceTransmitPower:(CBPeripheral * _Nullable)peripheral power:(int8_t)power completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 运动心率区间设置
/// \param peripheral 连接设备
///
/// \param zoneType 运动类型
///
/// \param minimumHeartRate 心率最大值
///
/// \param maximumHeartRate 心率最小值
///
/// \param completion 设置结果
///
+ (void)setDeviceExerciseHeartRateZone:(CBPeripheral * _Nullable)peripheral zoneType:(enum YCDeviceExerciseHeartRateType)zoneType minimumHeartRate:(uint8_t)minimumHeartRate maximumHeartRate:(uint8_t)maximumHeartRate completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 不同工作模式下数据采集配置
/// \param peripheral 连接设备
///
/// \param mode 工作模式
///
/// \param dataType 采集数据类型
///
/// \param acquisitionTime 采集时长 秒
///
/// \param acquisitionInterval 采集间隔 分钟
///
/// \param completion 设置结果
///
+ (void)setDeviceWorkModeDataCollection:(CBPeripheral * _Nullable)peripheral mode:(enum YCDeviceWorkModeType)mode dataType:(enum YCDeviceDataCollectionType)dataType acquisitionTime:(uint16_t)acquisitionTime acquisitionInterval:(uint16_t)acquisitionInterval completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置显示保险界面
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param completion 设置结果
///
+ (void)setDeviceInsuranceInterfaceDisplay:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 修改地址
/// \param peripheral 连接设备
///
/// \param macaddress mac地址
///
/// \param completion <#completion description#>
///
+ (void)setDeviceMacAddress:(CBPeripheral * _Nullable)peripheral macaddress:(NSString * _Nonnull)macaddress completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 启动SOS使能开关
/// \param peripheral 已连接设备
///
/// \param isEnable 是否使能
///
/// \param completion 结果
///
+ (void)setDeviceSOSEnable:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 血压报警设置
/// \param peripheral 连接设备
///
/// \param isEnable 是否开启
///
/// \param maximumSystolicBloodPressure 最大收缩压
///
/// \param maximumDiastolicBloodPressure 最大舒张压
///
/// \param minimumSystolicBloodPressure 最小收缩压
///
/// \param minimumDiastolicBloodPressure 最小舒张压
///
/// \param completion 设置结果
///
+ (void)setDeviceBloodPressureAlarm:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable maximumSystolicBloodPressure:(uint8_t)maximumSystolicBloodPressure maximumDiastolicBloodPressure:(uint8_t)maximumDiastolicBloodPressure minimumSystolicBloodPressure:(uint8_t)minimumSystolicBloodPressure minimumDiastolicBloodPressure:(uint8_t)minimumDiastolicBloodPressure completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置血氧报警
/// \param peripheral 连接外设
///
/// \param isEnable 是否开启
///
/// \param minimum 最低血氧值
///
/// \param completion 结果
///
+ (void)setDeviceBloodOxygenAlarm:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable minimum:(uint8_t)minimum completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置马达需要时长
/// \param peripheral 连接设备
///
/// \param mode 马达震动类型
///
/// \param time 时长，单位毫秒
///
/// \param completion 设置结果
///
+ (void)setDeviceMotorVibrationTime:(CBPeripheral * _Nullable)peripheral mode:(enum YCDeviceMotorVibrationType)mode time:(uint32_t)time completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置马达震动等级
/// \param peripheral 连接设备
///
/// \param level 震动等级
///
/// \param completion 设置结果
///
+ (void)setDeviceMotorVibrationStrength:(CBPeripheral * _Nullable)peripheral level:(enum YCMotorVibrationStrengthLevel)level completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置呼吸率报警
/// \param peripheral 连接外设
///
/// \param isEnable 是否开启
///
/// \param maximum 报警值上限
///
/// \param minimum 报警值下限
///
/// \param completion 结果
///
+ (void)setDeviceRespirationRateAlarm:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable maximum:(uint8_t)maximum minimum:(uint8_t)minimum completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置血糖报警
/// \param peripheral 连接外设
///
/// \param isEnable 是否开启
///
/// \param hyperglycemia 高血糖(一位小数)
///
/// \param hypoglycemia 低血糖(一位小数)
///
/// \param severeHyperglycemia 严重高血糖(一位小数)
///
/// \param severeHypoglycemia 严重低血糖(一位小数)
///
/// \param completion 结果
///
+ (void)setDeviceBloodGlucoseAlarm:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable hyperglycemia:(double)hyperglycemia hypoglycemia:(double)hypoglycemia severeHyperglycemia:(double)severeHyperglycemia severeHypoglycemia:(double)severeHypoglycemia completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置自动测量类型
/// \param peripheral 已连接设备
///
/// \param isEnable 是否使能
///
/// \param measureType YCAppControlMeasureHealthDataType
///
/// \param time 间隔时间 (1 ~ 60 min)
///
/// \param completion 设置结果
///
+ (void)setAutoMeasure:(CBPeripheral * _Nullable)peripheral isEnable:(BOOL)isEnable measureType:(enum YCAppControlMeasureHealthDataType)measureType time:(NSInteger)time completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 设置杰理自定义表盘的参数
/// \param peripheral <#peripheral description#>
///
/// \param timeLocation <#timeLocation description#>
///
/// \param fontColor <#fontColor description#>
///
/// \param completion <#completion description#>
///
+ (void)setJLCustomDialParameter:(CBPeripheral * _Nullable)peripheral timeLocation:(enum YCDeviceFaceTimePosition)timeLocation fontColor:(uint16_t)fontColor completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
/// 定时任务提醒
/// \param peripheral 连接设备
///
/// \param periodicReminderType 提醒类型
///
/// \param startHour 开始小时
///
/// \param startMinute 开始分钟
///
/// \param endHour 结束小时
///
/// \param endMinute 结束分钟
///
/// \param repeat 星期重复 YCDeviceWeekRepeat
///
/// \param interval 间隔（分钟）0表示不再重复提醒
///
/// \param content 提醒内容
///
/// \param completion 设置结果
///
+ (void)setDevicePeriodicReminderTask:(CBPeripheral * _Nullable)peripheral periodicReminderType:(enum YCPeriodicReminderType)periodicReminderType startHour:(uint8_t)startHour startMinute:(uint8_t)startMinute endHour:(uint8_t)endHour endMinute:(uint8_t)endMinute repeat:(NSSet * _Nonnull)repeat interval:(uint8_t)interval content:(NSString * _Nonnull)content completion:(void (^ _Nullable)(enum YCProductState, id _Nullable))completion;
@end


/// 功能支持成员
SWIFT_CLASS("_TtC12YCProductSDK29YCProductFunctionSupportItems")
@interface YCProductFunctionSupportItems : NSObject
/// 血压
@property (nonatomic, readonly) BOOL isSupportBloodPressure;
/// 语言设置
@property (nonatomic, readonly) BOOL isSupportLanguageSettings;
/// 消息推送 (总开关)
@property (nonatomic, readonly) BOOL isSupportInformationPush;
/// 心率
@property (nonatomic, readonly) BOOL isSupportHeartRate;
/// OTA升级
@property (nonatomic, readonly) BOOL isSupportOta;
/// 实时数据上传
@property (nonatomic, readonly) BOOL isSupportRealTimeDataUpload;
/// 睡眠
@property (nonatomic, readonly) BOOL isSupportSleep;
/// 计步
@property (nonatomic, readonly) BOOL isSupportStep;
/// 多运动
@property (nonatomic, readonly) BOOL isSupportSport;
/// HRV
@property (nonatomic, readonly) BOOL isSupportHRV;
/// 呼吸率
@property (nonatomic, readonly) BOOL isSupportRespirationRate;
/// 血氧
@property (nonatomic, readonly) BOOL isSupportBloodOxygen;
/// 历史ECG
@property (nonatomic, readonly) BOOL isSupportHistoricalECG;
/// 实时ECG
@property (nonatomic, readonly) BOOL isSupportRealTimeECG;
/// 血压告警
@property (nonatomic, readonly) BOOL isSupportBloodPressureAlarm;
/// 心率告警
@property (nonatomic, readonly) BOOL isSupportHeartRateAlarm;
/// 闹钟数量(0表示不支持闹钟)
@property (nonatomic, readonly) NSInteger alarmClockCount;
/// 闹钟类型 起床
@property (nonatomic, readonly) BOOL isSupportAlarmTypeWakeUp;
/// 闹钟类型 起床
@property (nonatomic, readonly) BOOL isSupportAlarmTypeSleep;
/// 闹钟类型 锻炼
@property (nonatomic, readonly) BOOL isSupportAlarmTypeExercise;
/// 闹钟类型 吃药
@property (nonatomic, readonly) BOOL isSupportAlarmTypeMedicine;
/// 闹钟类型 约会
@property (nonatomic, readonly) BOOL isSupportAlarmTypeAppointment;
/// 闹钟类型 聚会
@property (nonatomic, readonly) BOOL isSupportAlarmTypeParty;
/// 闹钟类型 会议
@property (nonatomic, readonly) BOOL isSupportAlarmTypeMeeting;
/// 闹钟类型 自定义
@property (nonatomic, readonly) BOOL isSupportAlarmTypeCustom;
@property (nonatomic, readonly) BOOL isSupportInformationTypeTwitter;
@property (nonatomic, readonly) BOOL isSupportInformationTypeFacebook;
@property (nonatomic, readonly) BOOL isSupportInformationTypeWeiBo;
@property (nonatomic, readonly) BOOL isSupportInformationTypeQQ;
@property (nonatomic, readonly) BOOL isSupportInformationTypeWeChat;
@property (nonatomic, readonly) BOOL isSupportInformationTypeEmail;
@property (nonatomic, readonly) BOOL isSupportInformationTypeSMS;
@property (nonatomic, readonly) BOOL isSupportInformationTypeCall;
@property (nonatomic, readonly) BOOL isSupportInformationTypeTelegram;
@property (nonatomic, readonly) BOOL isSupportInformationTypeSkype;
@property (nonatomic, readonly) BOOL isSupportInformationTypeSnapchat;
@property (nonatomic, readonly) BOOL isSupportInformationTypeLine;
@property (nonatomic, readonly) BOOL isSupportInformationTypeLinkedIn;
@property (nonatomic, readonly) BOOL isSupportInformationTypeInstagram;
@property (nonatomic, readonly) BOOL isSupportInformationTypeMessenger;
@property (nonatomic, readonly) BOOL isSupportInformationTypeWhatsApp;
/// 翻腕亮屏
@property (nonatomic, readonly) BOOL isSupportWristBrightScreen;
/// 勿扰模式
@property (nonatomic, readonly) BOOL isSupportDoNotDisturbMode;
/// 血压水平设置
@property (nonatomic, readonly) BOOL isSupportBloodPressureLevelSetting;
/// 出厂设置
@property (nonatomic, readonly) BOOL isSupportFactorySettings;
/// 找设备
@property (nonatomic, readonly) BOOL isSupportFindDevice;
/// 找手机
@property (nonatomic, readonly) BOOL isSupportFindPhone;
/// 防丢提醒
@property (nonatomic, readonly) BOOL isSupportAntiLostReminder;
/// 久坐提醒
@property (nonatomic, readonly) BOOL isSupportSedentaryReminder;
/// 上传数据 加密
@property (nonatomic, readonly) BOOL isSupportUploadDataEncryption;
/// 通话
@property (nonatomic, readonly) BOOL isSupportCall;
/// 心电诊断
@property (nonatomic, readonly) BOOL isSupportECGDiagnosis;
/// 明日天气
@property (nonatomic, readonly) BOOL isSupportTomorrowWeather;
/// 今日天气
@property (nonatomic, readonly) BOOL isSupportTodayWeather;
/// 搜周边
@property (nonatomic, readonly) BOOL isSupportSearchAround;
/// 微信运动
@property (nonatomic, readonly) BOOL isSupportWeChatSports;
/// 肤色设置
@property (nonatomic, readonly) BOOL isSupportSkinColorSettings;
/// 温度
@property (nonatomic, readonly) BOOL isSupportTemperature;
/// 音乐控制
@property (nonatomic, readonly) BOOL isSupportMusicControl;
/// 主题
@property (nonatomic, readonly) BOOL isSupportTheme;
/// 电极位置
@property (nonatomic, readonly) BOOL isSupportElectrodePosition;
/// 血压校准
@property (nonatomic, readonly) BOOL isSupportBloodPressureCalibration;
/// CVRR
@property (nonatomic, readonly) BOOL isSupportCVRR;
/// 腋温测量
@property (nonatomic, readonly) BOOL isSupportAxillaryTemperatureMeasurement;
/// 温度预警
@property (nonatomic, readonly) BOOL isSupportTemperatureAlarm;
/// 温度校准
@property (nonatomic, readonly) BOOL isSupportTemperatureCalibration;
/// 机主信息编辑
@property (nonatomic, readonly) BOOL isSupportHostInfomationEdit;
/// 手动拍照
@property (nonatomic, readonly) BOOL isSupportManualPhotographing;
/// 摇一摇拍照
@property (nonatomic, readonly) BOOL isSupportShakePhotographing;
/// 女性生理周期
@property (nonatomic, readonly) BOOL isSupportFemalePhysiologicalCycle;
/// 表盘功能
@property (nonatomic, readonly) BOOL isSupportWatchFace;
/// 通讯录
@property (nonatomic, readonly) BOOL isSupportAddressBook;
/// ECG结果精准
@property (nonatomic, readonly) BOOL isECGResultsAccurate;
/// 登山
@property (nonatomic, readonly) BOOL isSupportMountaineering;
/// 足球
@property (nonatomic, readonly) BOOL isSupportFootball;
/// 乒乓球
@property (nonatomic, readonly) BOOL isSupportPingPang;
/// 户外跑步
@property (nonatomic, readonly) BOOL isSupportOutdoorRunning;
/// 室内跑步
@property (nonatomic, readonly) BOOL isSupportIndoorRunning;
/// 户外步行
@property (nonatomic, readonly) BOOL isSupportOutdoorWalking;
/// 室内步行
@property (nonatomic, readonly) BOOL isSupportIndoorWalking;
/// 实时监护模式
@property (nonatomic, readonly) BOOL isSupportRealTimeMonitoring;
/// 羽毛球
@property (nonatomic, readonly) BOOL isSupportBadminton;
/// 健走
@property (nonatomic, readonly) BOOL isSupportWalk;
/// 游泳
@property (nonatomic, readonly) BOOL isSupportSwimming;
/// 篮球
@property (nonatomic, readonly) BOOL isSupportPlayball;
/// 跳绳
@property (nonatomic, readonly) BOOL isSupportRopeskipping;
/// 骑行
@property (nonatomic, readonly) BOOL isSupportRiding;
/// 健身
@property (nonatomic, readonly) BOOL isSupportFitness;
/// 跑步
@property (nonatomic, readonly) BOOL isSupportRun;
/// 室内骑行
@property (nonatomic, readonly) BOOL isSupportIndoorRiding;
/// 踏步机
@property (nonatomic, readonly) BOOL isSupportStepper;
/// 划船机
@property (nonatomic, readonly) BOOL isSupportRowingMachine;
/// 仰卧起坐
@property (nonatomic, readonly) BOOL isSupportSitups;
/// 跳跃运动
@property (nonatomic, readonly) BOOL isSupportJumping;
/// 重量训练
@property (nonatomic, readonly) BOOL isSupportWeightTraining;
/// 瑜伽
@property (nonatomic, readonly) BOOL isSupportYoga;
/// 徒步
@property (nonatomic, readonly) BOOL isSupportOnfoot;
/// 同步运动实时数据
@property (nonatomic, readonly) BOOL isSupportSyncRealSportData;
/// 启动心率测量
@property (nonatomic, readonly) BOOL isSupportStartHeartRateMeasurement;
/// 启动血压测量
@property (nonatomic, readonly) BOOL isSupportStartBloodPressureMeasurement;
/// 启动血氧测量
@property (nonatomic, readonly) BOOL isSupportStartBloodOxygenMeasurement;
/// 启动体温测量
@property (nonatomic, readonly) BOOL isSupportStartBodyTemperatureMeasurement;
/// 启动呼吸率测量
@property (nonatomic, readonly) BOOL isSupportStartRespirationRateMeasurement;
/// 自定义表盘
@property (nonatomic, readonly) BOOL isSupportCustomWatchface;
/// 精准血压测量
@property (nonatomic, readonly) BOOL isSupportAccurateBloodPressureMeasurement;
/// SOS开关
@property (nonatomic, readonly) BOOL isSupportSOS;
/// 血氧报警
@property (nonatomic, readonly) BOOL isSupportBloodOxygenAlarm;
/// 精准血压实时数据上传
@property (nonatomic, readonly) BOOL isSupportAccurateBloodPressureRealTimeDataUpload;
/// Viber消息推送
@property (nonatomic, readonly) BOOL isSupportInformationTypeViber;
/// 其它消息推送
@property (nonatomic, readonly) BOOL isSupportInformationTypeOther;
/// 自定义表盘颜色翻转
@property (nonatomic, readonly) BOOL isFlipCustomDialColor;
/// 手表亮度调节五档
@property (nonatomic, readonly) BOOL isSupportFiveSpeedBrightness;
/// 震动强度设置
@property (nonatomic, readonly) BOOL isSupportVibrationIntensitySetting;
/// 亮屏时间设置
@property (nonatomic, readonly) BOOL isSupportScreenTimeSetting;
/// 亮屏亮度调节
@property (nonatomic, readonly) BOOL isSupportScreenBrightnessAdjust;
/// 血糖测量
@property (nonatomic, readonly) BOOL isSupportBloodGlucose;
/// 运动暂停
@property (nonatomic, readonly) BOOL isSupportSportPause;
/// 喝水提醒
@property (nonatomic, readonly) BOOL isSupportDrinkWaterReminder;
/// 发送名片
@property (nonatomic, readonly) BOOL isSupportBusinessCard;
/// 尿酸测量
@property (nonatomic, readonly) BOOL isSupportUricAcid;
/// 网球
@property (nonatomic, readonly) BOOL isSupportVolleyball;
/// 皮划艇
@property (nonatomic, readonly) BOOL isSupportKayak;
/// 轮滑
@property (nonatomic, readonly) BOOL isSupportRollerSkating;
/// 网球
@property (nonatomic, readonly) BOOL isSupportTennis;
/// 高尔夫
@property (nonatomic, readonly) BOOL isSupportGolf;
/// 椭圆机
@property (nonatomic, readonly) BOOL isSupportEllipticalMachine;
/// 舞蹈
@property (nonatomic, readonly) BOOL isSupportDance;
/// 攀岩
@property (nonatomic, readonly) BOOL isSupportRockClimbing;
/// 健身操
@property (nonatomic, readonly) BOOL isSupportAerobics;
/// 其它运动
@property (nonatomic, readonly) BOOL isSupportOtherSports;
/// 血酮测量
@property (nonatomic, readonly) BOOL isSupportBloodKetone;
/// 支持支付宝
@property (nonatomic, readonly) BOOL isSupportAliPay;
/// 安卓绑定
@property (nonatomic, readonly) BOOL isSupportAndroidBind;
/// 呼吸率告警
@property (nonatomic, readonly) BOOL isSupportRespirationRateAlarm;
/// 血脂测量
@property (nonatomic, readonly) BOOL isSupportBloodFat;
/// 独立监测时间(E200测量)
@property (nonatomic, readonly) BOOL isSupportIndependentMonitoringTime;
/// 本地录音上传
@property (nonatomic, readonly) BOOL isSupportLocalRecordingFileUpload;
/// 理疗记录
@property (nonatomic, readonly) BOOL isSupportPhysiotherapyRecords;
/// 是否支持消息推送 Zoom
@property (nonatomic, readonly) BOOL isSupportInformationTypeZoom;
/// 是否支持消息推送 TikTok
@property (nonatomic, readonly) BOOL isSupportInformationTypeTikTok;
/// 是否支持消息推送 KakaoTalk
@property (nonatomic, readonly) BOOL isSupportInformationTypeKakaoTalk;
/// 是否支持睡眠提醒
@property (nonatomic, readonly) BOOL isSupportSleepRemider;
/// 是否支持设备规格设置
@property (nonatomic, readonly) BOOL isSupportDeviceSpecificationsSetting;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 日志等级 (目前只支持两个，开和关)
typedef SWIFT_ENUM(NSInteger, YCProductLogLevel, open) {
  YCProductLogLevelOff = 0,
  YCProductLogLevelNormal = 1,
  YCProductLogLevelError = 2,
};


/// 日志管理器
SWIFT_CLASS("_TtC12YCProductSDK19YCProductLogManager")
@interface YCProductLogManager : NSObject
/// 全局对象
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) YCProductLogManager * _Nonnull shared;)
+ (YCProductLogManager * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
/// 日志文件路径
@property (nonatomic, readonly, copy) NSString * _Nonnull logFilePath;
/// 初始化
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


@interface YCProductLogManager (SWIFT_EXTENSION(YCProductSDK))
/// 读取日志文件中的数据
///
/// returns:
/// <#description#>
+ (NSString * _Nullable)readLogFileData SWIFT_WARN_UNUSED_RESULT;
@end


@interface YCProductLogManager (SWIFT_EXTENSION(YCProductSDK))
/// 清除日志内容
+ (void)clear;
@end

/// 操作状态标示
typedef SWIFT_ENUM(NSInteger, YCProductState, open) {
  YCProductStateUnknow = 0,
  YCProductStateResetting = 1,
  YCProductStateUnsupported = 2,
  YCProductStateUnauthorized = 3,
  YCProductStatePoweredOff = 4,
  YCProductStatePoweredOn = 5,
  YCProductStateDisconnected = 6,
  YCProductStateConnected = 7,
  YCProductStateConnectedFailed = 8,
  YCProductStateUnavailable = 9,
  YCProductStateTimeout = 10,
  YCProductStateDataError = 11,
  YCProductStateCrcError = 12,
  YCProductStateDataTypeError = 13,
  YCProductStateSucceed = 14,
  YCProductStateFailed = 15,
  YCProductStateNoRecord = 16,
  YCProductStateParameterError = 17,
  YCProductStateAlarmNotExist = 18,
  YCProductStateAlarmAlreadyExist = 19,
  YCProductStateAlarmCountLimit = 20,
  YCProductStateAlarmTypeNotSupport = 21,
  YCProductStateInvalidMacaddress = 22,
};


/// 用户配置
SWIFT_CLASS("_TtC12YCProductSDK26YCProductUserConfiguration")
@interface YCProductUserConfiguration : NSObject
/// 步数目标
@property (nonatomic, readonly) NSInteger stepGoal;
/// 卡路里目标
@property (nonatomic, readonly) NSInteger calorieGoal;
/// 距离
@property (nonatomic, readonly) NSInteger distanceGoal;
/// 睡眠目标(小时部分)
@property (nonatomic, readonly) NSInteger sleepGoalHour;
/// 睡眠目标(分钟部分)
@property (nonatomic, readonly) NSInteger sleepGoalMinute;
/// 身高(cm)
@property (nonatomic, readonly) NSInteger height;
/// 体重(kg)
@property (nonatomic, readonly) NSInteger weight;
/// 性别
@property (nonatomic, readonly) enum YCDeviceGender gender;
/// 年龄
@property (nonatomic, readonly) NSInteger age;
/// 距离单位
@property (nonatomic, readonly) enum YCDeviceDistanceType distanceUnit;
/// 体重单位
@property (nonatomic, readonly) enum YCDeviceWeightType weightUnit;
/// 温度单位
@property (nonatomic, readonly) enum YCDeviceTemperatureType temperatureUnit;
/// 显示时间方式
@property (nonatomic, readonly) enum YCDeviceTimeType showTimeMode;
@property (nonatomic, readonly) uint8_t startHour1;
@property (nonatomic, readonly) uint8_t startMinute1;
@property (nonatomic, readonly) uint8_t endHour1;
@property (nonatomic, readonly) uint8_t endMinute1;
@property (nonatomic, readonly) uint8_t startHour2;
@property (nonatomic, readonly) uint8_t startMinute2;
@property (nonatomic, readonly) uint8_t endHour2;
@property (nonatomic, readonly) uint8_t endMinute2;
@property (nonatomic, readonly) uint8_t sedentaryReminderInterval;
/// 重复时间集合(和 sedentaryReminderRepeat` 取值相同) YCDeviceWeekRepeat 类型
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull sedentaryReminderRepeatArray;
@property (nonatomic, readonly) enum YCDeviceAntiLostType antiLostType;
@property (nonatomic, readonly) int8_t rssi;
@property (nonatomic, readonly) uint8_t antiLostDelay;
@property (nonatomic, readonly) BOOL antiLostDisconnectDelay;
@property (nonatomic, readonly) BOOL antiLostRepeat;
@property (nonatomic, readonly) BOOL infomationPushEnable;
/// 左右手
@property (nonatomic, readonly) enum YCDeviceWearingPositionType wearingPosition;
/// 心率报警开关
@property (nonatomic, readonly) BOOL heartRateAlarmEnable;
/// 心率报警值
@property (nonatomic, readonly) NSInteger heartRateAlarmValue;
/// 自动监测是否开启
@property (nonatomic, readonly) BOOL heartMonitoringModeEnable;
/// 监测间隔时间
@property (nonatomic, readonly) NSInteger monitoringInterval;
/// 语言
@property (nonatomic, readonly) enum YCDeviceLanguageType language;
/// 抬腕亮屏开关
@property (nonatomic, readonly) BOOL wristBrightenScreenEnable;
/// 屏幕亮度
@property (nonatomic, readonly) enum YCDeviceDisplayBrightnessLevel brightnessLevel;
/// 肤色设置
@property (nonatomic, readonly) enum YCDeviceSkinColorLevel skinColor;
/// 息屏时间
@property (nonatomic, readonly) enum YCDeviceBreathScreenInterval breathScreenInterval SWIFT_DEPRECATED_MSG("use YCDeviceScreenTimeInterval instand of it");
/// 亮屏时间
@property (nonatomic, readonly) enum YCDeviceScreenTimeInterval screenTimeInterval;
/// 蓝牙断开提醒
@property (nonatomic, readonly) BOOL deviceDisconnectedReminderEnable;
/// 上传提醒开关
@property (nonatomic, readonly) BOOL uploadReminderEnable;
@property (nonatomic, readonly) BOOL notDisturbEnable;
@property (nonatomic, readonly) NSInteger notDisturbStartHour;
@property (nonatomic, readonly) NSInteger notDisturbStartMinute;
@property (nonatomic, readonly) NSInteger notDisturbEndHour;
@property (nonatomic, readonly) NSInteger notDisturbEndMinute;
@property (nonatomic, readonly) BOOL sleepReminderEnable;
@property (nonatomic, readonly) NSInteger sleepReminderStartHour;
@property (nonatomic, readonly) NSInteger sleepReminderStartMinute;
/// 日程开关
@property (nonatomic, readonly) BOOL scheduleEnable;
/// 事件提醒开关
@property (nonatomic, readonly) BOOL eventReminderEable;
/// 意外监测开关
@property (nonatomic, readonly) BOOL accidentMonitorinEnable;
/// 体温报警
@property (nonatomic, readonly) BOOL bodyTemperatureAlarm;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


@interface YCProductUserConfiguration (SWIFT_EXTENSION(YCProductSDK))
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
@end

/// 查询健康数据的类型定义
typedef SWIFT_ENUM(uint8_t, YCQueryHealthDataType, open) {
  YCQueryHealthDataTypeStep = 0x02,
  YCQueryHealthDataTypeSleep = 0x04,
  YCQueryHealthDataTypeHeartRate = 0x06,
  YCQueryHealthDataTypeBloodPressure = 0x08,
  YCQueryHealthDataTypeCombinedData = 0x09,
  YCQueryHealthDataTypeBloodOxygen = 0x1A,
  YCQueryHealthDataTypeTemperatureHumidity = 0x1C,
  YCQueryHealthDataTypeBodyTemperature = 0x1E,
  YCQueryHealthDataTypeAmbientLight = 0x20,
  YCQueryHealthDataTypeWearState = 0x29,
  YCQueryHealthDataTypeHealthMonitoringData = 0x2B,
  YCQueryHealthDataTypeSportModeHistoryData = 0x2D,
  YCQueryHealthDataTypeInvasiveComprehensiveData = 0x2F,
};

/// 查询采样率
typedef SWIFT_ENUM(uint8_t, YCQuerySampleRateType, open) {
  YCQuerySampleRateTypePpg = 0,
  YCQuerySampleRateTypeEcg = 1,
  YCQuerySampleRateTypeMultiAxisSensor = 2,
};

/// 实时数据上传
typedef SWIFT_ENUM(uint8_t, YCRealTimeDataType, open) {
  YCRealTimeDataTypeStep = 0,
  YCRealTimeDataTypeHeartRate = 1,
  YCRealTimeDataTypeBloodOxygen = 2,
  YCRealTimeDataTypeBloodPressure = 3,
  YCRealTimeDataTypeHrv = 4,
  YCRealTimeDataTypeRespirationRate = 5,
  YCRealTimeDataTypeSportMode = 6,
  YCRealTimeDataTypeCombinedData = 7,
};


/// 实时综合数据
SWIFT_CLASS("_TtC12YCProductSDK35YCReceivedComprehensiveDataModeInfo")
@interface YCReceivedComprehensiveDataModeInfo : NSObject
/// 步数
@property (nonatomic, readonly) NSInteger step;
/// 距离
@property (nonatomic, readonly) NSInteger distance;
/// 卡路里
@property (nonatomic, readonly) NSInteger calories;
/// 心率值
@property (nonatomic, readonly) NSInteger heartRate;
/// 收缩压
@property (nonatomic, readonly) NSInteger systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) NSInteger diastolicBloodPressure;
/// 血氧值
@property (nonatomic, readonly) NSInteger bloodOxygen;
/// 呼吸率值
@property (nonatomic, readonly) NSInteger respirationRate;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 佩戴状态(是否已佩戴)
@property (nonatomic, readonly) BOOL isWorn;
/// 电池电量
@property (nonatomic, readonly) NSInteger batteryPower;
/// 脉博波信号的峰峰值间期(单位: 微秒)
@property (nonatomic, readonly) NSInteger ppi;
/// 打印字符串
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 接收到的实时数据的响应
SWIFT_CLASS("_TtC12YCProductSDK26YCReceivedDeviceReportInfo")
@interface YCReceivedDeviceReportInfo : NSObject
/// 收到的响应设备
@property (nonatomic, readonly, strong) CBPeripheral * _Nullable device;
/// 结果
@property (nonatomic, readonly) id _Nullable data;
/// 结果描述
@property (nonatomic, readonly, copy) NSString * _Nonnull dataDescription;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 实时监护数据
SWIFT_CLASS("_TtC12YCProductSDK28YCReceivedMonitoringModeInfo")
@interface YCReceivedMonitoringModeInfo : NSObject
/// 开始时间戳
@property (nonatomic, readonly) NSInteger startTimeStamp;
/// 心率值
@property (nonatomic, readonly) NSInteger heartRate;
/// 收缩压
@property (nonatomic, readonly) NSInteger systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) NSInteger diastolicBloodPressure;
/// 血氧值
@property (nonatomic, readonly) NSInteger bloodOxygen;
/// 呼吸率值
@property (nonatomic, readonly) NSInteger respirationRate;
/// 温度
@property (nonatomic, readonly) double temperature;
/// 实时步数
@property (nonatomic, readonly) NSInteger realStep;
/// 实时距离 (单位:米)
@property (nonatomic, readonly) uint16_t realDistance;
/// 实时卡路里 (单位:千卡)
@property (nonatomic, readonly) uint16_t realCalories;
/// 模式步数
@property (nonatomic, readonly) NSInteger modeStep;
/// 模式距离 (单位:米)
@property (nonatomic, readonly) uint16_t modeDistance;
/// 模式卡路里 (单位:千卡)
@property (nonatomic, readonly) uint16_t modeCalories;
/// 脉博波信号的峰峰值间期(单位: 微秒)
@property (nonatomic, readonly) NSInteger ppi;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 多通道PPG数据
SWIFT_CLASS("_TtC12YCProductSDK29YCReceivedMultiChannelPPGInfo")
@interface YCReceivedMultiChannelPPGInfo : NSObject
/// 组合数组
@property (nonatomic, readonly) enum YCMultiChannelPPGCompositeType compositeType;
/// 组合数据
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull compositeData;
/// green数据
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull greenData;
/// ir数据
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull irData;
/// red数据
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull redData;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 实时血压数据
SWIFT_CLASS("_TtC12YCProductSDK35YCReceivedRealTimeBloodPressureInfo")
@interface YCReceivedRealTimeBloodPressureInfo : NSObject
/// 心率值
@property (nonatomic, readonly) NSInteger heartRate;
/// 收缩压
@property (nonatomic, readonly) NSInteger systolicBloodPressure;
/// 舒张压
@property (nonatomic, readonly) NSInteger diastolicBloodPressure;
/// 血氧值
@property (nonatomic, readonly) NSInteger bloodOxygen;
/// HRV
@property (nonatomic, readonly) NSInteger hrv;
/// 温度
@property (nonatomic, readonly) double temperature;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 接收到实时数据
typedef SWIFT_ENUM(uint8_t, YCReceivedRealTimeDataType, open) {
  YCReceivedRealTimeDataTypeStep = 0,
  YCReceivedRealTimeDataTypeHeartRate = 1,
  YCReceivedRealTimeDataTypeBloodOxygen = 2,
  YCReceivedRealTimeDataTypeBloodPressure = 3,
  YCReceivedRealTimeDataTypePpg = 4,
  YCReceivedRealTimeDataTypeEcg = 5,
  YCReceivedRealTimeDataTypeSportMode = 6,
  YCReceivedRealTimeDataTypeRespirationRate = 7,
  YCReceivedRealTimeDataTypeNAxisSensor = 8,
  YCReceivedRealTimeDataTypeAmbientLight = 9,
  YCReceivedRealTimeDataTypeComprehensiveData = 10,
  YCReceivedRealTimeDataTypeSchedule = 11,
  YCReceivedRealTimeDataTypeEvent = 12,
  YCReceivedRealTimeDataTypeRealTimeMonitoringMode = 13,
  YCReceivedRealTimeDataTypeAccurateBloodPressureWaveform = 14,
  YCReceivedRealTimeDataTypeMultiChannelPPG = 15,
};


/// 实时步数
SWIFT_CLASS("_TtC12YCProductSDK26YCReceivedRealTimeStepInfo")
@interface YCReceivedRealTimeStepInfo : NSObject
/// 步数 (单 位:步)
@property (nonatomic, readonly) uint16_t step;
/// 距离 (单位:米)
@property (nonatomic, readonly) uint16_t distance;
/// 卡路里 (单位:千卡)
@property (nonatomic, readonly) uint16_t calories;
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 录音文件类型
SWIFT_CLASS("_TtC12YCProductSDK19YCRecordingFileInfo")
@interface YCRecordingFileInfo : NSObject
/// 文件名称
@property (nonatomic, readonly, copy) NSString * _Nonnull fileName;
/// 文件大小(总字节)
@property (nonatomic, readonly) uint32_t totalBytes;
/// CRC32数据
@property (nonatomic, readonly) uint32_t crcData;
/// 文件信息
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 录音文件数据信息
SWIFT_CLASS("_TtC12YCProductSDK26YCRecordingFileReceiveInfo")
@interface YCRecordingFileReceiveInfo : NSObject
/// 总大小
@property (nonatomic, readonly) uint32_t totalBytes;
/// 进度
@property (nonatomic, readonly) double progress;
/// 已经传输的字节数
@property (nonatomic, readonly, copy) NSData * _Nonnull receivcedData;
/// 是否传输完成 (如果不用这个标记会出现两次回调)
@property (nonatomic, readonly) BOOL isFinished;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// ******* 目标设置类型
typedef SWIFT_ENUM(uint8_t, YCSettingGoalType, open) {
  YCSettingGoalTypeStep = 0,
  YCSettingGoalTypeCalories = 1,
  YCSettingGoalTypeDistance = 2,
  YCSettingGoalTypeSleep = 3,
  YCSettingGoalTypeSportTime = 4,
  YCSettingGoalTypeEffectiveSteps = 5,
};

/// ******** 温度模式设置
typedef SWIFT_ENUM(uint8_t, YCSettingTemperatureModeType, open) {
  YCSettingTemperatureModeTypeBodyTemperature = 0,
};

/// 通讯录同步状态
typedef SWIFT_ENUM(uint8_t, YCSyncAddressBookState, open) {
  YCSyncAddressBookStateEnd = 0,
  YCSyncAddressBookStateSynchronizing = 1,
  YCSyncAddressBookStateStart = 2,
};

/// 温度设置类型
typedef SWIFT_ENUM(uint8_t, YCTemperatureType, open) {
  YCTemperatureTypeBodyTemperature = 0,
  YCTemperatureTypeAmbientTemperature = 1,
};

/// 预警信息类型
typedef SWIFT_ENUM(uint8_t, YCWarningInformationType, open) {
  YCWarningInformationTypeWarnSelf = 0,
  YCWarningInformationTypeWarnOthers = 1,
  YCWarningInformationTypeHighRisk = 2,
  YCWarningInformationTypeNonHighRisk = 3,
};

@class YCWatchFaceInfo;

/// 表盘的断点信息
SWIFT_CLASS("_TtC12YCProductSDK25YCWatchFaceBreakCountInfo")
@interface YCWatchFaceBreakCountInfo : NSObject
/// 表盘数据
@property (nonatomic, readonly, copy) NSArray<YCWatchFaceInfo *> * _Nonnull dials;
/// 支持的最大数量
@property (nonatomic, readonly) NSInteger limitCount;
/// 本地已存储的数量
@property (nonatomic, readonly) NSInteger localCount;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 表盘中的图片信息
SWIFT_CLASS("_TtC12YCProductSDK22YCWatchFaceDataBmpInfo")
@interface YCWatchFaceDataBmpInfo : NSObject
/// 背景图片的宽度
@property (nonatomic, readonly) NSInteger width;
/// 背景图片的高度
@property (nonatomic, readonly) NSInteger height;
/// 背景图片的大小(字节)
@property (nonatomic, readonly) NSInteger size;
/// 背景图片的半径
@property (nonatomic, readonly) NSInteger radius;
/// 缩略图片的宽度
@property (nonatomic, readonly) NSInteger thumbnailWidth;
/// 缩略图片的宽度
@property (nonatomic, readonly) NSInteger thumbnailHeight;
/// 缩略图片的大小(字节)
@property (nonatomic, readonly) NSInteger thumbnailSize;
/// 缩略图片的的半径
@property (nonatomic, readonly) NSInteger thumbnailRadius;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end


/// 表盘信息
SWIFT_CLASS("_TtC12YCProductSDK15YCWatchFaceInfo")
@interface YCWatchFaceInfo : NSObject
/// 表盘id
@property (nonatomic, readonly) uint32_t dialID;
/// 表盘包数
@property (nonatomic, readonly) uint16_t blockCount;
/// 支持删除
@property (nonatomic, readonly) BOOL isSupportDelete;
/// 表盘版本
@property (nonatomic, readonly) uint16_t version;
/// 是否为自定义表盘
@property (nonatomic, readonly) BOOL isCustomDial;
/// 当前显示表盘
@property (nonatomic, readonly) BOOL isShowing;
/// 显示
@property (nonatomic, readonly, copy) NSString * _Nonnull toString;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// 波形类型选择
typedef SWIFT_ENUM(uint8_t, YCWaveDataType, open) {
  YCWaveDataTypePpg = 0,
  YCWaveDataTypeEcg = 1,
  YCWaveDataTypeMultiAxisSensor = 2,
  YCWaveDataTypeAmbientLight = 3,
  YCWaveDataTypeMultiChannelPPG = 4,
};

/// 波形上传控制
typedef SWIFT_ENUM(uint8_t, YCWaveUploadState, open) {
  YCWaveUploadStateOff = 0,
  YCWaveUploadStateUploadWithOutSerialnumber = 1,
  YCWaveUploadStateUploadSerialnumber = 2,
};

/// 天气编码类型
typedef SWIFT_ENUM(uint8_t, YCWeatherCodeType, open) {
  YCWeatherCodeTypeUnknow = 0,
  YCWeatherCodeTypeSunny = 1,
  YCWeatherCodeTypeCloudy = 2,
  YCWeatherCodeTypeWind = 3,
  YCWeatherCodeTypeRain = 4,
  YCWeatherCodeTypeSnow = 5,
  YCWeatherCodeTypeFoggy = 6,
  YCWeatherCodeTypeSunnyCustom = 7,
  YCWeatherCodeTypeCloudyCustom = 8,
  YCWeatherCodeTypeThunderShower = 9,
  YCWeatherCodeTypeLightRain = 10,
  YCWeatherCodeTypeModerateRain = 11,
  YCWeatherCodeTypeHeavyRain = 12,
  YCWeatherCodeTypeRainSnow = 13,
  YCWeatherCodeTypeLightSnow = 14,
  YCWeatherCodeTypeModerateSnow = 15,
  YCWeatherCodeTypeHeavySnow = 16,
  YCWeatherCodeTypeFloatingDust = 17,
  YCWeatherCodeTypeFog = 18,
  YCWeatherCodeTypeHaze = 19,
  YCWeatherCodeTypeWindCustom = 20,
};

/// 天气中的月相信息
typedef SWIFT_ENUM(uint8_t, YCWeatherMoonType, open) {
  YCWeatherMoonTypeNewMoon = 0,
  YCWeatherMoonTypeWaningMoon = 1,
  YCWeatherMoonTypeTheLastQuarterMoon = 2,
  YCWeatherMoonTypeLowerConvexNoon = 3,
  YCWeatherMoonTypeFullMoon = 4,
  YCWeatherMoonTypeUpperConvexMoon = 5,
  YCWeatherMoonTypeFirstQuarterMoon = 6,
  YCWeatherMoonTypeCrescentMoon = 7,
  YCWeatherMoonTypeUnknown = 8,
};

/// 天气时段
typedef SWIFT_ENUM(uint8_t, YCWeatherPeriodType, open) {
  YCWeatherPeriodTypeToday = 0,
  YCWeatherPeriodTypeTomorrow = 1,
};

/// 3.5.2.1 星期表示
typedef SWIFT_ENUM(uint8_t, YCWeekDay, open) {
  YCWeekDayMonday = 0,
  YCWeekDayTuesday = 1,
  YCWeekDayWednesday = 2,
  YCWeekDayThursday = 3,
  YCWeekDayFriday = 4,
  YCWeekDaySaturday = 5,
  YCWeekDaySunday = 6,
};

#endif
#if defined(__cplusplus)
#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
