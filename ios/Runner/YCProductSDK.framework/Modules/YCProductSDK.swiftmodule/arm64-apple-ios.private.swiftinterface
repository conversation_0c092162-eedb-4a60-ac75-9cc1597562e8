// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 5.7.2 (swiftlang-*********.5 clang-1400.0.29.51)
// swift-module-flags: -target arm64-apple-ios9.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -module-name YCProductSDK
// swift-module-flags-ignorable: -enable-bare-slash-regex
import CoreBluetooth
import Foundation
import MobileCoreServices
import Swift
import UIKit
@_exported import YCProductSDK
import _Concurrency
import _StringProcessing
extension CoreBluetooth.CBPeripheral {
  @objc dynamic public var supportItems: YCProductSDK.YCProductFunctionSupportItems {
    @objc get
  }
  @objc dynamic public var basicInfo: YCProductSDK.YCDeviceBasicInfo {
    @objc get
    @objc set
  }
  @objc dynamic public var mcu: YCProductSDK.YCDeviceMCUType {
    @objc get
  }
  @objc dynamic public var rssiValue: Swift.Int {
    @objc get
  }
  @objc dynamic public var macAddress: Swift.String {
    @objc get
  }
  @objc dynamic public var deviceModel: Swift.String {
    @objc get
    @objc set
  }
  @objc dynamic public var advDataManufacturerData: Foundation.Data {
    @objc get
  }
  @objc override dynamic open var description: Swift.String {
    @objc get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCProductUserConfiguration : ObjectiveC.NSObject {
  @objc public var stepGoal: Swift.Int {
    get
  }
  @objc public var calorieGoal: Swift.Int {
    get
  }
  @objc public var distanceGoal: Swift.Int {
    get
  }
  @objc public var sleepGoalHour: Swift.Int {
    get
  }
  @objc public var sleepGoalMinute: Swift.Int {
    get
  }
  @objc public var height: Swift.Int {
    get
  }
  @objc public var weight: Swift.Int {
    get
  }
  @objc public var gender: YCProductSDK.YCDeviceGender {
    get
  }
  @objc public var age: Swift.Int {
    get
  }
  @objc public var distanceUnit: YCProductSDK.YCDeviceDistanceType {
    get
  }
  @objc public var weightUnit: YCProductSDK.YCDeviceWeightType {
    get
  }
  @objc public var temperatureUnit: YCProductSDK.YCDeviceTemperatureType {
    get
  }
  @objc public var showTimeMode: YCProductSDK.YCDeviceTimeType {
    get
  }
  @objc public var startHour1: Swift.UInt8 {
    get
  }
  @objc public var startMinute1: Swift.UInt8 {
    get
  }
  @objc public var endHour1: Swift.UInt8 {
    get
  }
  @objc public var endMinute1: Swift.UInt8 {
    get
  }
  @objc public var startHour2: Swift.UInt8 {
    get
  }
  @objc public var startMinute2: Swift.UInt8 {
    get
  }
  @objc public var endHour2: Swift.UInt8 {
    get
  }
  @objc public var endMinute2: Swift.UInt8 {
    get
  }
  @objc public var sedentaryReminderInterval: Swift.UInt8 {
    get
  }
  public var sedentaryReminderRepeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat> {
    get
  }
  @objc public var sedentaryReminderRepeatArray: [Foundation.NSNumber] {
    @objc get
  }
  @objc public var antiLostType: YCProductSDK.YCDeviceAntiLostType {
    get
  }
  @objc public var rssi: Swift.Int8 {
    get
  }
  @objc public var antiLostDelay: Swift.UInt8 {
    get
  }
  @objc public var antiLostDisconnectDelay: Swift.Bool {
    get
  }
  @objc public var antiLostRepeat: Swift.Bool {
    get
  }
  @objc public var infomationPushEnable: Swift.Bool {
    get
  }
  public var infomationPushItems: Swift.Set<YCProductSDK.YCDeviceInfoPushType> {
    get
  }
  @objc public var wearingPosition: YCProductSDK.YCDeviceWearingPositionType {
    get
  }
  @objc public var heartRateAlarmEnable: Swift.Bool {
    get
  }
  @objc public var heartRateAlarmValue: Swift.Int {
    get
  }
  @objc public var heartMonitoringModeEnable: Swift.Bool {
    get
  }
  @objc public var monitoringInterval: Swift.Int {
    get
  }
  @objc public var language: YCProductSDK.YCDeviceLanguageType {
    get
  }
  @objc public var wristBrightenScreenEnable: Swift.Bool {
    get
  }
  @objc public var brightnessLevel: YCProductSDK.YCDeviceDisplayBrightnessLevel {
    get
  }
  @objc public var skinColor: YCProductSDK.YCDeviceSkinColorLevel {
    get
  }
  @objc @available(*, deprecated, message: "use YCDeviceScreenTimeInterval instand of it")
  public var breathScreenInterval: YCProductSDK.YCDeviceBreathScreenInterval {
    get
  }
  @objc public var screenTimeInterval: YCProductSDK.YCDeviceScreenTimeInterval {
    get
  }
  @objc public var deviceDisconnectedReminderEnable: Swift.Bool {
    get
  }
  @objc public var uploadReminderEnable: Swift.Bool {
    get
  }
  @objc public var notDisturbEnable: Swift.Bool {
    get
  }
  @objc public var notDisturbStartHour: Swift.Int {
    get
  }
  @objc public var notDisturbStartMinute: Swift.Int {
    get
  }
  @objc public var notDisturbEndHour: Swift.Int {
    get
  }
  @objc public var notDisturbEndMinute: Swift.Int {
    get
  }
  @objc public var sleepReminderEnable: Swift.Bool {
    get
  }
  @objc public var sleepReminderStartHour: Swift.Int {
    get
  }
  @objc public var sleepReminderStartMinute: Swift.Int {
    get
  }
  @objc public var scheduleEnable: Swift.Bool {
    get
  }
  @objc public var eventReminderEable: Swift.Bool {
    get
  }
  @objc public var accidentMonitorinEnable: Swift.Bool {
    get
  }
  @objc public var bodyTemperatureAlarm: Swift.Bool {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
extension YCProductSDK.YCProductUserConfiguration {
  @objc dynamic public var toString: Swift.String {
    @objc get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCProductFunctionSupportItems : ObjectiveC.NSObject {
  @objc public var isSupportBloodPressure: Swift.Bool {
    get
  }
  @objc public var isSupportLanguageSettings: Swift.Bool {
    get
  }
  @objc public var isSupportInformationPush: Swift.Bool {
    get
  }
  @objc public var isSupportHeartRate: Swift.Bool {
    get
  }
  @objc public var isSupportOta: Swift.Bool {
    get
  }
  @objc public var isSupportRealTimeDataUpload: Swift.Bool {
    get
  }
  @objc public var isSupportSleep: Swift.Bool {
    get
  }
  @objc public var isSupportStep: Swift.Bool {
    get
  }
  @objc public var isSupportSport: Swift.Bool {
    get
  }
  @objc public var isSupportHRV: Swift.Bool {
    get
  }
  @objc public var isSupportRespirationRate: Swift.Bool {
    get
  }
  @objc public var isSupportBloodOxygen: Swift.Bool {
    get
  }
  @objc public var isSupportHistoricalECG: Swift.Bool {
    get
  }
  @objc public var isSupportRealTimeECG: Swift.Bool {
    get
  }
  @objc public var isSupportBloodPressureAlarm: Swift.Bool {
    get
  }
  @objc public var isSupportHeartRateAlarm: Swift.Bool {
    get
  }
  @objc public var alarmClockCount: Swift.Int {
    get
  }
  @objc public var isSupportAlarmTypeWakeUp: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeSleep: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeExercise: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeMedicine: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeAppointment: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeParty: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeMeeting: Swift.Bool {
    get
  }
  @objc public var isSupportAlarmTypeCustom: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeTwitter: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeFacebook: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeWeiBo: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeQQ: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeWeChat: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeEmail: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeSMS: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeCall: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeTelegram: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeSkype: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeSnapchat: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeLine: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeLinkedIn: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeInstagram: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeMessenger: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeWhatsApp: Swift.Bool {
    get
  }
  @objc public var isSupportWristBrightScreen: Swift.Bool {
    get
  }
  @objc public var isSupportDoNotDisturbMode: Swift.Bool {
    get
  }
  @objc public var isSupportBloodPressureLevelSetting: Swift.Bool {
    get
  }
  @objc public var isSupportFactorySettings: Swift.Bool {
    get
  }
  @objc public var isSupportFindDevice: Swift.Bool {
    get
  }
  @objc public var isSupportFindPhone: Swift.Bool {
    get
  }
  @objc public var isSupportAntiLostReminder: Swift.Bool {
    get
  }
  @objc public var isSupportSedentaryReminder: Swift.Bool {
    get
  }
  @objc public var isSupportUploadDataEncryption: Swift.Bool {
    get
  }
  @objc public var isSupportCall: Swift.Bool {
    get
  }
  @objc public var isSupportECGDiagnosis: Swift.Bool {
    get
  }
  @objc public var isSupportTomorrowWeather: Swift.Bool {
    get
  }
  @objc public var isSupportTodayWeather: Swift.Bool {
    get
  }
  @objc public var isSupportSearchAround: Swift.Bool {
    get
  }
  @objc public var isSupportWeChatSports: Swift.Bool {
    get
  }
  @objc public var isSupportSkinColorSettings: Swift.Bool {
    get
  }
  @objc public var isSupportTemperature: Swift.Bool {
    get
  }
  @objc public var isSupportMusicControl: Swift.Bool {
    get
  }
  @objc public var isSupportTheme: Swift.Bool {
    get
  }
  @objc public var isSupportElectrodePosition: Swift.Bool {
    get
  }
  @objc public var isSupportBloodPressureCalibration: Swift.Bool {
    get
  }
  @objc public var isSupportCVRR: Swift.Bool {
    get
  }
  @objc public var isSupportAxillaryTemperatureMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportTemperatureAlarm: Swift.Bool {
    get
  }
  @objc public var isSupportTemperatureCalibration: Swift.Bool {
    get
  }
  @objc public var isSupportHostInfomationEdit: Swift.Bool {
    get
  }
  @objc public var isSupportManualPhotographing: Swift.Bool {
    get
  }
  @objc public var isSupportShakePhotographing: Swift.Bool {
    get
  }
  @objc public var isSupportFemalePhysiologicalCycle: Swift.Bool {
    get
  }
  @objc public var isSupportWatchFace: Swift.Bool {
    get
  }
  @objc public var isSupportAddressBook: Swift.Bool {
    get
  }
  @objc public var isECGResultsAccurate: Swift.Bool {
    get
  }
  @objc public var isSupportMountaineering: Swift.Bool {
    get
  }
  @objc public var isSupportFootball: Swift.Bool {
    get
  }
  @objc public var isSupportPingPang: Swift.Bool {
    get
  }
  @objc public var isSupportOutdoorRunning: Swift.Bool {
    get
  }
  @objc public var isSupportIndoorRunning: Swift.Bool {
    get
  }
  @objc public var isSupportOutdoorWalking: Swift.Bool {
    get
  }
  @objc public var isSupportIndoorWalking: Swift.Bool {
    get
  }
  @objc public var isSupportRealTimeMonitoring: Swift.Bool {
    get
  }
  @objc public var isSupportBadminton: Swift.Bool {
    get
  }
  @objc public var isSupportWalk: Swift.Bool {
    get
  }
  @objc public var isSupportSwimming: Swift.Bool {
    get
  }
  @objc public var isSupportPlayball: Swift.Bool {
    get
  }
  @objc public var isSupportRopeskipping: Swift.Bool {
    get
  }
  @objc public var isSupportRiding: Swift.Bool {
    get
  }
  @objc public var isSupportFitness: Swift.Bool {
    get
  }
  @objc public var isSupportRun: Swift.Bool {
    get
  }
  @objc public var isSupportIndoorRiding: Swift.Bool {
    get
  }
  @objc public var isSupportStepper: Swift.Bool {
    get
  }
  @objc public var isSupportRowingMachine: Swift.Bool {
    get
  }
  @objc public var isSupportSitups: Swift.Bool {
    get
  }
  @objc public var isSupportJumping: Swift.Bool {
    get
  }
  @objc public var isSupportWeightTraining: Swift.Bool {
    get
  }
  @objc public var isSupportYoga: Swift.Bool {
    get
  }
  @objc public var isSupportOnfoot: Swift.Bool {
    get
  }
  @objc public var isSupportSyncRealSportData: Swift.Bool {
    get
  }
  @objc public var isSupportStartHeartRateMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportStartBloodPressureMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportStartBloodOxygenMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportStartBodyTemperatureMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportStartRespirationRateMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportCustomWatchface: Swift.Bool {
    get
  }
  @objc public var isSupportAccurateBloodPressureMeasurement: Swift.Bool {
    get
  }
  @objc public var isSupportSOS: Swift.Bool {
    get
  }
  @objc public var isSupportBloodOxygenAlarm: Swift.Bool {
    get
  }
  @objc public var isSupportAccurateBloodPressureRealTimeDataUpload: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeViber: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeOther: Swift.Bool {
    get
  }
  @objc public var isFlipCustomDialColor: Swift.Bool {
    get
  }
  @objc public var isSupportFiveSpeedBrightness: Swift.Bool {
    get
  }
  @objc public var isSupportVibrationIntensitySetting: Swift.Bool {
    get
  }
  @objc public var isSupportScreenTimeSetting: Swift.Bool {
    get
  }
  @objc public var isSupportScreenBrightnessAdjust: Swift.Bool {
    get
  }
  @objc public var isSupportBloodGlucose: Swift.Bool {
    get
  }
  @objc public var isSupportSportPause: Swift.Bool {
    get
  }
  @objc public var isSupportDrinkWaterReminder: Swift.Bool {
    get
  }
  @objc public var isSupportBusinessCard: Swift.Bool {
    get
  }
  @objc public var isSupportUricAcid: Swift.Bool {
    get
  }
  @objc public var isSupportVolleyball: Swift.Bool {
    get
  }
  @objc public var isSupportKayak: Swift.Bool {
    get
  }
  @objc public var isSupportRollerSkating: Swift.Bool {
    get
  }
  @objc public var isSupportTennis: Swift.Bool {
    get
  }
  @objc public var isSupportGolf: Swift.Bool {
    get
  }
  @objc public var isSupportEllipticalMachine: Swift.Bool {
    get
  }
  @objc public var isSupportDance: Swift.Bool {
    get
  }
  @objc public var isSupportRockClimbing: Swift.Bool {
    get
  }
  @objc public var isSupportAerobics: Swift.Bool {
    get
  }
  @objc public var isSupportOtherSports: Swift.Bool {
    get
  }
  @objc public var isSupportBloodKetone: Swift.Bool {
    get
  }
  @objc public var isSupportAliPay: Swift.Bool {
    get
  }
  @objc public var isSupportAndroidBind: Swift.Bool {
    get
  }
  @objc public var isSupportRespirationRateAlarm: Swift.Bool {
    get
  }
  @objc public var isSupportBloodFat: Swift.Bool {
    get
  }
  @objc public var isSupportIndependentMonitoringTime: Swift.Bool {
    get
  }
  @objc public var isSupportLocalRecordingFileUpload: Swift.Bool {
    get
  }
  @objc public var isSupportPhysiotherapyRecords: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeZoom: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeTikTok: Swift.Bool {
    get
  }
  @objc public var isSupportInformationTypeKakaoTalk: Swift.Bool {
    get
  }
  @objc public var isSupportSleepRemider: Swift.Bool {
    get
  }
  @objc public var isSupportDeviceSpecificationsSetting: Swift.Bool {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCCustomDeviceDataType : Swift.UInt8 {
  case none
  case cgmBloodGlucose
  case physiotherapyRecords
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCCustomCGMDeviceInfoType : Swift.UInt8 {
  case none = 0
  case startTimeAndSerialNumber = 1
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCCustomCGMDeviceInfo : ObjectiveC.NSObject {
  @objc public var infoType: YCProductSDK.YCCustomCGMDeviceInfoType {
    get
  }
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var serialNumber: Swift.String {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCCustomDeviceCGMData : ObjectiveC.NSObject {
  @objc public var round: Swift.Int {
    get
  }
  @objc public var bloodGlucose: Swift.Double {
    get
  }
  @objc public var quality: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCDevicePhysiotherapyOperationType : Swift.UInt8 {
  case continuous = 0
  case pulse
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDevicePhysiotherapyStartupType : Swift.UInt8 {
  case manual = 0
  case automatic
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCCustomDevicePhysiotherapyRecord : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var duration: Swift.Int {
    get
  }
  @objc public var operationType: YCProductSDK.YCDevicePhysiotherapyOperationType {
    get
  }
  @objc public var startupType: YCProductSDK.YCDevicePhysiotherapyStartupType {
    get
  }
  @objc public var powerLevel: Swift.UInt8 {
    get
  }
  @objc public var durationLevel: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCIntelligentFunctionType : Swift.UInt8 {
  case shortVideo = 1
  case music
  case read
  case takePhotoOrVideo
  case sos
  case slides
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCCollectDataType : Swift.UInt8 {
  case ecg = 0
  case ppg
  case triaxialAcceleration
  case sixAxisSensor
  case nineAxisSensor
  case triaxialMagnetometer
  case inflationBloodPressure
  case ppi
  case eda
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCCollectDataBasicInfo : ObjectiveC.NSObject {
  @objc public var dataType: YCProductSDK.YCCollectDataType {
    get
  }
  @objc public var index: Swift.UInt16 {
    get
  }
  @objc public var timeStamp: Swift.UInt32 {
    get
  }
  @objc public var sampleRate: Swift.UInt16 {
    get
  }
  @objc public var samplesCount: Swift.UInt8 {
    get
  }
  @objc public var totalBytes: Swift.UInt32 {
    get
  }
  @objc public var packages: Swift.UInt16 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCCollectDataInfo : ObjectiveC.NSObject {
  @objc public var basicInfo: YCProductSDK.YCCollectDataBasicInfo {
    get
  }
  @objc public var progress: Swift.Double {
    @objc get
  }
  @objc public var receivcedData: Foundation.NSMutableData {
    get
  }
  @objc public var isFinished: Swift.Bool {
    get
  }
  @objc public var data: [Swift.Int32] {
    get
  }
  @objc public var processedData: [Any] {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
public enum YCRecordingFileType : Swift.UInt8 {
  case mp3
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCRecordingFileInfo : ObjectiveC.NSObject {
  @objc public var fileName: Swift.String {
    get
  }
  public var fileType: YCProductSDK.YCRecordingFileType {
    get
  }
  @objc public var totalBytes: Swift.UInt32 {
    get
  }
  @objc public var crcData: Swift.UInt32 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCRecordingFileReceiveInfo : ObjectiveC.NSObject {
  @objc public var totalBytes: Swift.UInt32 {
    get
  }
  @objc public var progress: Swift.Double {
    @objc get
  }
  @objc public var receivcedData: Foundation.Data {
    get
  }
  @objc public var isFinished: Swift.Bool {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCReceivedRealTimeDataType : Swift.UInt8 {
  case step = 0
  case heartRate
  case bloodOxygen
  case bloodPressure
  case ppg
  case ecg
  case sportMode
  case respirationRate
  case nAxisSensor
  case ambientLight
  case comprehensiveData
  case schedule
  case event
  case realTimeMonitoringMode
  case accurateBloodPressureWaveform
  case multiChannelPPG
  @available(*, deprecated, message: "use toString instand of it")
  public var string: Swift.String {
    get
  }
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCReceivedDeviceReportInfo : ObjectiveC.NSObject {
  @objc public var device: CoreBluetooth.CBPeripheral? {
    get
  }
  @objc public var data: Any? {
    get
  }
  @objc public var dataDescription: Swift.String {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCReceivedRealTimeStepInfo : ObjectiveC.NSObject {
  @objc public var step: Swift.UInt16 {
    get
  }
  @objc public var distance: Swift.UInt16 {
    get
  }
  @objc public var calories: Swift.UInt16 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCReceivedRealTimeBloodPressureInfo : ObjectiveC.NSObject {
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var systolicBloodPressure: Swift.Int {
    get
  }
  @objc public var diastolicBloodPressure: Swift.Int {
    get
  }
  @objc public var bloodOxygen: Swift.Int {
    get
  }
  @objc public var hrv: Swift.Int {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCReceivedMonitoringModeInfo : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var systolicBloodPressure: Swift.Int {
    get
  }
  @objc public var diastolicBloodPressure: Swift.Int {
    get
  }
  @objc public var bloodOxygen: Swift.Int {
    get
  }
  @objc public var respirationRate: Swift.Int {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var realStep: Swift.Int {
    get
  }
  @objc public var realDistance: Swift.UInt16 {
    get
  }
  @objc public var realCalories: Swift.UInt16 {
    get
  }
  @objc public var modeStep: Swift.Int {
    get
  }
  @objc public var modeDistance: Swift.UInt16 {
    get
  }
  @objc public var modeCalories: Swift.UInt16 {
    get
  }
  @objc public var ppi: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCReceivedComprehensiveDataModeInfo : ObjectiveC.NSObject {
  @objc public var step: Swift.Int {
    get
  }
  @objc public var distance: Swift.Int {
    get
  }
  @objc public var calories: Swift.Int {
    get
  }
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var systolicBloodPressure: Swift.Int {
    get
  }
  @objc public var diastolicBloodPressure: Swift.Int {
    get
  }
  @objc public var bloodOxygen: Swift.Int {
    get
  }
  @objc public var respirationRate: Swift.Int {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var isWorn: Swift.Bool {
    get
  }
  @objc public var batteryPower: Swift.Int {
    get
  }
  @objc public var ppi: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCMultiChannelPPGCompositeType : Swift.UInt8 {
  case greenIrRed = 0
  case greenIr = 1
  case irRed = 2
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCReceivedMultiChannelPPGInfo : ObjectiveC.NSObject {
  @objc public var compositeType: YCProductSDK.YCMultiChannelPPGCompositeType {
    get
  }
  @objc public var compositeData: [Swift.Int32] {
    get
  }
  @objc public var greenData: [Swift.Int32] {
    get
  }
  @objc public var irData: [Swift.Int32] {
    get
  }
  @objc public var redData: [Swift.Int32] {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
extension YCProductSDK.YCProduct {
  @objc public static func setIntelligentFunctions(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, functionType: YCProductSDK.YCIntelligentFunctionType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func queryCustomCGMDeviceInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, infoType: YCProductSDK.YCCustomCGMDeviceInfoType = .startTimeAndSerialNumber, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryCustomDeviceData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCustomDeviceDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteCustomDeviceData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCustomDeviceDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
@objc public enum YCQueryHealthDataType : Swift.UInt8 {
  case step = 0x02
  case sleep = 0x04
  case heartRate = 0x06
  case bloodPressure = 0x08
  case combinedData = 0x09
  case bloodOxygen = 0x1A
  case temperatureHumidity = 0x1C
  case bodyTemperature = 0x1E
  case ambientLight = 0x20
  case wearState = 0x29
  case healthMonitoringData = 0x2B
  case sportModeHistoryData = 0x2D
  case invasiveComprehensiveData = 0x2F
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeleteHealthDataType : Swift.UInt8 {
  case step = 0x40
  case sleep = 0x41
  case heartRate = 0x42
  case bloodPressure = 0x43
  case combinedData = 0x44
  case bloodOxygen = 0x45
  case temperatureHumidity = 0x46
  case bodyTemperature = 0x47
  case ambientLight = 0x48
  case wearState = 0x49
  case healthMonitoringData = 0x4A
  case sportModeHistoryData = 0x4B
  case invasiveComprehensiveData = 0x4C
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataStep : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var endTimeStamp: Swift.Int {
    get
  }
  @objc public var step: Swift.Int {
    get
  }
  @objc public var distance: Swift.Int {
    get
  }
  @objc public var calories: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCHealthDataSleepType : Swift.Int {
  case unknow = 0
  case deepSleep = 0xF1
  case lightSleep = 0xF2
  case rem = 0xF3
  case awake = 0xF4
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataSleepDetail : ObjectiveC.NSObject {
  @objc public var sleepType: YCProductSDK.YCHealthDataSleepType {
    get
  }
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var duration: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataSleep : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var endTimeStamp: Swift.Int {
    get
  }
  @objc public var deepSleepCount: Swift.Int {
    get
  }
  @objc public var deepSleepMinutes: Swift.Int {
    get
  }
  @objc public var remSleepMinutes: Swift.Int {
    get
  }
  @objc public var lightSleepCount: Swift.Int {
    get
  }
  @objc public var lightSleepMinutes: Swift.Int {
    get
  }
  @objc public var deepSleepSeconds: Swift.Int {
    get
  }
  @objc public var remSleepSeconds: Swift.Int {
    get
  }
  @objc public var lightSleepSeconds: Swift.Int {
    get
  }
  @objc public var sleepDetailDatas: [YCProductSDK.YCHealthDataSleepDetail] {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCHealthDataMeasureMode : Swift.UInt8 {
  case single = 0
  case monitor
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataHeartRate : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var mode: YCProductSDK.YCHealthDataMeasureMode {
    get
  }
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCHealthDataBloodPressureMode : Swift.UInt8 {
  case single = 0
  case monitor
  case inflated
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataBloodPressure : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var mode: YCProductSDK.YCHealthDataBloodPressureMode {
    get
  }
  @objc public var systolicBloodPressure: Swift.Int {
    get
  }
  @objc public var diastolicBloodPressure: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataCombinedData : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var step: Swift.Int {
    get
  }
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var systolicBloodPressure: Swift.Int {
    get
  }
  @objc public var diastolicBloodPressure: Swift.Int {
    get
  }
  @objc public var bloodOxygen: Swift.Int {
    get
  }
  @objc public var respirationRate: Swift.Int {
    get
  }
  @objc public var hrv: Swift.Int {
    get
  }
  @objc public var cvrr: Swift.Int {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var temperatureValid: Swift.Bool {
    get
  }
  @objc public var fat: Swift.Double {
    get
  }
  @objc public var bloodGlucose: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataBloodOxygen : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var mode: YCProductSDK.YCHealthDataMeasureMode {
    get
  }
  @objc public var bloodOxygen: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataTemperatureHumidity : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var mode: YCProductSDK.YCHealthDataMeasureMode {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var humidity: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataBodyTemperature : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var mode: YCProductSDK.YCHealthDataMeasureMode {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataAmbientLight : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var mode: YCProductSDK.YCHealthDataMeasureMode {
    get
  }
  @objc public var ambientLight: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCHealthDataWearState : Swift.UInt8 {
  case wear = 0
  case fallOff
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataWearStateHistory : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var state: YCProductSDK.YCHealthDataWearState {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataMonitor : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var step: Swift.Int {
    get
  }
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var systolicBloodPressure: Swift.Int {
    get
  }
  @objc public var diastolicBloodPressure: Swift.Int {
    get
  }
  @objc public var bloodOxygen: Swift.Int {
    get
  }
  @objc public var respirationRate: Swift.Int {
    get
  }
  @objc public var hrv: Swift.Int {
    get
  }
  @objc public var cvrr: Swift.Int {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var temperatureValid: Swift.Bool {
    get
  }
  @objc public var humidity: Swift.Double {
    get
  }
  @objc public var ambientLight: Swift.Double {
    get
  }
  @objc public var sport: YCProductSDK.YCDeviceSportType {
    get
  }
  @objc public var distance: Swift.Int {
    get
  }
  @objc public var calories: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCHealthDataSportModeStartMethod : Swift.UInt8 {
  case app = 0
  case device
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataSportModeHistory : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var endTimeStamp: Swift.Int {
    get
  }
  @objc public var step: Swift.Int {
    get
  }
  @objc public var distance: Swift.Int {
    get
  }
  @objc public var calories: Swift.Int {
    get
  }
  @objc public var sport: YCProductSDK.YCDeviceSportType {
    get
  }
  @objc public var flag: YCProductSDK.YCHealthDataSportModeStartMethod {
    get
  }
  @objc public var heartRate: Swift.Int {
    get
  }
  @objc public var sportTime: Swift.Int {
    get
  }
  @objc public var minimumHeartRate: Swift.Int {
    get
  }
  @objc public var maximumHeartRate: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCHealthDataInvasiveComprehensiveDataMode : Swift.UInt8 {
  case single = 0
  case monitor
  case inflated
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCHealthDataInvasiveComprehensiveData : ObjectiveC.NSObject {
  @objc public var startTimeStamp: Swift.Int {
    get
  }
  @objc public var bloodGlucoseMode: YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode {
    get
  }
  @objc public var bloodGlucose: Swift.Double {
    get
  }
  @objc public var uricAcidMode: YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode {
    get
  }
  @objc public var uricAcid: Swift.UInt16 {
    get
  }
  @objc public var bloodKetoneMode: YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode {
    get
  }
  @objc public var bloodKetone: Swift.Double {
    get
  }
  @objc public var bloodFatMode: YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode {
    get
  }
  @objc public var totalCholesterol: Swift.Double {
    get
  }
  @objc public var hdlCholesterol: Swift.Double {
    get
  }
  @objc public var ldlCholesterol: Swift.Double {
    get
  }
  @objc public var triglycerides: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @objcMembers open class YCProduct : ObjectiveC.NSObject {
  @objc public static let shared: YCProductSDK.YCProduct
  @objc open var isReconnectEnable: Swift.Bool
  @objc open var isUpgrading: Swift.Bool {
    @objc get
    @objc set
  }
  @objc public static var isConnectForProduct: Swift.Bool
  @objc public static var filterProductID: [Swift.Int]
  @objc open var centralManagerState: YCProductSDK.YCProductState
  @objc open var currentPeripheral: CoreBluetooth.CBPeripheral? {
    get
  }
  @objc open var connectedPeripherals: [CoreBluetooth.CBPeripheral] {
    get
  }
  @objc open func reconnectedDevice()
  @objc deinit
}
extension YCProductSDK.YCProduct {
  @objc public static let connecteStateKey: Swift.String
  @objc public static let connectDeviceKey: Swift.String
  @objc public static let connecteStateKeyObjc: Swift.String
  @objc public static let ecgElectrodesStateKey: Swift.String
  @objc public static let photoelectricSensorStateKey: Swift.String
  @objc public static let deviceStateNotification: Foundation.Notification.Name
  @objc public static let receivedRealTimeNotification: Foundation.Notification.Name
  @objc public static let deviceControlNotification: Foundation.Notification.Name
  @objc public static func cancel()
}
extension YCProductSDK.YCProduct {
  @objc public static func queryHealthData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCQueryHealthDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteHealthData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCDeleteHealthDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func queryCollectDataBasicinfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCollectDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryCollectDataCount(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCollectDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryCollectDataInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCollectDataType, index: Swift.UInt16 = 0, uploadEnable: Swift.Bool = true, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryCollectDataInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCollectDataType, timeStamp: Swift.UInt32, uploadEnable: Swift.Bool = true, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteCollectData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCollectDataType, index: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteCollectData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCCollectDataType, timeStamp: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func queryDeviceRecordingListFileCount(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceRecordingListFilesInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, index: Swift.UInt16 = 1, count: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func synchronizeRecordingData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, fileName: Swift.String, offset: Swift.Int = 0, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func findDevice(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, remindCount: Swift.UInt8 = 5, remindInterval: Swift.UInt8 = 1, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceBloodPressureCalibration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, systolicBloodPressure: Swift.UInt8, diastolicBloodPressure: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func realTimeDataUplod(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, dataType: YCProductSDK.YCRealTimeDataType, interval: Swift.UInt8 = 2, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func querySampleRate(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCQuerySampleRateType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func waveDataUpload(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, state: YCProductSDK.YCWaveUploadState, dataType: YCProductSDK.YCWaveDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func controlSport(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, state: YCProductSDK.YCDeviceSportState, sportType: YCProductSDK.YCDeviceSportType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func takephotoByPhone(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendHealthParameters(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, warningState: YCProductSDK.YCHealthParametersState, healthState: YCProductSDK.YCHealthState, healthIndex: Swift.UInt8, othersWarningState: YCProductSDK.YCHealthParametersState, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceSystemOperator(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCDeviceSystemOperator, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceTemperatureCalibration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceArmpitTemperatureMeasurement(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceShowFriendMessage(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, index: Swift.UInt8, hour: Swift.UInt8, minute: Swift.UInt8, name: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceHealthValueWriteBack(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, healthValue: Swift.UInt8, statusDescription: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceSleepDataWriteBack(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, deepSleepHour: Swift.UInt8, deepSleepMinute: Swift.UInt8, lightSleepHour: Swift.UInt8, lightSleepMinute: Swift.UInt8, totalSleepHour: Swift.UInt8, totalSleepMinute: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func devicePersonalInfoWriteBack(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, infoType: YCProductSDK.YCPersonalInfoType, information: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceUpgradeReminderWriteBack(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, percentage: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceAmbientLightMeasurement(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCAppControlMeasureMode, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func changeDeviceBodyTemperatureQRCodeColor(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, color: YCProductSDK.YCBodyTemperatureQRCodeColor, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceAmbientTemperatureHumidityMeasuremen(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCAppControlMeasureMode, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceSenserSaveData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCDeviceSenserSaveDataType, isEable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendPhoneModeInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceSportDataWriteBack(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, step: Swift.UInt32, state: YCProductSDK.YCDeviceExerciseHeartRateType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendCaclulateHeartRate(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, heartRate: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendWarningInformation(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, infoType: YCProductSDK.YCWarningInformationType, message: Swift.String?, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendShowMessage(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, index: Swift.UInt8, content: Swift.String?, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceTemperatureHumidityCalibration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, temperaturerInteger: Swift.Int8, temperaturerDecimal: Swift.Int8, humidityInteger: Swift.Int8, humidityDecimal: Swift.Int8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendMenstrualCycle(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, time: Swift.Int, duration: Swift.UInt8, cycle: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deviceMeasurementDataWriteBack(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCMeasurementDataType, values: [Swift.UInt8], completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func controlAccurateBloodPessureRealTimeMeasure(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, systolicBloodPressure: Swift.UInt8, diastolicBloodPressure: Swift.UInt8, heartRate: Swift.UInt8, height: Swift.UInt8, weight: Swift.UInt8, age: Swift.UInt8, gender: YCProductSDK.YCDeviceGender, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func controlMeasureHealthData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, measureType: YCProductSDK.YCAppControlHealthDataMeasureType, dataType: YCProductSDK.YCAppControlMeasureHealthDataType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendSpecifyMessage(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, messageType: YCProductSDK.YCDevcieSpecifyMessageType = .ok, title: Swift.String, content: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func bloodGlucoseCalibration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, bloodGlucoseInteger: Swift.Int8, bloodGlucoseDecimal: Swift.Int8, mode: YCProductSDK.YCBloodGlucoseCalibrationaMode = .fasting, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendPDValue(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, title: Swift.String, unit: Swift.String, values: [Swift.Int8], latestValue: Swift.Int8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendLocationInformation(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, locationType: YCProductSDK.YCLocationInformationType, content: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendBusinessCard(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, businessCardType: YCProductSDK.YCBusinessCardType, content: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendMeasuredHealthData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCMeasurementDataType, time: Swift.UInt, values: [Swift.Int8], completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendProductionInformation(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, content: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func uricAcidCalibration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, value: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func bloodFatCalibration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, cholesterol: Swift.Double, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendPhoneUUIDToDevice(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, content: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func startECGMeasurement(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func stopECGMeasurement(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func startSendAddressBook(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func stopSendAddressBook(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func sendAddressBook(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, phone: Swift.String, name: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func sendWeatherData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, whichDay: YCProductSDK.YCWeatherPeriodType = .today, lowestTemperature: Swift.Int8, highestTemperature: Swift.Int8, realTimeTemperature: Swift.Int8, weatherType: YCProductSDK.YCWeatherCodeType, windDirection: Swift.String = "", windPower: Swift.String = "", location: Swift.String = "", moonType: YCProductSDK.YCWeatherMoonType = .unknown, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func sendWeatherData(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isTomorrow: Swift.Bool = false, lowestTemperature: Swift.Int8, highestTemperature: Swift.Int8, realTimeTemperature: Swift.Int8, weatherType: YCProductSDK.YCWeatherCodeType, windDirection: Swift.String? = nil, windPower: Swift.String? = nil, location: Swift.String? = nil, moonType: YCProductSDK.YCWeatherMoonType? = nil, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func downloadWatchFace(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, data: Foundation.NSData, dialID: Swift.UInt32, blockCount: Swift.UInt16, dialVersion: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryWatchFaceInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func changeWatchFace(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dialID: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteWatchFace(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dialID: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func generateCustomDialData(_ dialData: Foundation.Data, backgroundImage: UIKit.UIImage?, thumbnail: UIKit.UIImage?, timePosition: CoreFoundation.CGPoint, redColor: Swift.UInt8, greenColor: Swift.UInt8, blueColor: Swift.UInt8, isFlipColor: Swift.Bool = false) -> Foundation.Data
  @objc public static func queryDeviceBmpInfo(_ dialData: Foundation.Data) -> YCProductSDK.YCWatchFaceDataBmpInfo
}
extension YCProductSDK.YCProduct {
  @objc public static func presetWathcFaceTask(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func bootLogoTask(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func embeddedPeripheralFirmwareUpgrade(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, firmwareType: YCProductSDK.YCEmbeddedPeripheralFirmwareType, data: Foundation.NSData, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func queryDeviceBasicInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceMacAddress(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceModel(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceFunctionEnableState(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceCurrentHeartRate(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceCurrentBloodPressure(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceUserConfiguration(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceTheme(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceElectrodePosition(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceScreenInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceCurrentExerciseInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceHistorySummary(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceRealTimeTemperature(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceScreenDisplayInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceBloodOxygen(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceAmbientLight(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceTemperatureHumidity(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceSensorSampleInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCDeviceDataCollectionType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceWorkMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceUploadReminderInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceMCU(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceRemindSettingInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, dataType: YCProductSDK.YCDeviceRemindType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceAliPayActivationStatus(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceDisplayParameters(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryBusinessCardContent(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, businessCardType: YCProductSDK.YCBusinessCardType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
public func computeRepeatValue(_ repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>) -> Swift.UInt8
public func repeatWeekValueToSet(_ repeat: Swift.UInt8) -> Swift.Set<YCProductSDK.YCDeviceWeekRepeat>
public func stringToUInt(_ string: Swift.String) -> Swift.UInt
extension YCProductSDK.YCProduct {
  @objc public static func setLogLevel(_ printLevel: YCProductSDK.YCProductLogLevel = .off, saveLevel: YCProductSDK.YCProductLogLevel = .off)
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @objcMembers public class YCProductLogManager : ObjectiveC.NSObject {
  @objc public static let shared: YCProductSDK.YCProductLogManager
  @objc public var logFilePath: Swift.String {
    get
  }
  @objc deinit
}
extension YCProductSDK.YCProductLogManager {
  public static func write<T>(_ level: YCProductSDK.YCProductLogLevel = .normal, message: T, file: Swift.String = #file, method: Swift.String = #function, line: Swift.Int = #line, bleData: Foundation.Data? = nil)
  @objc public static func readLogFileData() -> Swift.String?
}
extension YCProductSDK.YCProductLogManager {
  @objc public static func clear()
}
extension YCProductSDK.YCProduct : CoreBluetooth.CBPeripheralDelegate {
  @objc dynamic open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didUpdateValueFor characteristic: CoreBluetooth.CBCharacteristic, error: Swift.Error?)
  @objc dynamic open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didUpdateNotificationStateFor characteristic: CoreBluetooth.CBCharacteristic, error: Swift.Error?)
  @objc dynamic open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didDiscoverCharacteristicsFor service: CoreBluetooth.CBService, error: Swift.Error?)
  @objc dynamic open func peripheral(_ peripheral: CoreBluetooth.CBPeripheral, didDiscoverServices error: Swift.Error?)
}
extension YCProductSDK.YCProduct : CoreBluetooth.CBCentralManagerDelegate {
  @objc dynamic open func centralManager(_ central: CoreBluetooth.CBCentralManager, didConnect peripheral: CoreBluetooth.CBPeripheral)
  @objc dynamic open func centralManager(_ central: CoreBluetooth.CBCentralManager, didDisconnectPeripheral peripheral: CoreBluetooth.CBPeripheral, error: Swift.Error?)
  @objc dynamic open func centralManager(_ central: CoreBluetooth.CBCentralManager, didFailToConnect peripheral: CoreBluetooth.CBPeripheral, error: Swift.Error?)
  @objc dynamic open func centralManager(_ central: CoreBluetooth.CBCentralManager, didDiscover peripheral: CoreBluetooth.CBPeripheral, advertisementData: [Swift.String : Any], rssi RSSI: Foundation.NSNumber)
  @objc dynamic open func centralManagerDidUpdateState(_ central: CoreBluetooth.CBCentralManager)
}
@objc public enum YCEmbeddedPeripheralFirmwareType : Swift.UInt8 {
  case none = 0
  case bloodpressure = 1
  case touchPanel = 2
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
extension YCProductSDK.YCProduct {
  @objc public static func checkRingDeviceHardwareState(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc dynamic open func clearQueue()
}
@objc public enum YCECGResultType : Swift.UInt {
  case failed = 0
  case atrialFibrillation
  case earlyHeartbeat
  case supraventricularHeartbeat
  case atrialBradycardia
  case atrialTachycardia
  case atrialArrhythmi
  case normal
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCECGMeasurementResult : ObjectiveC.NSObject {
  @objc public var hearRate: Swift.Int {
    get
  }
  @objc public var hrv: Swift.Int {
    get
  }
  @objc public var qrsType: Swift.Int {
    get
  }
  @objc public var afflag: Swift.Bool {
    get
  }
  @objc public var ecgMeasurementType: YCProductSDK.YCECGResultType {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCECGHeavyLoadLevel : Swift.UInt {
  case weak
  case suitable
  case strong
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc public enum YCECGPressureLevel : Swift.UInt {
  case relax
  case mild
  case moderate
  case severe
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc public enum YCECGHrvNormLevel : Swift.UInt {
  case abnormal
  case mild
  case normal
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc public enum YCECGBodyIndexLevel : Swift.UInt {
  case abnormal
  case mild
  case normal
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc public enum YCECGSympatheticNervousActivityLevel : Swift.UInt {
  case mild
  case moderate
  case severe
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCECGBodyIndexResult : ObjectiveC.NSObject {
  @objc public var isAvailable: Swift.Bool {
    @objc get
  }
  @objc public var heavyLoad: Swift.Float
  @objc public var headvyLoadLevel: YCProductSDK.YCECGHeavyLoadLevel {
    @objc get
  }
  @objc public var pressure: Swift.Float
  @objc public var pressureLevel: YCProductSDK.YCECGPressureLevel {
    @objc get
  }
  @objc public var hrvNorm: Swift.Float
  @objc public var hrvNormLevel: YCProductSDK.YCECGHrvNormLevel {
    @objc get
  }
  @objc public var body: Swift.Float
  @objc public var bodyLevel: YCProductSDK.YCECGBodyIndexLevel {
    @objc get
  }
  @objc public var sympatheticActivityIndex: Swift.Float
  @objc public var sympatheticActivityIndexLevel: YCProductSDK.YCECGSympatheticNervousActivityLevel {
    @objc get
  }
  @objc public var respiratoryRate: Swift.UInt
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCECGManager : ObjectiveC.NSObject {
  @objc override dynamic public init()
  @objc deinit
}
extension YCProductSDK.YCECGManager {
  @objc dynamic open func setupManagerInfo(rr: ((_ rr: Swift.Float, _ heartRate: Swift.Int) -> ())?, hrv: ((_ hrv: Swift.Int) -> ())?)
  @objc dynamic open func processECGData(_ data: Swift.Int) -> Swift.Float
  public static func getDrawECGData(_ ecgatas: [Swift.Int32], gridSize: Swift.Float, count: Swift.Int, isRawData: inout Swift.Bool) -> Foundation.NSMutableArray
  @objc public static func getDrawECGLineData(_ ecgatas: [Swift.Int32], gridSize: Swift.Float, count: Swift.Int) -> Foundation.NSMutableArray
  @objc public static func converKanYanECGData(_ ecgDatas: [Swift.Int32]) -> Foundation.NSMutableArray
}
extension YCProductSDK.YCECGManager {
  @objc dynamic open func getECGMeasurementResult(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, deviceHeartRate: Swift.Int, deviceHRV: Swift.Int, completion: @escaping (_ result: YCProductSDK.YCECGMeasurementResult) -> ())
  open func getECGMeasurementResult(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, deviceHeartRate: Swift.Int? = nil, deviceHRV: Swift.Int? = nil, completion: @escaping (_ result: YCProductSDK.YCECGMeasurementResult) -> ())
  @objc dynamic open func getPhysicalIndexParameters() -> YCProductSDK.YCECGBodyIndexResult
}
extension YCProductSDK.YCProduct {
  @objc public static func downloadUIFile(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, data: Foundation.NSData, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
@objc public enum YCProductState : Swift.Int {
  case unknow
  case resetting
  case unsupported
  case unauthorized
  case poweredOff
  case poweredOn
  case disconnected
  case connected
  case connectedFailed
  case unavailable
  case timeout
  case dataError
  case crcError
  case dataTypeError
  case succeed
  case failed
  case noRecord
  case parameterError
  case alarmNotExist
  case alarmAlreadyExist
  case alarmCountLimit
  case alarmTypeNotSupport
  case invalidMacaddress
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum YCDeviceRealState : Swift.UInt8 {
  case good = 0
  case fallof
  case fault
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceSportState : Swift.UInt8 {
  case stop = 0
  case start
  case pause
  case `continue`
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceSportType : Swift.UInt8 {
  case none = 0
  case run = 0x01
  case swimming = 0x02
  case riding = 0x03
  case fitness = 0x04
  case ropeskipping = 0x06
  case playball = 0x07
  case walk = 0x08
  case badminton = 0x09
  case football = 0x0A
  case mountaineering = 0x0B
  case pingPang = 0x0C
  case freeMode = 0x0D
  case indoorRunning = 0x0E
  case outdoorRunning = 0x0F
  case outdoorWalking = 0x10
  case indoorWalking = 0x11
  case runMode = 0x12
  case indoorRiding = 0x13
  case stepper = 0x14
  case rowingMachine = 0x15
  case realTimeMonitoring = 0x16
  case situps = 0x17
  case jumping = 0x18
  case weightTraining = 0x19
  case yoga = 0x1A
  case onfoot = 0x1B
  case volleyball = 0x1C
  case kayak = 0x1D
  case rollerSkating = 0x1E
  case tennis = 0x1F
  case golf = 0x20
  case ellipticalMachine = 0x21
  case dance = 0x22
  case rockClimbing = 0x23
  case aerobics = 0x24
  case otherSports = 0x25
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCWaveUploadState : Swift.UInt8 {
  case off = 0
  case uploadWithOutSerialnumber
  @available(*, deprecated, renamed: "uploadWithOutSerialnumber", message: "use uploadWithOutSerialnumber instand of it")
  case uploadSerialnumber
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCRealTimeDataType : Swift.UInt8 {
  case step = 0
  case heartRate
  case bloodOxygen
  case bloodPressure
  case hrv
  case respirationRate
  case sportMode
  case combinedData
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCWaveDataType : Swift.UInt8 {
  case ppg = 0
  case ecg
  case multiAxisSensor
  case ambientLight
  case multiChannelPPG
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCQuerySampleRateType : Swift.UInt8 {
  case ppg = 0
  case ecg
  case multiAxisSensor
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCWeatherPeriodType : Swift.UInt8 {
  case today = 0
  case tomorrow
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCWeatherCodeType : Swift.UInt8 {
  case unknow = 0
  case sunny
  case cloudy
  case wind
  case rain
  case snow
  case foggy
  case sunnyCustom
  case cloudyCustom
  case thunderShower
  case lightRain
  case moderateRain
  case heavyRain
  case rainSnow
  case lightSnow
  case moderateSnow
  case heavySnow
  case floatingDust
  case fog
  case haze
  case windCustom
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCWeatherMoonType : Swift.UInt8 {
  case newMoon = 0
  case waningMoon
  case theLastQuarterMoon
  case lowerConvexNoon
  case fullMoon
  case upperConvexMoon
  case firstQuarterMoon
  case crescentMoon
  case unknown
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCHealthParametersState : Swift.UInt8 {
  case off = 0
  case effect = 1
  case invalid = 0xFF
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCHealthState : Swift.UInt8 {
  case unknow = 0
  case excellent
  case good
  case general
  case poor
  case sick
  case invalid = 0xFF
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceSystemOperator : Swift.UInt8 {
  case shutDown = 1
  case transportation
  case resetRestart
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCPersonalInfoType : Swift.UInt8 {
  case insurance = 0
  case vip
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCAppControlMeasureMode : Swift.UInt8 {
  case off = 0
  case single
  case monitor
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCBodyTemperatureQRCodeColor : Swift.UInt8 {
  case green = 0
  case red
  case orange
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceSenserSaveDataType : Swift.UInt8 {
  case ppg = 0
  case acceleration
  case ecg
  case temperatureHumidity
  case ambientLight
  case bodyTemperature
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCWarningInformationType : Swift.UInt8 {
  case warnSelf = 0
  case warnOthers
  case highRisk
  case nonHighRisk
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCSyncAddressBookState : Swift.UInt8 {
  case end = 0
  case synchronizing
  case start
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCMeasurementDataType : Swift.UInt8 {
  case heartRate = 0
  case bloodPressure
  case bloodOxygen
  case respirationRate
  case hrv
  case bloodGlucose
  case temperature
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCAppControlHealthDataMeasureType : Swift.UInt8 {
  case off = 0
  case single
  case monitor
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCAppControlMeasureHealthDataType : Swift.UInt8 {
  case heartRate = 0
  case bloodPressure
  case bloodOxygen
  case respirationRate
  case bodyTemperature
  case bloodGlucose
  case uricAcid
  case bloodKetone
  case eda
  case clinicalMode = 0x70
  case unknow = 0xFF
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCAppControlMeasureHealthDataResult : Swift.UInt8 {
  case exit = 0
  case success
  case fail
  case enter
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCBloodGlucoseCalibrationaMode : Swift.UInt8 {
  case fasting = 0
  case afterBreakfast
  case beforeLunch
  case afterLunch
  case beforeDinner
  case afterDinner
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCLocationInformationType : Swift.UInt8 {
  case longitudeAndLatitude = 0
  case detailedAddress = 1
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCBusinessCardType : Swift.UInt8 {
  case wechat = 0
  case qq
  case facebook
  case twitter
  case whatsapp
  case instagram
  case snCode = 0xF0
  case staticCode = 0xF1
  case dynamicCode = 0xF2
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceControlInvasiveMeasurementState : Swift.UInt8 {
  case plugIn = 0
  case plugOut
  case testStripIn
  case testStripOut
  case measuredValue
  case eepromReadError
  case eepromWriteError
  case temperatureOutOfBounds
  case measurementInterruption
  case parameterError
  case communicationError
  case wrongTestStrip
  case measurementCountdown
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDevcieSpecifyMessageType : Swift.UInt8 {
  case ok = 0
  case warning
  case alert
  case message
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCWatchFaceBreakCountInfo : ObjectiveC.NSObject {
  @objc public var dials: [YCProductSDK.YCWatchFaceInfo] {
    get
  }
  @objc public var limitCount: Swift.Int {
    get
  }
  @objc public var localCount: Swift.Int {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCWatchFaceInfo : ObjectiveC.NSObject {
  @objc public var dialID: Swift.UInt32 {
    @objc get
  }
  @objc public var blockCount: Swift.UInt16 {
    @objc get
  }
  @objc public var isSupportDelete: Swift.Bool {
    get
  }
  @objc public var version: Swift.UInt16 {
    get
  }
  @objc public var isCustomDial: Swift.Bool {
    get
  }
  @objc public var isShowing: Swift.Bool {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCWatchFaceDataBmpInfo : ObjectiveC.NSObject {
  @objc public var width: Swift.Int {
    get
  }
  @objc public var height: Swift.Int {
    get
  }
  @objc public var size: Swift.Int {
    get
  }
  @objc public var radius: Swift.Int {
    get
  }
  @objc public var thumbnailWidth: Swift.Int {
    get
  }
  @objc public var thumbnailHeight: Swift.Int {
    get
  }
  @objc public var thumbnailSize: Swift.Int {
    get
  }
  @objc public var thumbnailRadius: Swift.Int {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDownloadProgressInfo : ObjectiveC.NSObject {
  @objc public var progress: Swift.Float {
    get
  }
  @objc public var downloaded: Swift.Int {
    @objc get
  }
  @objc public var total: Swift.Int {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCProductLogLevel : Swift.Int {
  case off
  case normal
  case error
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum YCWeekDay : Swift.UInt8 {
  case monday = 0
  case tuesday
  case wednesday
  case thursday
  case friday
  case saturday
  case sunday
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCSettingGoalType : Swift.UInt8 {
  case step = 0
  case calories
  case distance
  case sleep
  case sportTime
  case effectiveSteps
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceGender : Swift.UInt8 {
  case male = 0
  case female
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceDistanceType : Swift.UInt8 {
  case km = 0
  case mile
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceWeightType : Swift.UInt8 {
  case kg = 0
  case lb
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceTemperatureType : Swift.UInt8 {
  case celsius = 0
  case fahrenheit
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceBloodGlucoseType : Swift.UInt8 {
  case millimolePerLiter = 0
  case milligramsPerDeciliter
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceUricAcidType : Swift.UInt8 {
  case micromolePerLiter = 0
  case milligramsPerDeciliter
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceTimeType : Swift.UInt8 {
  case hour24 = 0
  case hour12
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceWeekRepeat : Swift.UInt8 {
  case monday = 1
  case tuesday = 2
  case wednesday = 4
  case thursday = 8
  case friday = 16
  case saturday = 32
  case sunday = 64
  case enable = 128
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceAntiLostType : Swift.UInt8 {
  case off = 0
  case closeRange
  case middleDistance
  case longDistance
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceWearingPositionType : Swift.UInt8 {
  case left
  case right
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCPhoneSystemType : Swift.UInt8 {
  case android = 0
  case ios
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceInfoPushType : Swift.UInt {
  case call
  case sms
  case email
  case wechat
  case qq
  case weibo
  case facebook
  case twitter
  case messenger
  case whatsAPP
  case linkedIn
  case instagram
  case skype
  case line
  case snapchat
  case telegram
  case other
  case viber
  case zoom
  case tiktok
  case kakaoTalk
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc public enum YCDeviceLanguageType : Swift.UInt8 {
  case english = 0x00
  case chineseSimplified = 0x01
  case russian = 0x02
  case german = 0x03
  case french = 0x04
  case japanese = 0x05
  case spanish = 0x06
  case italian = 0x07
  case portuguese = 0x08
  case korean = 0x09
  case poland = 0x0A
  case malay = 0x0B
  case chineseTradition = 0x0C
  case thai = 0x0D
  case vietnamese = 0x0F
  case hungarian = 0x10
  case arabic = 0x1A
  case greek = 0x1B
  case malaysian = 0x1C
  case hebrew = 0x1D
  case finnish = 0x1E
  case czech = 0x1F
  case croatian = 0x20
  case persian = 0x24
  case ukrainian = 0x27
  case turkish = 0x28
  case danish = 0x2B
  case swedish = 0x2C
  case norwegian = 0x2D
  case romanian = 0x32
  case slovak = 0x34
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceDisplayBrightnessLevel : Swift.UInt8 {
  case low = 0
  case middle
  case high
  case automatic
  case lower
  case higher
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceSkinColorLevel : Swift.UInt8 {
  case white = 0
  case whiteYellow = 1
  case yellow = 2
  case brown = 3
  case darkBrown = 4
  case black = 5
  case other = 7
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceBloodPressureLevel : Swift.UInt8 {
  case low = 0
  case normal
  case slightlyHigh
  case moderatelyHigh
  case severeHigh
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceDataCollectionType : Swift.UInt8 {
  case ppg = 0
  case acceleration
  case ecg
  case temperatureHumidity
  case ambientLight
  case bodyTemperature
  case heartRate
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCSettingTemperatureModeType : Swift.UInt8 {
  case bodyTemperature = 0
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@available(*, deprecated, message: "use YCDeviceScreenTimeInterval instand of it")
@objc public enum YCDeviceBreathScreenInterval : Swift.UInt8 {
  case five = 0
  case ten
  case fifteen
  case thirty
  case twenty
  case twentyFive
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceScreenTimeInterval : Swift.UInt8 {
  case fiveSeconds = 0
  case tenSeconds
  case fifteenSeconds
  case thirtySeconds
  case twentySeconds
  case twentyFiveSeconds
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceWorkModeType : Swift.UInt8 {
  case normal = 0
  case care
  case powerSaving
  case custom
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceMCUType : Swift.UInt8 {
  case nrf52832 = 0
  case rtk8762c
  case rtk8762d
  case jl701n
  case jl632n
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceReminderSettingState : Swift.UInt8 {
  case off = 0
  case on
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceExerciseHeartRateType : Swift.UInt8 {
  case retreat = 0
  case casualwarmup
  case cardiorespiratoryStrengthening
  case reduceFatShape
  case sportsLimit
  case emptyState
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceMotorVibrationType : Swift.UInt8 {
  case alarm = 0
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceAlarmType : Swift.UInt8 {
  case wakeUp = 0
  case sleep
  case exercise
  case medicine
  case appointment
  case party
  case meeting
  case custom
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceAlarmOperatoreType : Swift.UInt8 {
  case query = 0
  case add
  case delete
  case modify
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceAlarmInfo : ObjectiveC.NSObject {
  @objc public var limitCount: Swift.UInt8 {
    get
  }
  @objc public var alarmType: YCProductSDK.YCDeviceAlarmType {
    get
  }
  @objc public var hour: Swift.UInt8 {
    get
  }
  @objc public var minute: Swift.UInt8 {
    get
  }
  public var `repeat`: Swift.Set<YCProductSDK.YCDeviceWeekRepeat> {
    get
  }
  @objc public var repeatArray: [Foundation.NSNumber] {
    @objc get
  }
  @objc public var snoozeTime: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceEventInfo : ObjectiveC.NSObject {
  @objc public var eventID: Swift.UInt8 {
    get
  }
  @objc public var isEnable: Swift.Bool {
    get
  }
  @objc public var hour: Swift.UInt8 {
    get
  }
  @objc public var minute: Swift.UInt8 {
    get
  }
  public var `repeat`: Swift.Set<YCProductSDK.YCDeviceWeekRepeat> {
    get
  }
  @objc public var repeatArray: [Foundation.NSNumber] {
    @objc get
  }
  @objc public var interval: YCProductSDK.YCDeviceEventInterval {
    get
  }
  @objc public var name: Swift.String {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCDeviceEventInterval : Swift.UInt8 {
  case none = 0
  case ten = 10
  case twenty = 20
  case thirty = 30
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDevcieScheduleEventType : Swift.UInt8 {
  case getUp = 0
  case breakfast
  case baskInTheSun
  case lunch
  case lunchBreak
  case sports
  case dinner
  case sleep
  case customize
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceScheduleInfo : ObjectiveC.NSObject {
  @objc public var scheduleIndex: Swift.UInt8 {
    get
  }
  @objc public var scheduleEnable: Swift.Bool {
    get
  }
  @objc public var eventIndex: Swift.UInt8 {
    get
  }
  @objc public var eventEnable: Swift.Bool {
    get
  }
  @objc public var eventTime: Swift.UInt32 {
    get
  }
  @objc public var eventType: YCProductSDK.YCDevcieScheduleEventType {
    get
  }
  @objc public var eventName: Swift.String {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCTemperatureType : Swift.UInt8 {
  case bodyTemperature
  case ambientTemperature
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCMotorVibrationStrengthLevel : Swift.UInt8 {
  case none = 0
  case level1
  case level2
  case level3
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCPeriodicReminderType : Swift.UInt8 {
  case drinkWater = 0
  case takeMedicine = 1
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceAlipayState : Swift.UInt8 {
  case notActive = 0
  case activated
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceScreenType : Swift.Int {
  case round = 0
  case square
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.Int)
  public typealias RawValue = Swift.Int
  public var rawValue: Swift.Int {
    get
  }
}
@objc public enum YCDeviceFaceTimePosition : Swift.UInt8 {
  case top = 1
  case bottom
  case left
  case right
  case leftTop
  case rightTop
  case leftBottom
  case rightBottom
  case center
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceDisplayParametersInfo : ObjectiveC.NSObject {
  @objc public var screenType: YCProductSDK.YCDeviceScreenType
  @objc public var widthPixels: Swift.UInt16
  @objc public var heightPixels: Swift.UInt16
  @objc public var filletRadiusPixels: Swift.UInt16
  @objc public var thumbnailWidthPixels: Swift.UInt16
  @objc public var thumbnailHeightPixels: Swift.UInt16
  @objc public var thumbnailRadiusPixels: Swift.UInt16
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCDeviceControlType : Swift.UInt8 {
  case findPhone = 0
  case antiLost = 0x01
  case photo = 0x03
  case sos = 0x05
  case allowConnection = 0x07
  case sportMode = 0x08
  case reset = 0x0A
  case stopRealTimeECGMeasurement = 0x0B
  case sportModeControl = 0x0C
  case switchWatchFace = 0x0D
  case healthDataMeasurementResult = 0x0E
  case reportWarningValue = 0x0F
  case accurateBloodPressureMeasurementStop = 0x10
  case deviceFirmwareDownloadState = 0x11
  case ppi = 0x12
  case invasiveMeasurementState = 0x13
  case alipayActivationState = 0x14
  case dynamicCodeState = 0x15
  @available(*, deprecated, message: "use toString instand of it")
  public var string: Swift.String {
    get
  }
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceControlState : Swift.UInt8 {
  case stop = 0
  case start
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceControlPhotoState : Swift.UInt8 {
  case exit = 0
  case enter
  case photo
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceControlAllowConnectionState : Swift.UInt8 {
  case agree = 0
  case refuse
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc public enum YCDeviceControlDeviceSportState : Swift.UInt8 {
  case running = 0
  case freeMove
  case stop
  case start
  case pause
  case `continue`
  case unknow = 0xFF
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceControlSportModeControlInfo : ObjectiveC.NSObject {
  @objc public var state: YCProductSDK.YCDeviceSportState {
    get
  }
  @objc public var sportType: YCProductSDK.YCDeviceSportType {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceControlMeasureHealthDataResultInfo : ObjectiveC.NSObject {
  @objc public var state: YCProductSDK.YCAppControlMeasureHealthDataResult {
    get
  }
  @objc public var dataType: YCProductSDK.YCAppControlMeasureHealthDataType {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceControlReportWarningValueInfo : ObjectiveC.NSObject {
  @objc public var dataType: YCProductSDK.YCAppControlMeasureHealthDataType {
    get
  }
  @objc public var values: [Swift.Int] {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceControlAccurateBloodPressureMeasurementEndInfo : ObjectiveC.NSObject {
  @objc public var isSuccess: Swift.Bool {
    get
  }
  @objc public var measureSystolicBloodPressure: Swift.UInt8 {
    get
  }
  @objc public var measureDiastolicBloodPressure: Swift.UInt8 {
    get
  }
  @objc public var measureHeartRate: Swift.UInt8 {
    get
  }
  @objc public var inputSystolicBloodPressure: Swift.UInt8 {
    get
  }
  @objc public var inputDiastolicBloodPressure: Swift.UInt8 {
    get
  }
  @objc public var inputHeartRate: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceControlEmbeddedPeripheralFirmwareUpgradeStateInfo : ObjectiveC.NSObject {
  @objc public var firmwareType: YCProductSDK.YCEmbeddedPeripheralFirmwareType {
    get
  }
  @objc public var isSuccess: Swift.Bool {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceControlInvasiveMeasurementStateInfo : ObjectiveC.NSObject {
  @objc public var dataType: YCProductSDK.YCAppControlMeasureHealthDataType {
    get
  }
  @objc public var state: YCProductSDK.YCDeviceControlInvasiveMeasurementState {
    get
  }
  @objc public var values: [Swift.Int] {
    get
  }
  @objc public var measredValue: Any {
    @objc get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCDeviceControlDynamicCodeState : Swift.UInt8 {
  case requestCode = 0
  case end = 1
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
extension YCProductSDK.YCProduct {
  @objc public static func setDeviceTime(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, year: Swift.UInt16, month: Swift.UInt8, day: Swift.UInt8, hour: Swift.UInt8, minute: Swift.UInt8, second: Swift.UInt8, weekDay: YCProductSDK.YCWeekDay, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSyncPhoneTime(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func isHour12Format() -> Swift.Bool
  @objc public static func setDeviceStepGoal(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, step: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceCaloriesGoal(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, calories: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceDistanceGoal(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, distance: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSleepGoal(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, hour: Swift.UInt8, minute: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSportTimeGoal(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, hour: Swift.UInt8, minute: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceEffectiveStepsGoal(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, effectiveSteps: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceUserInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, height: Swift.UInt8, weight: Swift.UInt8, gender: YCProductSDK.YCDeviceGender, age: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceUnit(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, distance: YCProductSDK.YCDeviceDistanceType = .km, weight: YCProductSDK.YCDeviceWeightType = .kg, temperature: YCProductSDK.YCDeviceTemperatureType = .celsius, timeFormat: YCProductSDK.YCDeviceTimeType = .hour24, bloodGlucoseOrBloodFat: YCProductSDK.YCDeviceBloodGlucoseType = .millimolePerLiter, uricAcid: YCProductSDK.YCDeviceUricAcidType = .micromolePerLiter, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSedentary(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, startHour1: Swift.UInt8, startMinute1: Swift.UInt8, endHour1: Swift.UInt8, endMinute1: Swift.UInt8, startHour2: Swift.UInt8, startMinute2: Swift.UInt8, endHour2: Swift.UInt8, endMinute2: Swift.UInt8, interval: Swift.UInt8, repeat: Foundation.NSSet, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func setDeviceSedentary(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, startHour1: Swift.UInt8, startMinute1: Swift.UInt8, endHour1: Swift.UInt8, endMinute1: Swift.UInt8, startHour2: Swift.UInt8, startMinute2: Swift.UInt8, endHour2: Swift.UInt8, endMinute2: Swift.UInt8, interval: Swift.UInt8, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceAntiLost(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, antiLostType: YCProductSDK.YCDeviceAntiLostType = .middleDistance, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceAntiLost(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, antiLostType: YCProductSDK.YCDeviceAntiLostType = .middleDistance, rssi: Swift.Int8, delay: Swift.UInt8, supportDisconnectDelay: Swift.Bool, repeat: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceWearingPosition(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, wearingPosition: YCProductSDK.YCDeviceWearingPositionType = .left, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setPhoneSystemInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, phoneSystemType: YCProductSDK.YCPhoneSystemType = .ios, systemVersion: Swift.String = UIDevice.current.systemVersion, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceInfoPush(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, infoPushType: Foundation.NSSet, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func setDeviceInfoPush(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, infoPushType: Swift.Set<YCProductSDK.YCDeviceInfoPushType>, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceHeartRateAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, maxHeartRate: Swift.UInt8, minHeartRate: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceHealthMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc @available(*, deprecated, renamed: "setDeviceHealthMonitoringMode", message: "use func setDeviceHealthMonitoringMode instand of it")
  public static func setDeviceHeartRateMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceFindPhone(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceReset(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceNotDisturb(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, startHour: Swift.UInt8, startMinute: Swift.UInt8, endHour: Swift.UInt8, endMinute: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceAncsEnable(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceLanguage(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, language: YCProductSDK.YCDeviceLanguageType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceWristBrightScreen(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceDisplayBrightness(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, level: YCProductSDK.YCDeviceDisplayBrightnessLevel, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSkinColor(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, level: YCProductSDK.YCDeviceSkinColorLevel, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceBloodPressureRange(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, level: YCProductSDK.YCDeviceBloodPressureLevel, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceName(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, name: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSensorSamplingRate(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, ppg: Swift.UInt16, ecg: Swift.UInt16, gSensor: Swift.UInt16, tempeatureSensor: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceTheme(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, index: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSleepReminder(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, hour: Swift.UInt8, minute: Swift.UInt8, repeat: Foundation.NSSet, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func setDeviceSleepReminder(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, hour: Swift.UInt8, minute: Swift.UInt8, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceDataCollection(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, dataType: YCProductSDK.YCDeviceDataCollectionType, acquisitionTime: Swift.UInt8, acquisitionInterval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc @available(*, deprecated, message: "No need to use this method")
  public static func setDeviceBloodPressureMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceTemperatureMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCSettingTemperatureModeType = .bodyTemperature, temperature: Swift.Int8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceOwnerInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, name: Swift.String, className: Swift.String, identifier: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceTemperatureAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, temperatureType: YCProductSDK.YCTemperatureType = .bodyTemperature, highTemperatureIntegerValue: Swift.UInt8, highTemperatureDecimalValue: Swift.UInt8, lowTemperatureIntegerValue: Swift.Int8, lowTemperatureDecimalValue: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc @available(*, deprecated, renamed: "setDeviceHealthMonitoringMode", message: "use func setDeviceHealthMonitoringMode instand of it")
  public static func setDeviceTemperatureMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc @available(*, deprecated, renamed: "setDeviceScreenTime", message: "use setDeviceScreenTime instand of it")
  public static func setDeviceBreathScreen(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, interval: YCProductSDK.YCDeviceBreathScreenInterval, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceScreenTime(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, interval: YCProductSDK.YCDeviceScreenTimeInterval, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceAmbientLightMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceWorkMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCDeviceWorkModeType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceAccidentMonitoring(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceReminderType(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, remindType: YCProductSDK.YCDeviceRemindType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceBloodOxygenMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceTemperatureHumidityMonitoringMode(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, interval: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDevicePedometerTime(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, time: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceUploadReminder(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, threshold: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceBroadcastInterval(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, interval: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceTransmitPower(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, power: Swift.Int8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceExerciseHeartRateZone(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, zoneType: YCProductSDK.YCDeviceExerciseHeartRateType, minimumHeartRate: Swift.UInt8, maximumHeartRate: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceWorkModeDataCollection(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCDeviceWorkModeType, dataType: YCProductSDK.YCDeviceDataCollectionType, acquisitionTime: Swift.UInt16, acquisitionInterval: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceInsuranceInterfaceDisplay(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceMacAddress(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, macaddress: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceSOSEnable(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceBloodPressureAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, maximumSystolicBloodPressure: Swift.UInt8, maximumDiastolicBloodPressure: Swift.UInt8, minimumSystolicBloodPressure: Swift.UInt8, minimumDiastolicBloodPressure: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceBloodOxygenAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, minimum: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceMotorVibrationTime(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, mode: YCProductSDK.YCDeviceMotorVibrationType = .alarm, time: Swift.UInt32, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceMotorVibrationStrength(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, level: YCProductSDK.YCMotorVibrationStrengthLevel, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceRespirationRateAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, maximum: Swift.UInt8, minimum: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceBloodGlucoseAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, hyperglycemia: Swift.Double, hypoglycemia: Swift.Double, severeHyperglycemia: Swift.Double, severeHypoglycemia: Swift.Double, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setAutoMeasure(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, measureType: YCProductSDK.YCAppControlMeasureHealthDataType, time: Swift.Int, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setJLCustomDialParameter(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, timeLocation: YCProductSDK.YCDeviceFaceTimePosition = .center, fontColor: Swift.UInt16, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDevicePeriodicReminderTask(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, periodicReminderType: YCProductSDK.YCPeriodicReminderType, startHour: Swift.UInt8, startMinute: Swift.UInt8, endHour: Swift.UInt8, endMinute: Swift.UInt8, repeat: Foundation.NSSet, interval: Swift.UInt8, content: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func setDevicePeriodicReminderTask(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, periodicReminderType: YCProductSDK.YCPeriodicReminderType, startHour: Swift.UInt8, startMinute: Swift.UInt8, endHour: Swift.UInt8, endMinute: Swift.UInt8, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, interval: Swift.UInt8 = 0, content: Swift.String = "", completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func queryDeviceAlarmInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func addDeviceAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, alarmType: YCProductSDK.YCDeviceAlarmType, hour: Swift.UInt8, minute: Swift.UInt8, repeat: Foundation.NSSet, snoozeTime: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func addDeviceAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, alarmType: YCProductSDK.YCDeviceAlarmType, hour: Swift.UInt8, minute: Swift.UInt8, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, snoozeTime: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteDeviceAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, hour: Swift.UInt8, minute: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func modifyDeviceAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, oldHour: Swift.UInt8, oldMinute: Swift.UInt8, hour: Swift.UInt8, minute: Swift.UInt8, alarmType: YCProductSDK.YCDeviceAlarmType, repeat: Foundation.NSSet, snoozeTime: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func modifyDeviceAlarm(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, oldHour: Swift.UInt8, oldMinute: Swift.UInt8, hour: Swift.UInt8, minute: Swift.UInt8, alarmType: YCProductSDK.YCDeviceAlarmType, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, snoozeTime: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceEventEnable(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func addDeviceEvent(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, name: Swift.String, isEnable: Swift.Bool, hour: Swift.UInt8, minute: Swift.UInt8, interval: YCProductSDK.YCDeviceEventInterval, repeat: Foundation.NSSet, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func addDeviceEvent(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, name: Swift.String, isEnable: Swift.Bool, hour: Swift.UInt8, minute: Swift.UInt8, interval: YCProductSDK.YCDeviceEventInterval, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteDeviceEvent(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, eventID: Swift.UInt8, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func modifyDeviceEvent(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, name: Swift.String, eventID: Swift.UInt8, isEnable: Swift.Bool, hour: Swift.UInt8, minute: Swift.UInt8, interval: YCProductSDK.YCDeviceEventInterval, repeat: Foundation.NSSet, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  public static func modifyDeviceEvent(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, name: Swift.String, eventID: Swift.UInt8, isEnable: Swift.Bool, hour: Swift.UInt8, minute: Swift.UInt8, interval: YCProductSDK.YCDeviceEventInterval, repeat: Swift.Set<YCProductSDK.YCDeviceWeekRepeat>, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceEventnfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func setDeviceScheduleEnable(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, isEnable: Swift.Bool, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func addDeviceSchedule(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, scheduleEnable: Swift.Bool, scheduleIndex: Swift.UInt8, eventIndex: Swift.UInt8, eventType: YCProductSDK.YCDevcieScheduleEventType, eventEnable: Swift.Bool, eventTime: Swift.Int, eventName: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func modifyDeviceSchedule(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, scheduleEnable: Swift.Bool, scheduleIndex: Swift.UInt8, eventIndex: Swift.UInt8, eventType: YCProductSDK.YCDevcieScheduleEventType, eventEnable: Swift.Bool, eventTime: Swift.Int, eventName: Swift.String, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func deleteDeviceSchedule(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, scheduleIndex: Swift.UInt8, eventIndex: Swift.UInt8, eventType: YCProductSDK.YCDevcieScheduleEventType, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
  @objc public static func queryDeviceScheduleInfo(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ response: Any?) -> ())?)
}
extension YCProductSDK.YCProduct {
  @objc public static func getDeviceByUUID(_ uuidString: Swift.String) -> CoreBluetooth.CBPeripheral?
  @objc public static func connectDevice(_ peripheral: CoreBluetooth.CBPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ error: Foundation.NSError?) -> ())?)
  @objc public static func disconnectDevice(_ peripheral: CoreBluetooth.CBPeripheral? = YCProduct.shared.currentPeripheral, completion: ((_ state: YCProductSDK.YCProductState, _ error: Foundation.NSError?) -> ())?)
  @objc public static func scanningDevice(delayTime: Foundation.TimeInterval = 3.0, completion: ((_ devices: [CoreBluetooth.CBPeripheral], _ error: Foundation.NSError?) -> ())?)
  @objc public static func stopSearchDevice()
}
@objc public enum YCDeviceCheckType : Swift.UInt16 {
  case auto = 0
  case gSensor
  case ppg
  case ecg
  case temperature
  public init?(rawValue: Swift.UInt16)
  public typealias RawValue = Swift.UInt16
  public var rawValue: Swift.UInt16 {
    get
  }
}
@objc public enum YCDeviceDetectionErrorType : Swift.UInt {
  case gSensor = 1
  case ppg = 2
  case ppgG = 4
  case ppgR = 8
  case ppgIR = 16
  case ecg = 32
  case temp = 64
  case lcd = 128
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt)
  public typealias RawValue = Swift.UInt
  public var rawValue: Swift.UInt {
    get
  }
}
@objc public enum YCDeviceBatterystate : Swift.UInt8 {
  case normal = 0
  case low
  case charging
  case full
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceVersionInfo : ObjectiveC.NSObject {
  @objc public var version: Swift.String {
    @objc get
  }
  @objc public var majorVersion: Swift.UInt8 {
    get
  }
  @objc public var subVersion: Swift.UInt8 {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCDeviceType : Swift.UInt8 {
  case watch = 0
  case ring
  case touchRing
  case bodyTemperatureSticker
  case ecgStickers
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceBasicInfo : ObjectiveC.NSObject {
  @objc public var deviceID: Swift.UInt16 {
    get
  }
  @objc public var mcuFirmware: YCProductSDK.YCDeviceVersionInfo {
    get
  }
  @objc public var batterystatus: YCProductSDK.YCDeviceBatterystate {
    get
  }
  @objc public var batteryPower: Swift.UInt8 {
    get
  }
  @objc public var isBind: Swift.Bool {
    get
  }
  @objc public var needSync: Swift.Bool {
    get
  }
  @objc public var innerProtocol: YCProductSDK.YCDeviceVersionInfo {
    get
  }
  @objc public var bloodPressureFirmware: YCProductSDK.YCDeviceVersionInfo {
    get
  }
  @objc public var touchPanelFirmware: YCProductSDK.YCDeviceVersionInfo {
    get
  }
  @objc public var bloodGlucoseFirmware: YCProductSDK.YCDeviceVersionInfo {
    get
  }
  @objc public var uiInfo: YCProductSDK.YCDeviceVersionInfo {
    get
  }
  @objc public var deviceType: YCProductSDK.YCDeviceType {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceCurrentHeartRate : ObjectiveC.NSObject {
  @objc public var isMeasuring: Swift.Bool {
    get
  }
  @objc public var heartRate: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceCurrentBloodPressure : ObjectiveC.NSObject {
  @objc public var isMeasuring: Swift.Bool {
    get
  }
  @objc public var systolicBloodPressure: Swift.UInt8 {
    get
  }
  @objc public var diastolicBloodPressure: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceTheme : ObjectiveC.NSObject {
  @objc public var themeCount: Swift.Int {
    get
  }
  @objc public var themeIndex: Swift.Int {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc public enum YCDeviceElectrodePosition : Swift.UInt8 {
  case right = 0
  case bottom
  case bothSides
  case fullEncirclement
  public var toString: Swift.String {
    get
  }
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceScreenInfo : ObjectiveC.NSObject {
  @objc public var screenWidth: Swift.UInt16 {
    get
  }
  @objc public var screenHeight: Swift.UInt16 {
    get
  }
  @objc public var fontWidth: Swift.UInt16 {
    get
  }
  @objc public var fontHeight: Swift.UInt16 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@_inheritsConvenienceInitializers @objc public class YCDeviceCurrentExercise : ObjectiveC.NSObject {
  public var step: Swift.Int {
    get
  }
  public var calories: Swift.Int {
    get
  }
  public var distance: Swift.Int {
    get
  }
  public var toString: Swift.String {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@_inheritsConvenienceInitializers @objc public class YCDeviceHistorySummary : ObjectiveC.NSObject {
  public var sleepCount: Swift.Int {
    get
  }
  public var sleepTime: Swift.Int {
    get
  }
  public var heartRateCount: Swift.Int {
    get
  }
  public var sportCount: Swift.Int {
    get
  }
  public var bloodPressureCount: Swift.Int {
    get
  }
  public var bloodOxygenCount: Swift.Int {
    get
  }
  public var temperatureHumidityCount: Swift.Int {
    get
  }
  public var bodyTemperatureCount: Swift.Int {
    get
  }
  public var ambientLightCount: Swift.Int {
    get
  }
  public var toString: Swift.String {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@_inheritsConvenienceInitializers @objc public class YCDeviceScreenDisplayInfo : ObjectiveC.NSObject {
  public var brightnessLevel: YCProductSDK.YCDeviceDisplayBrightnessLevel {
    get
  }
  public var restScreenTime: YCProductSDK.YCDeviceBreathScreenInterval {
    get
  }
  public var language: YCProductSDK.YCDeviceLanguageType {
    get
  }
  public var workmode: YCProductSDK.YCDeviceWorkModeType {
    get
  }
  public var toString: Swift.String {
    get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceBloodOxygen : ObjectiveC.NSObject {
  @objc public var isMeasuring: Swift.Bool {
    get
  }
  @objc public var bloodOxygen: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceAmbientLight : ObjectiveC.NSObject {
  @objc public var isMeasuring: Swift.Bool {
    get
  }
  @objc public var ambientLight: Swift.UInt16 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceTemperatureHumidity : ObjectiveC.NSObject {
  @objc public var isMeasuring: Swift.Bool {
    get
  }
  @objc public var temperature: Swift.Double {
    get
  }
  @objc public var humidity: Swift.Double {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@objc @_inheritsConvenienceInitializers @objcMembers public class YCDeviceSensorSampleInfo : ObjectiveC.NSObject {
  @objc public var isOn: Swift.Bool {
    get
  }
  @objc public var acquisitionTime: Swift.UInt16 {
    get
  }
  @objc public var acquisitionInterval: Swift.UInt16 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc override dynamic public init()
  @objc deinit
}
@_hasMissingDesignatedInitializers @objcMembers public class YCDeviceUploadReminderInfo {
  @objc public var isOn: Swift.Bool {
    get
  }
  @objc public var threshold: Swift.UInt8 {
    get
  }
  @objc public var toString: Swift.String {
    @objc get
  }
  @objc deinit
}
@objc public enum YCDeviceRemindType : Swift.UInt8 {
  case deviceDisconnected = 0
  case sportsCompliance
  public init?(rawValue: Swift.UInt8)
  public typealias RawValue = Swift.UInt8
  public var rawValue: Swift.UInt8 {
    get
  }
}
extension YCProductSDK.YCCustomDeviceDataType : Swift.Equatable {}
extension YCProductSDK.YCCustomDeviceDataType : Swift.Hashable {}
extension YCProductSDK.YCCustomDeviceDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCCustomCGMDeviceInfoType : Swift.Equatable {}
extension YCProductSDK.YCCustomCGMDeviceInfoType : Swift.Hashable {}
extension YCProductSDK.YCCustomCGMDeviceInfoType : Swift.RawRepresentable {}
extension YCProductSDK.YCDevicePhysiotherapyOperationType : Swift.Equatable {}
extension YCProductSDK.YCDevicePhysiotherapyOperationType : Swift.Hashable {}
extension YCProductSDK.YCDevicePhysiotherapyOperationType : Swift.RawRepresentable {}
extension YCProductSDK.YCDevicePhysiotherapyStartupType : Swift.Equatable {}
extension YCProductSDK.YCDevicePhysiotherapyStartupType : Swift.Hashable {}
extension YCProductSDK.YCDevicePhysiotherapyStartupType : Swift.RawRepresentable {}
extension YCProductSDK.YCIntelligentFunctionType : Swift.Equatable {}
extension YCProductSDK.YCIntelligentFunctionType : Swift.Hashable {}
extension YCProductSDK.YCIntelligentFunctionType : Swift.RawRepresentable {}
extension YCProductSDK.YCCollectDataType : Swift.Equatable {}
extension YCProductSDK.YCCollectDataType : Swift.Hashable {}
extension YCProductSDK.YCCollectDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCRecordingFileType : Swift.Equatable {}
extension YCProductSDK.YCRecordingFileType : Swift.Hashable {}
extension YCProductSDK.YCRecordingFileType : Swift.RawRepresentable {}
extension YCProductSDK.YCReceivedRealTimeDataType : Swift.Equatable {}
extension YCProductSDK.YCReceivedRealTimeDataType : Swift.Hashable {}
extension YCProductSDK.YCReceivedRealTimeDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCMultiChannelPPGCompositeType : Swift.Equatable {}
extension YCProductSDK.YCMultiChannelPPGCompositeType : Swift.Hashable {}
extension YCProductSDK.YCMultiChannelPPGCompositeType : Swift.RawRepresentable {}
extension YCProductSDK.YCQueryHealthDataType : Swift.Equatable {}
extension YCProductSDK.YCQueryHealthDataType : Swift.Hashable {}
extension YCProductSDK.YCQueryHealthDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeleteHealthDataType : Swift.Equatable {}
extension YCProductSDK.YCDeleteHealthDataType : Swift.Hashable {}
extension YCProductSDK.YCDeleteHealthDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthDataSleepType : Swift.Equatable {}
extension YCProductSDK.YCHealthDataSleepType : Swift.Hashable {}
extension YCProductSDK.YCHealthDataSleepType : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthDataMeasureMode : Swift.Equatable {}
extension YCProductSDK.YCHealthDataMeasureMode : Swift.Hashable {}
extension YCProductSDK.YCHealthDataMeasureMode : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthDataBloodPressureMode : Swift.Equatable {}
extension YCProductSDK.YCHealthDataBloodPressureMode : Swift.Hashable {}
extension YCProductSDK.YCHealthDataBloodPressureMode : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthDataWearState : Swift.Equatable {}
extension YCProductSDK.YCHealthDataWearState : Swift.Hashable {}
extension YCProductSDK.YCHealthDataWearState : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthDataSportModeStartMethod : Swift.Equatable {}
extension YCProductSDK.YCHealthDataSportModeStartMethod : Swift.Hashable {}
extension YCProductSDK.YCHealthDataSportModeStartMethod : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode : Swift.Equatable {}
extension YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode : Swift.Hashable {}
extension YCProductSDK.YCHealthDataInvasiveComprehensiveDataMode : Swift.RawRepresentable {}
extension YCProductSDK.YCEmbeddedPeripheralFirmwareType : Swift.Equatable {}
extension YCProductSDK.YCEmbeddedPeripheralFirmwareType : Swift.Hashable {}
extension YCProductSDK.YCEmbeddedPeripheralFirmwareType : Swift.RawRepresentable {}
extension YCProductSDK.YCECGResultType : Swift.Equatable {}
extension YCProductSDK.YCECGResultType : Swift.Hashable {}
extension YCProductSDK.YCECGResultType : Swift.RawRepresentable {}
extension YCProductSDK.YCECGHeavyLoadLevel : Swift.Equatable {}
extension YCProductSDK.YCECGHeavyLoadLevel : Swift.Hashable {}
extension YCProductSDK.YCECGHeavyLoadLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCECGPressureLevel : Swift.Equatable {}
extension YCProductSDK.YCECGPressureLevel : Swift.Hashable {}
extension YCProductSDK.YCECGPressureLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCECGHrvNormLevel : Swift.Equatable {}
extension YCProductSDK.YCECGHrvNormLevel : Swift.Hashable {}
extension YCProductSDK.YCECGHrvNormLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCECGBodyIndexLevel : Swift.Equatable {}
extension YCProductSDK.YCECGBodyIndexLevel : Swift.Hashable {}
extension YCProductSDK.YCECGBodyIndexLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCECGSympatheticNervousActivityLevel : Swift.Equatable {}
extension YCProductSDK.YCECGSympatheticNervousActivityLevel : Swift.Hashable {}
extension YCProductSDK.YCECGSympatheticNervousActivityLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCProductState : Swift.Equatable {}
extension YCProductSDK.YCProductState : Swift.Hashable {}
extension YCProductSDK.YCProductState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceRealState : Swift.Equatable {}
extension YCProductSDK.YCDeviceRealState : Swift.Hashable {}
extension YCProductSDK.YCDeviceRealState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceSportState : Swift.Equatable {}
extension YCProductSDK.YCDeviceSportState : Swift.Hashable {}
extension YCProductSDK.YCDeviceSportState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceSportType : Swift.Equatable {}
extension YCProductSDK.YCDeviceSportType : Swift.Hashable {}
extension YCProductSDK.YCDeviceSportType : Swift.RawRepresentable {}
extension YCProductSDK.YCWaveUploadState : Swift.Equatable {}
extension YCProductSDK.YCWaveUploadState : Swift.Hashable {}
extension YCProductSDK.YCWaveUploadState : Swift.RawRepresentable {}
extension YCProductSDK.YCRealTimeDataType : Swift.Equatable {}
extension YCProductSDK.YCRealTimeDataType : Swift.Hashable {}
extension YCProductSDK.YCRealTimeDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCWaveDataType : Swift.Equatable {}
extension YCProductSDK.YCWaveDataType : Swift.Hashable {}
extension YCProductSDK.YCWaveDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCQuerySampleRateType : Swift.Equatable {}
extension YCProductSDK.YCQuerySampleRateType : Swift.Hashable {}
extension YCProductSDK.YCQuerySampleRateType : Swift.RawRepresentable {}
extension YCProductSDK.YCWeatherPeriodType : Swift.Equatable {}
extension YCProductSDK.YCWeatherPeriodType : Swift.Hashable {}
extension YCProductSDK.YCWeatherPeriodType : Swift.RawRepresentable {}
extension YCProductSDK.YCWeatherCodeType : Swift.Equatable {}
extension YCProductSDK.YCWeatherCodeType : Swift.Hashable {}
extension YCProductSDK.YCWeatherCodeType : Swift.RawRepresentable {}
extension YCProductSDK.YCWeatherMoonType : Swift.Equatable {}
extension YCProductSDK.YCWeatherMoonType : Swift.Hashable {}
extension YCProductSDK.YCWeatherMoonType : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthParametersState : Swift.Equatable {}
extension YCProductSDK.YCHealthParametersState : Swift.Hashable {}
extension YCProductSDK.YCHealthParametersState : Swift.RawRepresentable {}
extension YCProductSDK.YCHealthState : Swift.Equatable {}
extension YCProductSDK.YCHealthState : Swift.Hashable {}
extension YCProductSDK.YCHealthState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceSystemOperator : Swift.Equatable {}
extension YCProductSDK.YCDeviceSystemOperator : Swift.Hashable {}
extension YCProductSDK.YCDeviceSystemOperator : Swift.RawRepresentable {}
extension YCProductSDK.YCPersonalInfoType : Swift.Equatable {}
extension YCProductSDK.YCPersonalInfoType : Swift.Hashable {}
extension YCProductSDK.YCPersonalInfoType : Swift.RawRepresentable {}
extension YCProductSDK.YCAppControlMeasureMode : Swift.Equatable {}
extension YCProductSDK.YCAppControlMeasureMode : Swift.Hashable {}
extension YCProductSDK.YCAppControlMeasureMode : Swift.RawRepresentable {}
extension YCProductSDK.YCBodyTemperatureQRCodeColor : Swift.Equatable {}
extension YCProductSDK.YCBodyTemperatureQRCodeColor : Swift.Hashable {}
extension YCProductSDK.YCBodyTemperatureQRCodeColor : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceSenserSaveDataType : Swift.Equatable {}
extension YCProductSDK.YCDeviceSenserSaveDataType : Swift.Hashable {}
extension YCProductSDK.YCDeviceSenserSaveDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCWarningInformationType : Swift.Equatable {}
extension YCProductSDK.YCWarningInformationType : Swift.Hashable {}
extension YCProductSDK.YCWarningInformationType : Swift.RawRepresentable {}
extension YCProductSDK.YCSyncAddressBookState : Swift.Equatable {}
extension YCProductSDK.YCSyncAddressBookState : Swift.Hashable {}
extension YCProductSDK.YCSyncAddressBookState : Swift.RawRepresentable {}
extension YCProductSDK.YCMeasurementDataType : Swift.Equatable {}
extension YCProductSDK.YCMeasurementDataType : Swift.Hashable {}
extension YCProductSDK.YCMeasurementDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCAppControlHealthDataMeasureType : Swift.Equatable {}
extension YCProductSDK.YCAppControlHealthDataMeasureType : Swift.Hashable {}
extension YCProductSDK.YCAppControlHealthDataMeasureType : Swift.RawRepresentable {}
extension YCProductSDK.YCAppControlMeasureHealthDataType : Swift.Equatable {}
extension YCProductSDK.YCAppControlMeasureHealthDataType : Swift.Hashable {}
extension YCProductSDK.YCAppControlMeasureHealthDataType : Swift.RawRepresentable {}
extension YCProductSDK.YCAppControlMeasureHealthDataResult : Swift.Equatable {}
extension YCProductSDK.YCAppControlMeasureHealthDataResult : Swift.Hashable {}
extension YCProductSDK.YCAppControlMeasureHealthDataResult : Swift.RawRepresentable {}
extension YCProductSDK.YCBloodGlucoseCalibrationaMode : Swift.Equatable {}
extension YCProductSDK.YCBloodGlucoseCalibrationaMode : Swift.Hashable {}
extension YCProductSDK.YCBloodGlucoseCalibrationaMode : Swift.RawRepresentable {}
extension YCProductSDK.YCLocationInformationType : Swift.Equatable {}
extension YCProductSDK.YCLocationInformationType : Swift.Hashable {}
extension YCProductSDK.YCLocationInformationType : Swift.RawRepresentable {}
extension YCProductSDK.YCBusinessCardType : Swift.Equatable {}
extension YCProductSDK.YCBusinessCardType : Swift.Hashable {}
extension YCProductSDK.YCBusinessCardType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlInvasiveMeasurementState : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlInvasiveMeasurementState : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlInvasiveMeasurementState : Swift.RawRepresentable {}
extension YCProductSDK.YCDevcieSpecifyMessageType : Swift.Equatable {}
extension YCProductSDK.YCDevcieSpecifyMessageType : Swift.Hashable {}
extension YCProductSDK.YCDevcieSpecifyMessageType : Swift.RawRepresentable {}
extension YCProductSDK.YCProductLogLevel : Swift.Equatable {}
extension YCProductSDK.YCProductLogLevel : Swift.Hashable {}
extension YCProductSDK.YCProductLogLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCWeekDay : Swift.Equatable {}
extension YCProductSDK.YCWeekDay : Swift.Hashable {}
extension YCProductSDK.YCWeekDay : Swift.RawRepresentable {}
extension YCProductSDK.YCSettingGoalType : Swift.Equatable {}
extension YCProductSDK.YCSettingGoalType : Swift.Hashable {}
extension YCProductSDK.YCSettingGoalType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceGender : Swift.Equatable {}
extension YCProductSDK.YCDeviceGender : Swift.Hashable {}
extension YCProductSDK.YCDeviceGender : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceDistanceType : Swift.Equatable {}
extension YCProductSDK.YCDeviceDistanceType : Swift.Hashable {}
extension YCProductSDK.YCDeviceDistanceType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceWeightType : Swift.Equatable {}
extension YCProductSDK.YCDeviceWeightType : Swift.Hashable {}
extension YCProductSDK.YCDeviceWeightType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceTemperatureType : Swift.Equatable {}
extension YCProductSDK.YCDeviceTemperatureType : Swift.Hashable {}
extension YCProductSDK.YCDeviceTemperatureType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceBloodGlucoseType : Swift.Equatable {}
extension YCProductSDK.YCDeviceBloodGlucoseType : Swift.Hashable {}
extension YCProductSDK.YCDeviceBloodGlucoseType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceUricAcidType : Swift.Equatable {}
extension YCProductSDK.YCDeviceUricAcidType : Swift.Hashable {}
extension YCProductSDK.YCDeviceUricAcidType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceTimeType : Swift.Equatable {}
extension YCProductSDK.YCDeviceTimeType : Swift.Hashable {}
extension YCProductSDK.YCDeviceTimeType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceWeekRepeat : Swift.Equatable {}
extension YCProductSDK.YCDeviceWeekRepeat : Swift.Hashable {}
extension YCProductSDK.YCDeviceWeekRepeat : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceAntiLostType : Swift.Equatable {}
extension YCProductSDK.YCDeviceAntiLostType : Swift.Hashable {}
extension YCProductSDK.YCDeviceAntiLostType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceWearingPositionType : Swift.Equatable {}
extension YCProductSDK.YCDeviceWearingPositionType : Swift.Hashable {}
extension YCProductSDK.YCDeviceWearingPositionType : Swift.RawRepresentable {}
extension YCProductSDK.YCPhoneSystemType : Swift.Equatable {}
extension YCProductSDK.YCPhoneSystemType : Swift.Hashable {}
extension YCProductSDK.YCPhoneSystemType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceInfoPushType : Swift.Equatable {}
extension YCProductSDK.YCDeviceInfoPushType : Swift.Hashable {}
extension YCProductSDK.YCDeviceInfoPushType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceLanguageType : Swift.Equatable {}
extension YCProductSDK.YCDeviceLanguageType : Swift.Hashable {}
extension YCProductSDK.YCDeviceLanguageType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceDisplayBrightnessLevel : Swift.Equatable {}
extension YCProductSDK.YCDeviceDisplayBrightnessLevel : Swift.Hashable {}
extension YCProductSDK.YCDeviceDisplayBrightnessLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceSkinColorLevel : Swift.Equatable {}
extension YCProductSDK.YCDeviceSkinColorLevel : Swift.Hashable {}
extension YCProductSDK.YCDeviceSkinColorLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceBloodPressureLevel : Swift.Equatable {}
extension YCProductSDK.YCDeviceBloodPressureLevel : Swift.Hashable {}
extension YCProductSDK.YCDeviceBloodPressureLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceDataCollectionType : Swift.Equatable {}
extension YCProductSDK.YCDeviceDataCollectionType : Swift.Hashable {}
extension YCProductSDK.YCDeviceDataCollectionType : Swift.RawRepresentable {}
extension YCProductSDK.YCSettingTemperatureModeType : Swift.Equatable {}
extension YCProductSDK.YCSettingTemperatureModeType : Swift.Hashable {}
extension YCProductSDK.YCSettingTemperatureModeType : Swift.RawRepresentable {}
@available(*, deprecated, message: "use YCDeviceScreenTimeInterval instand of it")
extension YCProductSDK.YCDeviceBreathScreenInterval : Swift.Equatable {}
@available(*, deprecated, message: "use YCDeviceScreenTimeInterval instand of it")
extension YCProductSDK.YCDeviceBreathScreenInterval : Swift.Hashable {}
@available(*, deprecated, message: "use YCDeviceScreenTimeInterval instand of it")
extension YCProductSDK.YCDeviceBreathScreenInterval : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceScreenTimeInterval : Swift.Equatable {}
extension YCProductSDK.YCDeviceScreenTimeInterval : Swift.Hashable {}
extension YCProductSDK.YCDeviceScreenTimeInterval : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceWorkModeType : Swift.Equatable {}
extension YCProductSDK.YCDeviceWorkModeType : Swift.Hashable {}
extension YCProductSDK.YCDeviceWorkModeType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceMCUType : Swift.Equatable {}
extension YCProductSDK.YCDeviceMCUType : Swift.Hashable {}
extension YCProductSDK.YCDeviceMCUType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceReminderSettingState : Swift.Equatable {}
extension YCProductSDK.YCDeviceReminderSettingState : Swift.Hashable {}
extension YCProductSDK.YCDeviceReminderSettingState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceExerciseHeartRateType : Swift.Equatable {}
extension YCProductSDK.YCDeviceExerciseHeartRateType : Swift.Hashable {}
extension YCProductSDK.YCDeviceExerciseHeartRateType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceMotorVibrationType : Swift.Equatable {}
extension YCProductSDK.YCDeviceMotorVibrationType : Swift.Hashable {}
extension YCProductSDK.YCDeviceMotorVibrationType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceAlarmType : Swift.Equatable {}
extension YCProductSDK.YCDeviceAlarmType : Swift.Hashable {}
extension YCProductSDK.YCDeviceAlarmType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceAlarmOperatoreType : Swift.Equatable {}
extension YCProductSDK.YCDeviceAlarmOperatoreType : Swift.Hashable {}
extension YCProductSDK.YCDeviceAlarmOperatoreType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceEventInterval : Swift.Equatable {}
extension YCProductSDK.YCDeviceEventInterval : Swift.Hashable {}
extension YCProductSDK.YCDeviceEventInterval : Swift.RawRepresentable {}
extension YCProductSDK.YCDevcieScheduleEventType : Swift.Equatable {}
extension YCProductSDK.YCDevcieScheduleEventType : Swift.Hashable {}
extension YCProductSDK.YCDevcieScheduleEventType : Swift.RawRepresentable {}
extension YCProductSDK.YCTemperatureType : Swift.Equatable {}
extension YCProductSDK.YCTemperatureType : Swift.Hashable {}
extension YCProductSDK.YCTemperatureType : Swift.RawRepresentable {}
extension YCProductSDK.YCMotorVibrationStrengthLevel : Swift.Equatable {}
extension YCProductSDK.YCMotorVibrationStrengthLevel : Swift.Hashable {}
extension YCProductSDK.YCMotorVibrationStrengthLevel : Swift.RawRepresentable {}
extension YCProductSDK.YCPeriodicReminderType : Swift.Equatable {}
extension YCProductSDK.YCPeriodicReminderType : Swift.Hashable {}
extension YCProductSDK.YCPeriodicReminderType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceAlipayState : Swift.Equatable {}
extension YCProductSDK.YCDeviceAlipayState : Swift.Hashable {}
extension YCProductSDK.YCDeviceAlipayState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceScreenType : Swift.Equatable {}
extension YCProductSDK.YCDeviceScreenType : Swift.Hashable {}
extension YCProductSDK.YCDeviceScreenType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceFaceTimePosition : Swift.Equatable {}
extension YCProductSDK.YCDeviceFaceTimePosition : Swift.Hashable {}
extension YCProductSDK.YCDeviceFaceTimePosition : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlType : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlType : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlState : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlState : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlPhotoState : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlPhotoState : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlPhotoState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlAllowConnectionState : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlAllowConnectionState : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlAllowConnectionState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlDeviceSportState : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlDeviceSportState : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlDeviceSportState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceControlDynamicCodeState : Swift.Equatable {}
extension YCProductSDK.YCDeviceControlDynamicCodeState : Swift.Hashable {}
extension YCProductSDK.YCDeviceControlDynamicCodeState : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceCheckType : Swift.Equatable {}
extension YCProductSDK.YCDeviceCheckType : Swift.Hashable {}
extension YCProductSDK.YCDeviceCheckType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceDetectionErrorType : Swift.Equatable {}
extension YCProductSDK.YCDeviceDetectionErrorType : Swift.Hashable {}
extension YCProductSDK.YCDeviceDetectionErrorType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceBatterystate : Swift.Equatable {}
extension YCProductSDK.YCDeviceBatterystate : Swift.Hashable {}
extension YCProductSDK.YCDeviceBatterystate : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceType : Swift.Equatable {}
extension YCProductSDK.YCDeviceType : Swift.Hashable {}
extension YCProductSDK.YCDeviceType : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceElectrodePosition : Swift.Equatable {}
extension YCProductSDK.YCDeviceElectrodePosition : Swift.Hashable {}
extension YCProductSDK.YCDeviceElectrodePosition : Swift.RawRepresentable {}
extension YCProductSDK.YCDeviceRemindType : Swift.Equatable {}
extension YCProductSDK.YCDeviceRemindType : Swift.Hashable {}
extension YCProductSDK.YCDeviceRemindType : Swift.RawRepresentable {}
