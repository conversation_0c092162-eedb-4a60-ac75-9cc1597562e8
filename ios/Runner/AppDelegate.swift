import UIKit
import Flutter
import YCProductSDK
import BackgroundTasks
import HealthKit
import CoreBluetooth

@main
@objc class AppDelegate: FlutterAppDelegate {
    
    private let CHANNEL = "com.saiwell.sw/ios_native"
    private let logPrefix = "[HealthKitDebug]"      
    var isRingInitialized = false
    let healthStore = HKHealthStore()
    var healthDataTypes: Set<HKObjectType> = []
    var isConnected = false
    private let bleServiceV2 = BleServiceV2.shared
    
    // Properties to track PPG recording progress
    private var ppgTimer: Timer?
    private var ppgMeasurementTime: Int = 0
    private let ppgTestTime: Int = 300 // Total time for 100% completion in seconds
    
    // Store PPG data
    private var ppgData: [Int] = []
    
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        print("\(self.logPrefix) App launching")
        GeneratedPluginRegistrant.register(with: self)
        initializeYCProduct()
        print("\(self.logPrefix) YCProduct initialized")
        
        // Set this AppDelegate as the delegate for BleServiceV2
        bleServiceV2.delegate = self
        
        if let flutterViewController = window?.rootViewController as? FlutterViewController {
            let methodChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: flutterViewController.binaryMessenger)
            methodChannel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, flutterResult: @escaping FlutterResult) in
                guard let strongSelf = self else { return }
                print("\(strongSelf.logPrefix) Received method call: \(call.method)")
                switch call.method {
                case "startScanDevices":
                    strongSelf.startScanDevices(result: flutterResult)
                case "startScanDevicesV2":
                    strongSelf.startScanDevicesV2(result: flutterResult)
                case "connectToDeviceV2":
                    if let uuidString = call.arguments as? String {
                        strongSelf.connectToDeviceV2(uuidString: uuidString, result: flutterResult)
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT", message: "Invalid UUID", details: nil))
                    }
                case "connectToDevice":
                    if let args = call.arguments as? [String: Any], let macId = args["macId"] as? String {
                        print("\(strongSelf.logPrefix) Connecting to device with macId: \(macId)")
                        strongSelf.connectToDevice(macId: macId, result: flutterResult)
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT", message: "macId is required", details: nil))
                    }
                case "disconnectToDeviceV2":
                    strongSelf.disconnectToDeviceV2(result: flutterResult)
                case "currentConnectedDevice":
                     print("\(strongSelf.logPrefix) Fetching current connected device")
                     strongSelf.currentConnectedDevice(result: flutterResult)
                case "handleGetBatteryLevelV2":
                    strongSelf.handleGetBatteryLevelV2(result: flutterResult)
                case "getDeviceVersionV2":
                    strongSelf.getDeviceVersionV2(result: flutterResult)
                case "getDeviceTimeV2":
                    strongSelf.getDeviceTimeV2(result: flutterResult)
                case "getTotalActivityDataWithModeV2":
                    strongSelf.getTotalActivityDataWithModeV2(result: flutterResult)
                case "getDeviceMacV2":
                    strongSelf.getDeviceMacV2(result: flutterResult)
                case "getTemperatureDataV2":
                    strongSelf.getTemperatureDataV2(result: flutterResult)
                case "getHRVDataWithModeV2":
                    strongSelf.getHRVDataWithModeV2(result: flutterResult)
                case "getSleepDataV2":
                    strongSelf.getSleepDataV2(result: flutterResult)
                case "getBloodOxygenV2":
                    strongSelf.getBloodOxygenV2(result: flutterResult)
                case "getHealthData":
                    if let args = call.arguments as? [String: Any], let type = args["type"] as? String {
                        print("\(strongSelf.logPrefix) Fetching health data for type: \(type)")
                        strongSelf.getHealthData(type: type, result: flutterResult)
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT", message: "type is required", details: nil))
                    }
                case "deleteHealthData":
                    if let args = call.arguments as? [String: Any], let type = args["type"] as? String {
                        print("\(strongSelf.logPrefix) Deleting health data for type: \(type)")
                        strongSelf.deleteHealthData(type: type, result: flutterResult)
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT", message: "type is required", details: nil))
                    }
                case "getDeviceInfo":
                    print("\(strongSelf.logPrefix) Fetching device info")
                    strongSelf.getDeviceInfo(result: flutterResult)
                case "requestHealthKitPermissions":
                    print("\(strongSelf.logPrefix) Fetching device info")
                    strongSelf.requestHealthKitPermissions(result: flutterResult)
                case "isDeviceConnected":
                    strongSelf.isDeviceConnected(result: flutterResult)
                case "isDeviceConnectedV2":
                    strongSelf.isDeviceConnectedV2(result: flutterResult)
                case "postDateTimeV2":
                    strongSelf.postDateTimeV2(result: flutterResult)
                case "deleteHRVDataV2":
                    strongSelf.deleteHRVDataV2(result: flutterResult)
                case "deleteTemperatureDataV2":
                    strongSelf.deleteTemperatureDataV2(result: flutterResult)
                case "deleteSleepDataV2":
                    strongSelf.deleteSleepDataV2(result: flutterResult)
                case "deleteBloodOxygenV2":
                    strongSelf.deleteBloodOxygenV2(result: flutterResult)
                case "deleteTotalActivityDataV2":
                    strongSelf.deleteTotalActivityDataV2(result: flutterResult)
                case "startPPGWaveV2":
                    strongSelf.startPPGWaveV2(result: flutterResult)
                case "stopPPGWaveV2":
                    strongSelf.stopPPGWaveV2(result: flutterResult)
                case "setAutomaticHRMonitoringOneV2":
                    if let args = call.arguments as? [String: Any],
                       let startHour = args["startHour"] as? Int,
                       let startMinute = args["startMinute"] as? Int,
                       let endHour = args["endHour"] as? Int,
                       let endMinute = args["endMinute"] as? Int,
                       let weeks = args["weeks"] as? [String: Bool],
                       let intervalTime = args["intervalTime"] as? Int {
                        let weeksConverted = weeks.mapValues { ObjCBool($0) }
                        
                        strongSelf.setAutomaticHRMonitoringOneV2(
                            startHour: startHour,
                            startMinute: startMinute,
                            endHour: endHour,
                            endMinute: endMinute,
                            weeks: weeksConverted,
                            intervalTime: intervalTime,
                            result: flutterResult
                        )
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT",
                                                   message: "All parameters are required",
                                                   details: nil))
                    }
                case "setAutomaticHRMonitoringTwoV2":
                    if let args = call.arguments as? [String: Any],
                       
                        let startHour = args["startHour"] as? Int,
                       let startMinute = args["startMinute"] as? Int,
                       let endHour = args["endHour"] as? Int,
                       let endMinute = args["endMinute"] as? Int,
                       let weeks = args["weeks"] as? [String: Bool],
                       let intervalTime = args["intervalTime"] as? Int {
                        let weeksConverted = weeks.mapValues { ObjCBool($0) }
                        strongSelf.setAutomaticHRMonitoringTwoV2(
                            startHour: startHour,
                            startMinute: startMinute,
                            endHour: endHour,
                            endMinute: endMinute,
                            weeks: weeksConverted,
                            intervalTime: intervalTime,
                            result: flutterResult
                        )
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT",
                                                   message: "All parameters are required",
                                                   details: nil))
                    }
                case "setAutomaticHRMonitoringThreeV2":
                    if let args = call.arguments as? [String: Any],
                
                        let startHour = args["startHour"] as? Int,
                       let startMinute = args["startMinute"] as? Int,
                       let endHour = args["endHour"] as? Int,
                       let endMinute = args["endMinute"] as? Int,
                       let weeks = args["weeks"] as? [String: Bool],
                       let intervalTime = args["intervalTime"] as? Int {
                        let weeksConverted = weeks.mapValues { ObjCBool($0) }
                        strongSelf.setAutomaticHRMonitoringThreeV2(
                            startHour: startHour,
                            startMinute: startMinute,
                            endHour: endHour,
                            endMinute: endMinute,
                            weeks: weeksConverted,
                            intervalTime: intervalTime,
                            result: flutterResult
                        )
                    } else {
                        flutterResult(FlutterError(code: "INVALID_ARGUMENT",
                                                   message: "All parameters are required",
                                                   details: nil))
                    }
                case "getBaseURL":
                    if let baseURL = UserDefaults.standard.string(forKey: "baseURL") {
                        print("\(strongSelf.logPrefix) Base URL fetched: \(baseURL)")
                        flutterResult(baseURL)
                    } else {
                        print("\(strongSelf.logPrefix) Base URL not found")
                        flutterResult(FlutterError(code: "UNAVAILABLE", message: "Base URL not found", details: nil))
                    }
                default:
                    print("\(strongSelf.logPrefix) Method not implemented: \(call.method)")
                    flutterResult(FlutterMethodNotImplemented)
                }
            }
        }
        
        defineHealthDataTypes()
        
        let center = UNUserNotificationCenter.current()
        center.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                print("Notification permission error: \(error.localizedDescription)")
            } else if granted {
                print("Notification permissions granted")
            }
        }
        center.delegate = self
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func defineHealthDataTypes() {
        print("\(self.logPrefix) Defining health data types")
        var tempDataTypes: [HKObjectType] = [
            // Activity
            HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned)!,
            HKQuantityType.quantityType(forIdentifier: .distanceWalkingRunning)!,
            HKQuantityType.quantityType(forIdentifier: .flightsClimbed)!,
            HKQuantityType.quantityType(forIdentifier: .stepCount)!,
            HKWorkoutType.workoutType(),
            
            // Vital Signs
            HKQuantityType.quantityType(forIdentifier: .heartRate)!,
            HKQuantityType.quantityType(forIdentifier: .restingHeartRate)!,
            HKQuantityType.quantityType(forIdentifier: .walkingHeartRateAverage)!,
            HKQuantityType.quantityType(forIdentifier: .heartRateVariabilitySDNN)!,
            HKQuantityType.quantityType(forIdentifier: .bloodPressureSystolic)!,
            HKQuantityType.quantityType(forIdentifier: .bloodPressureDiastolic)!,
            HKQuantityType.quantityType(forIdentifier: .respiratoryRate)!,
            HKQuantityType.quantityType(forIdentifier: .bodyTemperature)!,
            HKQuantityType.quantityType(forIdentifier: .oxygenSaturation)!,
            HKQuantityType.quantityType(forIdentifier: .bloodGlucose)!,
            
            // Body Measurements
            HKQuantityType.quantityType(forIdentifier: .height)!,
            HKQuantityType.quantityType(forIdentifier: .bodyFatPercentage)!,
            HKQuantityType.quantityType(forIdentifier: .bodyMassIndex)!,
            
            // Sleep
            HKCategoryType.categoryType(forIdentifier: .sleepAnalysis)!
        ]
        
        if #available(iOS 16.0, *) {
            // iOS 16+ specific types
            if let atrialFibrillationType = HKQuantityType.quantityType(forIdentifier: .atrialFibrillationBurden) {
                tempDataTypes.append(atrialFibrillationType)
            }
        }
        
        healthDataTypes = Set(tempDataTypes)
        print("\(self.logPrefix) Health data types defined: \(healthDataTypes)")
    }
    
    private func requestHealthKitPermissions(result: @escaping FlutterResult) {
        if HKHealthStore.isHealthDataAvailable() {
            print("\(self.logPrefix) Requesting HealthKit permissions")
            healthStore.requestAuthorization(toShare: nil, read: healthDataTypes) { [weak self] success, error in
                guard let self = self else {
                    result(FlutterError(code: "UNAVAILABLE", message: "Instance deallocated", details: nil))
                    return
                }
                
                DispatchQueue.main.async {
                    if success {
                        print("\(self.logPrefix) HealthKit permissions granted")
                        self.startObservingHealthData()
                        result(true) // Notify Flutter of success
                    } else {
                        let errorMessage = error?.localizedDescription ?? "Unknown error"
                        print("\(self.logPrefix) HealthKit permissions denied: \(errorMessage)")
                        result(FlutterError(code: "PERMISSION_DENIED", message: errorMessage, details: nil))
                    }
                }
            }
        } else {
            let message = "Health data not available on this device"
            print("\(self.logPrefix) \(message)")
            result(FlutterError(code: "NOT_AVAILABLE", message: message, details: nil))
        }
    }
    
    private func startObservingHealthData() {
        // Define the HealthKit data types we want to observe
        for type in healthDataTypes {
            // Skip if already observing this type
            if let quantityType = type as? HKQuantityType {
                observeQuantityType(quantityType)
            } else if let categoryType = type as? HKCategoryType {
                observeCategoryType(categoryType)
            }
        }
        
        // Also fetch the initial data immediately instead of waiting for changes
        DispatchQueue.global(qos: .background).async { [weak self] in
            guard let self = self else { return }
            // Start initial fetch of all health data types at once
            let group = DispatchGroup()
            
            for type in self.healthDataTypes {
                group.enter()
                if let quantityType = type as? HKQuantityType {
                    self.fetchQuantityData(for: quantityType) { data in
                        if !data.isEmpty {
                            let payload = [quantityType.identifier: data]
                            self.notifyFlutter(data: payload)
                        }
                        group.leave()
                    }
                } else if let categoryType = type as? HKCategoryType {
                    self.fetchCategoryData(for: categoryType) { data in
                        if !data.isEmpty {
                            if categoryType.identifier == HKCategoryTypeIdentifier.sleepAnalysis.rawValue {
                                self.notifyFlutter(data: data)
                            } else {
                                let payload = [categoryType.identifier: data]
                                self.notifyFlutter(data: payload)
                            }
                        }
                        group.leave()
                    }
                } else {
                    group.leave()
                }
            }
        }
    }
    
    private func fetchSpecificHealthData(for type: HKObjectType) {
        if let quantityType = type as? HKQuantityType {
            fetchQuantityData(for: quantityType) { [weak self] data in
                guard let strongSelf = self else { return }
                if !data.isEmpty {
                    let payload = [quantityType.identifier: data]
                    strongSelf.notifyFlutter(data: payload)
                }
            }
        } else if let categoryType = type as? HKCategoryType {
            fetchCategoryData(for: categoryType) { [weak self] data in
                guard let strongSelf = self else { return }
                if !data.isEmpty {
                    // Check if the data is for sleep analysis
                    if categoryType.identifier == HKCategoryTypeIdentifier.sleepAnalysis.rawValue {
                        strongSelf.notifyFlutter(data: data) // Directly send the sleep data without wrapping in the identifier
                    } else {
                        let payload = [categoryType.identifier: data]
                        strongSelf.notifyFlutter(data: payload)
                    }
                }
            }
        }
    }
    
    private func observeQuantityType(_ quantityType: HKQuantityType) {
        print("\(self.logPrefix) Observing quantity type: \(quantityType.identifier)")
        let query = HKObserverQuery(sampleType: quantityType, predicate: nil) { [weak self] query, completionHandler, error in
            guard let strongSelf = self else {
                completionHandler()
                return
            }
            
            if error == nil {
                strongSelf.fetchSpecificHealthData(for: quantityType)
            }
            completionHandler()
        }
        
        healthStore.execute(query)
        
        // Use hourly updates instead of immediate to reduce battery drain
        healthStore.enableBackgroundDelivery(for: quantityType, frequency: .hourly) { success, error in
            if !success {
                print("Failed to enable background delivery for \(quantityType.identifier): \(error?.localizedDescription ?? "Unknown error")")
            }
        }
    }
    
    private func observeCategoryType(_ categoryType: HKCategoryType) {
        print("\(self.logPrefix) Observing category type: \(categoryType.identifier)")
        let query = HKObserverQuery(sampleType: categoryType, predicate: nil) { [weak self] query, completionHandler, error in
            guard let strongSelf = self else {
                completionHandler()
                return
            }
            
            if error == nil {
                // Only fetch data for this specific type that changed
                strongSelf.fetchSpecificHealthData(for: categoryType)
            }
            completionHandler()
        }
        
        healthStore.execute(query)
        
        // Use hourly updates instead of immediate to reduce battery drain
        healthStore.enableBackgroundDelivery(for: categoryType, frequency: .hourly) { success, error in
            if !success {
                print("Failed to enable background delivery for \(categoryType.identifier): \(error?.localizedDescription ?? "Unknown error")")
            }
        }
    }
    
    private func fetchQuantityData(for quantityType: HKQuantityType, completion: @escaping ([String: Any]) -> Void) {
        let now = Date()
        let startDate = Calendar.current.date(byAdding: .hour, value: -24, to: now)!
        let predicate = HKQuery.predicateForSamples(withStart: startDate, end: now, options: .strictEndDate)
        
        let query = HKSampleQuery(
            sampleType: quantityType,
            predicate: predicate,
            limit: HKObjectQueryNoLimit,  // Remove limit to get all samples
            sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)]
        ) { [weak self] _, samples, error in
            guard let samples = samples as? [HKQuantitySample], !samples.isEmpty else {
                completion([:])
                return
            }
            
            let unit = self?.getUnitForQuantityType(quantityType) ?? HKUnit.count()
            
            // Create an array to store all samples
            var dataPoints: [[String: Any]] = []
            
            // Use batch processing to speed up data conversion
            let batchSize = 100
            var currentBatch: [[String: Any]] = []
            
            // Process each sample
            for (index, sample) in samples.enumerated() {
                let dataPoint: [String: Any] = [
                    "value": sample.quantity.doubleValue(for: unit),
                    "unit": unit.unitString == "m" ? "METER" : unit.unitString,
                    "startTimestamp": Int(sample.startDate.timeIntervalSince1970),
                    "endTimestamp": Int(sample.endDate.timeIntervalSince1970),
                    "sourceId": sample.sourceRevision.source.bundleIdentifier,
                    "uuid": sample.uuid.uuidString,
                ]
                currentBatch.append(dataPoint)
                
                // When we reach batch size or the end, add to main array and continue
                if currentBatch.count == batchSize || index == samples.count - 1 {
                    dataPoints.append(contentsOf: currentBatch)
                    currentBatch = []
                }
            }
            
            // Create the final data structure
            let data: [String: Any] = [
                "dataPoints": dataPoints,
                "type": quantityType.identifier,
                "count": dataPoints.count
            ]
            
            completion(data)
        }
        healthStore.execute(query)
    }
    
    private func getUnitForQuantityType(_ quantityType: HKQuantityType) -> HKUnit {
        
#if os(iOS)
        if #available(iOS 16.0, *) {
            if quantityType.identifier == HKQuantityTypeIdentifier.atrialFibrillationBurden.rawValue {
                return HKUnit.percent()
            }
        }
#endif
        
        
        switch quantityType.identifier {
            // Activity Metrics
        case HKQuantityTypeIdentifier.activeEnergyBurned.rawValue:
            return HKUnit.kilocalorie()
        case HKQuantityTypeIdentifier.distanceWalkingRunning.rawValue:
            return HKUnit.meter()
        case HKQuantityTypeIdentifier.flightsClimbed.rawValue:
            return HKUnit.count()
        case HKQuantityTypeIdentifier.stepCount.rawValue:
            return HKUnit.count()
            
            // Heart Metrics
        case HKQuantityTypeIdentifier.heartRate.rawValue,
            HKQuantityTypeIdentifier.restingHeartRate.rawValue,
            HKQuantityTypeIdentifier.walkingHeartRateAverage.rawValue:
            return HKUnit.count().unitDivided(by: .minute())
        case HKQuantityTypeIdentifier.heartRateVariabilitySDNN.rawValue:
            return HKUnit.secondUnit(with: .milli)
            
            // Blood Pressure
        case HKQuantityTypeIdentifier.bloodPressureSystolic.rawValue,
            HKQuantityTypeIdentifier.bloodPressureDiastolic.rawValue:
            return HKUnit.millimeterOfMercury()
            
            // Other Vital Signs
        case HKQuantityTypeIdentifier.respiratoryRate.rawValue:
            return HKUnit.count().unitDivided(by: .minute())
        case HKQuantityTypeIdentifier.bodyTemperature.rawValue:
            return HKUnit.degreeCelsius()
        case HKQuantityTypeIdentifier.oxygenSaturation.rawValue:
            return HKUnit.percent()
        case HKQuantityTypeIdentifier.bloodGlucose.rawValue:
            return HKUnit.gramUnit(with: .milli).unitDivided(by: .literUnit(with: .deci))
            
            // Body Measurements
        case HKQuantityTypeIdentifier.height.rawValue:
            return HKUnit.meter()
        case HKQuantityTypeIdentifier.bodyFatPercentage.rawValue:
            return HKUnit.percent()
        case HKQuantityTypeIdentifier.bodyMassIndex.rawValue:
            return HKUnit.count()
            
            // Sleep-Related Measurements
        case HKQuantityTypeIdentifier.appleStandTime.rawValue:
            return HKUnit.minute()
        case HKQuantityTypeIdentifier.appleExerciseTime.rawValue:
            return HKUnit.minute()
            
            
            // Default case for any unhandled types
        default:
            print("\(self.logPrefix) Warning: Using default unit for type: \(quantityType.identifier)")
            return HKUnit.count()
        }
    }
    
    private func fetchCategoryData(for categoryType: HKCategoryType, completion: @escaping ([String: Any]) -> Void) {
        let now = Date()
        let calendar = Calendar.current
        let startDate = calendar.date(byAdding: .hour, value: -24, to: now)!
        
        let predicate = HKQuery.predicateForSamples(
            withStart: startDate,
            end: now,
            options: .strictEndDate
        )
        
        let query = HKSampleQuery(
            sampleType: categoryType,
            predicate: predicate,
            limit: HKObjectQueryNoLimit,
            sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierEndDate, ascending: false)]
        ) { _, samples, error in
            guard let samples = samples as? [HKCategorySample], !samples.isEmpty else {
                print("No samples found for \(categoryType.identifier)")
                completion([:])
                return
            }
            
            if categoryType.identifier == HKCategoryTypeIdentifier.sleepAnalysis.rawValue {
                
                var sleepData: [String: [String: [[String: Any]]]] = [
                    "HKCategoryTypeIdentifierSleepAnalysisInBed": ["dataPoints": []],
                    "HKCategoryTypeIdentifierSleepAnalysisAsleep": ["dataPoints": []],
                    "HKCategoryTypeIdentifierSleepAnalysisAwake": ["dataPoints": []],
                    "HKCategoryTypeIdentifierSleepAnalysisLight": ["dataPoints": []],
                    "HKCategoryTypeIdentifierSleepAnalysisDeep": ["dataPoints": []],
                    "HKCategoryTypeIdentifierSleepAnalysisREM": ["dataPoints": []]
                ]
                
                for sample in samples {
                    var key: String = ""
                    
                    // Calculate duration in minutes
                    let durationInMinutes = sample.endDate.timeIntervalSince(sample.startDate) / 60.0
                    
#if os(iOS)
                    if #available(iOS 16.0, *) {
                        switch sample.value {
                        case HKCategoryValueSleepAnalysis.asleepCore.rawValue:
                            key = "HKCategoryTypeIdentifierSleepAnalysisLight"
                        case HKCategoryValueSleepAnalysis.asleepDeep.rawValue:
                            key = "HKCategoryTypeIdentifierSleepAnalysisDeep"
                        case HKCategoryValueSleepAnalysis.asleepREM.rawValue:
                            key = "HKCategoryTypeIdentifierSleepAnalysisREM"
                        default:
                            break
                        }
                    }
#endif
                    
                    if key.isEmpty {
                        switch sample.value {
                        case HKCategoryValueSleepAnalysis.asleep.rawValue:
                            key = "HKCategoryTypeIdentifierSleepAnalysisAsleep"
                        case HKCategoryValueSleepAnalysis.awake.rawValue:
                            key = "HKCategoryTypeIdentifierSleepAnalysisAwake"
                        case HKCategoryValueSleepAnalysis.inBed.rawValue:
                            key = "HKCategoryTypeIdentifierSleepAnalysisInBed"
                        default:
                            continue
                        }
                    }
                    
                    let dataPoint: [String: Any] = [
                        "value": durationInMinutes,
                        "unit": "min",
                        "startTimestamp": Int(sample.startDate.timeIntervalSince1970),
                        "endTimestamp": Int(sample.endDate.timeIntervalSince1970),
                        "sourceId": sample.sourceRevision.source.bundleIdentifier,
                        "uuid": sample.uuid.uuidString
                    ]
                    
                    sleepData[key]?["dataPoints"]?.append(dataPoint)
                }
                
                sleepData = sleepData.filter { !($0.value["dataPoints"] ?? []).isEmpty }
                completion(sleepData)
            } else {
                
                guard let sample = samples.first else {
                    completion([:])
                    return
                }
                
                var unit = "category"
                let value = sample.value
                
                switch categoryType.identifier {
                case HKCategoryTypeIdentifier.menstrualFlow.rawValue:
                    unit = "flow"
                case HKCategoryTypeIdentifier.irregularHeartRhythmEvent.rawValue:
                    unit = "event"
                default:
                    break
                }
                
                let dataPoint: [String: Any] = [
                    "value": value,
                    "unit": unit,
                    "startTimestamp": Int(sample.startDate.timeIntervalSince1970),
                    "endTimestamp": Int(sample.endDate.timeIntervalSince1970),
                    "sourceId": sample.sourceRevision.source.bundleIdentifier,
                    "uuid": sample.uuid.uuidString
                ]
                
                let data = [
                    categoryType.identifier: [
                        "dataPoints": [dataPoint]
                    ]
                ]
                
                completion(data)
            }
        }
        
        self.healthStore.execute(query)
    }
    
    
    private func scheduleNotification(for dataType: String) {
        let content = UNMutableNotificationContent()
        content.title = "Health Data Update"
        content.body = "New \(dataType) data has been sent to the app"
        content.sound = .default
        
        // Create a trigger for immediate delivery
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        
        // Create a request with a unique identifier
        let request = UNNotificationRequest(
            identifier: UUID().uuidString,
            content: content,
            trigger: trigger
        )
        
        // Schedule the notification
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("\(self.logPrefix) Error scheduling notification: \(error)")
            }
        }
    }
    
    
    private func notifyFlutter(data: [String: Any]) {
        print("\(self.logPrefix) Attempting to send data to Flutter")
        print("\(self.logPrefix) Data payload: \(data)")
        
        DispatchQueue.main.async { [weak self] in
            guard let strongSelf = self else {
                print("\(self?.logPrefix ?? "") Error: Self is nil")
                return
            }
            
            if let flutterViewController = strongSelf.window?.rootViewController as? FlutterViewController {
                print("\(strongSelf.logPrefix) Found Flutter view controller")
                let methodChannel = FlutterMethodChannel(
                    name: strongSelf.CHANNEL,
                    binaryMessenger: flutterViewController.binaryMessenger
                )
                
                // If this is a quantity type data with dataPoints
                if let dataPoints = data["dataPoints"] as? [[String: Any]] {
                    let payload: [String: Any] = [
                        "dataType": data["type"] as? String ?? "",
                        "data": dataPoints
                    ]
                    
                    print("\(strongSelf.logPrefix) Sending \(dataPoints.count) data points to Flutter")
                    methodChannel.invokeMethod("onHealthDataChange", arguments: payload) { result in
                        if let error = result as? FlutterError {
                            print("\(strongSelf.logPrefix) Error sending to Flutter: \(error.message ?? "Unknown error")")
                        } else {
                            print("\(strongSelf.logPrefix) Successfully sent to Flutter")
                        }
                    }
                } else {
                    // Handle category data or other formats as before
                    methodChannel.invokeMethod("onHealthDataChange", arguments: data) { result in
                        if let error = result as? FlutterError {
                            print("\(strongSelf.logPrefix) Error sending to Flutter: \(error.message ?? "Unknown error")")
                        } else {
                            print("\(strongSelf.logPrefix) Successfully sent to Flutter")
                        }
                    }
                }
            } else {
                print("\(strongSelf.logPrefix) Error: Flutter view controller not found")
            }
        }
    }
}

extension AppDelegate {
    private func startScanDevices(result: @escaping FlutterResult) {
        print("----------hello from native----IOS---scanning")
        ServiceBle.shared.getScannedDevices { devicesList, error in
            if let error = error {
                result(FlutterError(code: "UNAVAILABLE", message: "Scanning failed", details: error.localizedDescription))
            } else if let devicesList = devicesList {
                print("deviceList: ", devicesList)
                result(devicesList)
                
            } else {
                result([])  // Return an empty list if no devices found
            }
        }
    }
    private func connectToDevice(macId: String, result: @escaping FlutterResult) {
        print("----------hello from native----IOS--connecting")
        ServiceBle.shared.connectDevice(macId: macId) { isConnected, error in
            if let error = error {
                result(FlutterError(code: "UNAVAILABLE", message: "Connecting failed", details: error.localizedDescription))
            } else {
                print("got connected: ", isConnected)
                result(isConnected)
            }
        }
    }
    private func currentConnectedDevice(result: @escaping FlutterResult) {
        print("----------hello from native----IOS--currentConnectedDevice")
        ServiceBle.shared.currentConnectedDevice { peripherals, error in
            if let error = error {
                result(FlutterError(code: "404",
                                    message: "No connected device found",
                                    details: error.localizedDescription))
            } else if let peripherals = peripherals {
                result(peripherals)
            } else {
                result([])
            }
        }
    }
    
    
    private func isDeviceConnected(result: @escaping FlutterResult){
        result(isConnected)
    }
    
    private func getHealthData(type: String, result: @escaping FlutterResult) {
        print("----------hello from native----IOS--getHealthData")
        ServiceBle.shared.getHealthData(type: type) { isConnected, error in
            if let error = error {
                result(FlutterError(code: "UNAVAILABLE", message: "Getting health data failed", details: error.localizedDescription))
            } else {
                result(isConnected)
            }
        }
    }
    private func deleteHealthData(type: String, result: @escaping FlutterResult) {
        print("----------hello from native----IOS--deleteHealthData")
        ServiceBle.shared.deleteHealthData(type: type) { isDeleted, error in
            if let error = error {
                result(FlutterError(code: "UNAVAILABLE", message: "Deleting health data failed", details: error.localizedDescription))
            } else {
                result(isDeleted)
            }
        }
    }
    func initializeYCProduct() {
        if isRingInitialized == false {
            isRingInitialized = true
            print("initializeYCProduct")
            YCProduct.setLogLevel(.normal)
            _ = YCProduct.shared
            
            NotificationCenter.default.addObserver(self, selector: #selector(deviceStateChange(_:)), name: YCProduct.deviceStateNotification, object: nil)
        }
    }
    private func getDeviceInfo(result: @escaping FlutterResult) {
        print("----------hello from native----IOS--getDeviceInfo")
        ServiceBle.shared.getDeviceInfo { data, error in
            if let error = error {
                result(FlutterError(code: "UNAVAILABLE", message: "Fetching device data failed", details: error.localizedDescription))
            } else {
                result(data)
            }
        }
    }
    @objc private func deviceStateChange(_ ntf: Notification) {
        
        guard let info = ntf.userInfo as? [String: Any],
              let state = info[YCProduct.connecteStateKey] as? YCProductState else {
            return
        }
        
        print("===  stae change 状态变化 \(state.rawValue)  == \(state == .connected)")
        RingConnectivity.shared.updateConnectionStatus(isConnected: state == .connected)
        isConnected = (state == .connected)
        NotificationCenter.default.post(name: .ringConnectionStatus, object: nil, userInfo: ntf.userInfo)
    }
    
    private func startScanDevicesV2(result: @escaping FlutterResult) {
        bleServiceV2.startScanning { peripherals, error in
            if let error = error {
                result(FlutterError(code: "SCAN_ERROR",
                                    message: error.localizedDescription,
                                    details: nil))
                return
            }
            
            let deviceList = peripherals?.map { peripheral -> [String: Any] in
                return [
                    "name": peripheral.name ?? "Unknown",
                    "macId": peripheral.identifier.uuidString
                ]
            }
            
            result(deviceList)
        }
    }
    
    
    private func connectToDeviceV2(uuidString: String, result: @escaping FlutterResult) {
        print("RING-ID::",uuidString)
        bleServiceV2.connectToDevice(uuidString: uuidString) { success, error in
            if let error = error {
                result(FlutterError(code: "CONNECTION_ERROR",
                                    message: error.localizedDescription,
                                    details: nil))
                return
            }
            result(success)
        }
    }
    
    private func disconnectToDeviceV2( result: @escaping FlutterResult) {
        bleServiceV2.disconnectToDevice() { success, error in
            if let error = error {
                result(FlutterError(code: "CONNECTION_ERROR",
                                    message: error.localizedDescription,
                                    details: nil))
                return
            }
            result(success)
        }
    }
    
    private func isDeviceConnectedV2(result: @escaping FlutterResult) {
        let value : Bool = bleServiceV2.isConnected()
        result(value)
    }
    
    
    private func handleGetBatteryLevelV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "batteryLevel") { Data in
            print("Battery Level: \(Data)%")
            result(Data)
        }
    }
    
    private func getDeviceVersionV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "version") { Data in
            print("version: \(Data)%")
            result(Data)
        }
    }
    
    private func getDeviceTimeV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "deviceTime") { Data in
            print("deviceTime: \(Data)%")
            result(Data)
        }
    }
    
    
    private func getDeviceMacV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "getDeviceMac") { Data in
            print("getDeviceMac: \(Data)")
            result(Data)
        }
    }
    
    private func getTotalActivityDataWithModeV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "totalActivityDataWithMode") { Data in
            print("totalActivityDataWithMode: \(Data)%")
            result(Data)
        }
    }
    
    private func getHRVDataWithModeV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "getHRVDataWithMode") { Data in
            print("getHRVDataWithMode: \(Data)%")
            result(Data)
        }
    }
    
    private func getTemperatureDataV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "getTemperatureData") { Data in
            print("getTemperatureData: \(Data)%")
            result(Data)
        }
    }
    
    private func getSleepDataV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "getSleepData") { Data in
            print("getSleepData: \(Data)%")
            result(Data)
        }
    }
    
    private func getBloodOxygenV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "getBloodOxygen") { Data in
            print("getBloodOxygen: \(Data)%")
            result(Data)
        }
    }
    
    
    private func deleteHRVDataV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "deleteHRVData") { Data in
            print("deleteHRVData: \(Data)")
            result(Data)
        }
        
    }
    
    private func deleteTemperatureDataV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "deleteTemperatureData") { Data in
            print("deleteTemperatureData: \(Data)")
            result(Data)
        }
    }
    
    private func deleteSleepDataV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "deleteSleepData") { Data in
            print("deleteSleepData: \(Data)")
            result(Data)
        }
    }
    
    private func deleteBloodOxygenV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "deleteBloodOxygen") { Data in
            print("deleteBloodOxygen: \(Data)")
            result(Data)
        }
    }
    
    private func deleteTotalActivityDataV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "deleteTotalActivityData") { Data in
            print("deleteTotalActivityData: \(Data)")
            result(Data)
        }
    }
    
    private func postDateTimeV2(result: @escaping FlutterResult) {
        bleServiceV2.callData(type: "postDateTime") { Data in
            print("postDateTimeV2: \(Data)%")
            result(Data)
        }
    }
    
    // MARK: - PPG Wave Methods
    func startPPGWaveV2(result: @escaping FlutterResult) {
        // Clear previous data when starting a new recording
        ppgData = []
        
        bleServiceV2.callData(type: "startPPGWave") { [weak self] response in
            guard let self = self else { return }
            print("startPPGWaveV2: \(response)%")
            
            // Reset and start the progress tracking
            self.ppgMeasurementTime = 0
            self.ppgTimer?.invalidate()
            
            // Start a timer that updates progress every second
            self.ppgTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
                guard let self = self else { return }
                
                if self.ppgMeasurementTime <= self.ppgTestTime {
                    // Calculate progress percentage
                    let progressPercentage = Double(self.ppgMeasurementTime) / Double(self.ppgTestTime) * 100.0
                    
                    // Send progress update to Flutter
                    self.sendPPGProgressUpdate(percentComplete: progressPercentage)
                    
                    // Increment timer
                    self.ppgMeasurementTime += 1
                } else {
                    // Stop when we reach 100%
                    self.ppgTimer?.invalidate()
                    self.ppgTimer = nil
                }
            }
            
            // Check if the operation was successful and return true
            if response == "success" || response == "started" {
                result(true)
            } else {
                // Return true anyway to ensure the isRecording state updates in Flutter
                result(true)
                // Alternatively, we could return an error:
                // result(FlutterError(code: "PPG_ERROR", message: "Failed to start PPG wave: \(response)", details: nil))
            }
        }
    }

    func stopPPGWaveV2(result: @escaping FlutterResult) {
        // Stop the progress timer
        ppgTimer?.invalidate()
        ppgTimer = nil
        
        // Reset progress to 0
        sendPPGProgressUpdate(percentComplete: 0.0)
        
        bleServiceV2.callData(type: "stopPPGWave") { [weak self] response in
            if response == "stopped" {
                result(true)
            } else {
                result(FlutterError(code: "PPG_ERROR", message: "Failed to stop PPG wave: \(response)", details: nil))
            }
        }
    }
    
    
    private func setAutomaticHRMonitoringOneV2(
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        weeks: [String: ObjCBool],
        intervalTime: Int,
        result: @escaping FlutterResult
    ) {
        bleServiceV2.callData(
            type: "setAutomaticHRMonitoringOne",
            monitoringParams: MonitoringParams(
                startHour: startHour,
                startMinute: startMinute,
                endHour: endHour,
                endMinute: endMinute,
                weeks: weeks,
                intervalTime: intervalTime
            )
        ) { data in
            print("setAutomaticHRMonitoringOne: \(data)%")
            result(data)
        }
    }
    
    private func setAutomaticHRMonitoringTwoV2(
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        weeks: [String: ObjCBool],
        intervalTime: Int,
        result: @escaping FlutterResult
    ) {
        bleServiceV2.callData(
            type: "setAutomaticHRMonitoringTwo",
            monitoringParams: MonitoringParams(
                startHour: startHour,
                startMinute: startMinute,
                endHour: endHour,
                endMinute: endMinute,
                weeks: weeks,
                intervalTime: intervalTime
            )
        ) { data in
            print("setAutomaticHRMonitoringTwo: \(data)%")
            result(data)
        }
    }
    
    
    private func setAutomaticHRMonitoringThreeV2(
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        weeks: [String: ObjCBool],
        intervalTime: Int,
        result: @escaping FlutterResult
    ) {
        bleServiceV2.callData(
            type: "setAutomaticHRMonitoringThree",
            monitoringParams: MonitoringParams(
                startHour: startHour,
                startMinute: startMinute,
                endHour: endHour,
                endMinute: endMinute,
                weeks: weeks,
                intervalTime: intervalTime
            )
        ) { data in
            print("setAutomaticHRMonitoringThree: \(data)%")
            result(data)
        }
    }
    
    // Send PPG progress update to Flutter
    func sendPPGProgressUpdate(percentComplete: Double) {
        if let flutterViewController = window?.rootViewController as? FlutterViewController {
            let methodChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: flutterViewController.binaryMessenger)
            print("\(self.logPrefix) Sending PPG progress update: \(percentComplete)%")
            methodChannel.invokeMethod("onPPGProgressUpdate", arguments: percentComplete) { result in
                if let error = result as? FlutterError {
                    print("\(self.logPrefix) Error sending progress update to Flutter: \(error.message ?? "Unknown error")")
                }
            }
        }
    }
}

extension Notification.Name {
    static let deeplinkNotification     = Notification.Name("deeplinkNotification")
    static let ringConnectionStatus     = Notification.Name("ringConnectionStatus")
    static let ringStatusUpdate         = Notification.Name("ringStatusUpdate")
}

class RingConnectivity {
    static let shared = RingConnectivity()  // Singleton instance
    
    private init() {}  // Private initializer to prevent external instances
    
    private(set) var isConnected = false
    
    func updateConnectionStatus(isConnected: Bool) {
        self.isConnected = isConnected
        NotificationCenter.default.post(name: .ringConnectionStatus, object: nil, userInfo: ["isConnected": isConnected])
    }
}

extension AppDelegate: BleServiceDelegate {
    func connectSuccessfully() {
        NotificationCenter.default.post(name: .ringConnectionStatus, object: nil, userInfo: ["isConnected": true])
        self.isConnected = true
    }
    
    func disconnect(error: Error?) {
        NotificationCenter.default.post(name: .ringConnectionStatus, object: nil, userInfo: ["isConnected": false])
        self.isConnected = false
    }
    
    func scanWithPeripheral(_ peripheral: CBPeripheral, advertisementData: [String: Any], rssi: NSNumber) {
        // Handle scan results if needed
    }
    
    func connectFailed(error: Error?) {
        NotificationCenter.default.post(name: .ringConnectionStatus, object: nil, userInfo: ["isConnected": false])
        self.isConnected = false
    }
    
    func enableCommunicate() {
        // Handle communication enabled
    }
    
    func bleCommunicate(peripheral: CBPeripheral, data: Data) {
        // No need to parse the data here as it's already being
        // parsed in BleServiceV2 and sent via ppgWaveDataReceived
        
        // We can leave this method empty or use it for other non-PPG data types if needed
    }
    
    func getBattery(value: Int) {
        // Handle battery value if needed
    }

    func ppgWaveDataReceived(data: [Int]) {
        // This is the key method to forward PPG data to Flutter
        if let flutterViewController = window?.rootViewController as? FlutterViewController {
            let methodChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: flutterViewController.binaryMessenger)

            // Ensure we have valid data
            guard !data.isEmpty else {
                print("\(self.logPrefix) Empty PPG data received, not forwarding to Flutter")
                return
            }

            // Store the PPG data
            ppgData.append(contentsOf: data)

            // Convert to a safe format for passing through the method channel
            let safeData = data.map { NSNumber(value: $0) }

            print("\(self.logPrefix) Sending \(safeData.count) PPG data points to Flutter")

            // There appears to be a limit on how many points we can send at once
            // If we have too many points, send them in chunks
            let chunkSize = 100
            if safeData.count > chunkSize {
                for i in stride(from: 0, to: safeData.count, by: chunkSize) {
                    let end = min(i + chunkSize, safeData.count)
                    let chunk = Array(safeData[i..<end])

                    methodChannel.invokeMethod("onPPGDataReceived", arguments: chunk) { result in
                        if let error = result as? FlutterError {
                            print("\(self.logPrefix) Error sending PPG data chunk to Flutter: \(error.message ?? "Unknown error")")
                        }
                    }
                }
            } else {
                methodChannel.invokeMethod("onPPGDataReceived", arguments: safeData) { result in
                    if let error = result as? FlutterError {
                        print("\(self.logPrefix) Error sending PPG data to Flutter: \(error.message ?? "Unknown error")")
                    }
                }
            }
        }
    }

    func bluetoothTurnedOff() {
        // Notify Flutter that Bluetooth is turned off
        if let flutterViewController = window?.rootViewController as? FlutterViewController {
            let methodChannel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: flutterViewController.binaryMessenger)
            methodChannel.invokeMethod("BluetoothSwitchIsTurnedOff", arguments: nil)
        }

        // Update connection status
        NotificationCenter.default.post(name: .ringConnectionStatus, object: nil, userInfo: ["isConnected": false])
        self.isConnected = false
    }
}
