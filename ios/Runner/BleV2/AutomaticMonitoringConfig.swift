//
//  AutomaticMonitoringConfig.swift
//  Runner
//
//  Created by <PERSON><PERSON> on 07/02/25.
//
func configureAutomaticMonitoring(dataType: Int) -> MyAutomaticMonitoring_J2301A {
    let weeks = MyWeeks_J2301A(
        sunday: true,
        monday: true,
        Tuesday: true,
        Wednesday: true,
        Thursday: true,
        Friday: true,
        Saturday: true
    )
    
    let monitoringConfig = MyAutomaticMonitoring_J2301A(
        mode: Int32(2),
        startTime_Hour: Int32(0),
        startTime_Minutes: Int32(0),
        endTime_Hour: Int32(23),
        endTime_Minutes: Int32(59),
        weeks: weeks,
        intervalTime: Int32(5),
        dataType: Int32(dataType)
    )
    
    return monitoringConfig
}
