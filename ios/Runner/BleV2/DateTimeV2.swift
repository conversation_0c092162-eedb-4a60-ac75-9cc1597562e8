//
//  DateTimeV2.swift
//  Runner
//
//  Created by <PERSON><PERSON> on 07/02/25.
//

import Foundation

class DateTimeV2 {
    
    // Get the current date and time
    private let currentDate = Date()
    private let calendar = Calendar.current
    
    // Extract date components with explicit Int32 conversion
    var deviceTime: MyDeviceTime_J2301A

    init() {
        deviceTime = MyDeviceTime_J2301A(
            year: Int32(calendar.component(.year, from: currentDate)),
            month: Int32(calendar.component(.month, from: currentDate)),
            day: Int32(calendar.component(.day, from: currentDate)),
            hour: Int32(calendar.component(.hour, from: currentDate)),
            minute: Int32(calendar.component(.minute, from: currentDate)),
            second: Int32(calendar.component(.second, from: currentDate))
        )
    }
}
