import CoreBluetooth
import Foundation

// MARK: - BLE Service Constants
private enum BLEConstants {
    static let SCAN_TIMEOUT: TimeInterval = 6
}

struct MonitoringParams {
    let startHour: Int
    let startMinute: Int    
    let endHour: Int
    let endMinute: Int
    let weeks: [String: ObjCBool]
    let intervalTime: Int
}

// MARK: - BLE Service Delegate Protocol
protocol BleServiceDelegate: AnyObject {
    func connectSuccessfully()
    func disconnect(error: Error?)
    func scanWithPeripheral(_ peripheral: CBPeripheral, advertisementData: [String: Any], rssi: NSNumber)
    func connectFailed(error: Error?)
    func enableCommunicate()
    func bleCommunicate(peripheral: CBPeripheral, data: Data)
    func getBattery(value: Int)
    func ppgWaveDataReceived(data: [Int])
    func bluetoothTurnedOff()
}

// MARK: - BLE Command Queue Management
class Command {
    let type: String
    var data: NSMutableData
    let completion: (String) -> Void
    var timer: Timer?
    var activityArray: [[String: Any]] = []
    var count: Int = 0
    let monitoringParams: MonitoringParams?
    
    init(type: String, data: NSMutableData, completion: @escaping (String) -> Void, monitoringParams: MonitoringParams? = nil) {
        self.type = type
        self.data = data
        self.completion = completion
        self.monitoringParams = monitoringParams
    }
}

class BleServiceV2: NSObject {
    // MARK: - Properties
    static let shared = BleServiceV2()
    weak var delegate: BleServiceDelegate?
    
    private var centralManager: CBCentralManager!
    private var discoveredPeripherals: [CBPeripheral] = []
    private var connectedPeripheral: CBPeripheral?
    private let bleSDK = BleSDK_J2301A.sharedManager()!
    private var connectionCompletion: ((Bool, Error?) -> Void)?
    private var disconnectionCompletion: ((Bool, Error?) -> Void)?
    private var targetUUIDString: String?
    
   
    private static let COMMAND_TIMEOUT = 15.0
    
    private var commandQueue: [Command] = []
    private var currentCommand: Command?
    
    // MARK: - Initialization
    private override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    

    
    // MARK: - Public Methods
    func isConnected() -> Bool {
        return connectedPeripheral != nil
    }
    
    func startScanning(completion: @escaping ([CBPeripheral]?, Error?) -> Void) {
        guard centralManager.state == .poweredOn else {
            completion(nil, NSError(domain: "BleServiceV2",
                                    code: 100,
                                    userInfo: [NSLocalizedDescriptionKey: "Bluetooth unavailable"]))
            return
        }
        
        discoveredPeripherals.removeAll()
        centralManager.scanForPeripherals(withServices: nil, options: nil)
        
        DispatchQueue.global().asyncAfter(deadline: .now() + BLEConstants.SCAN_TIMEOUT) { [weak self] in
            self?.centralManager.stopScan()
            completion(self?.discoveredPeripherals, nil)
        }
    }
    
    
    func disconnectToDevice(completion: ((Bool, Error?) -> Void)? = nil) {
            guard let peripheral = connectedPeripheral else {
                completion?(false, NSError(domain: "BleServiceV2",
                                         code: 104,
                                         userInfo: [NSLocalizedDescriptionKey: "No connected device"]))
                return
            }
            
            disconnectionCompletion = completion
            centralManager.cancelPeripheralConnection(peripheral)
        }
    
    func connectToDevice(uuidString: String, completion: @escaping (Bool, Error?) -> Void) {
        self.connectionCompletion = completion
        self.targetUUIDString = uuidString
        
        if let connectedPeripheral = connectedPeripheral,
           connectedPeripheral.identifier.uuidString == uuidString {
            completion(true, nil)
            return
        }
        
        if let peripheral = discoveredPeripherals.first(where: { $0.identifier.uuidString == uuidString }) {
            centralManager.connect(peripheral, options: nil)
        } else {
            startScanning { [weak self] _, error in
                guard let self = self else { return }
                
                if let error = error {
                    completion(false, error)
                    return
                }
                
                if let peripheral = self.discoveredPeripherals.first(where: { $0.identifier.uuidString == uuidString }) {
                    self.centralManager.connect(peripheral, options: nil)
                } else {
                    completion(false, NSError(domain: "BleServiceV2",
                                             code: 102,
                                             userInfo: [NSLocalizedDescriptionKey: "Device not found"]))
                }
            }
        }
    }
    
    // MARK: - Device Information Methods
    func callData(type: String, monitoringParams: MonitoringParams? = nil, completion: @escaping (String) -> Void) {
        guard let data = createCommandData(type: type, monitoringParams: monitoringParams) else {
            print("[ERROR] Failed to create command data for type: \(type)")
            completion("")
            return
        }
        
        let command = Command(type: type,
                            data: data,
                                completion: completion,
                            monitoringParams: monitoringParams)
        commandQueue.append(command)
        processNextCommand()
    }
    
    private func createCommandData(type: String, monitoringParams: MonitoringParams?) -> NSMutableData? {
        switch type {
        case "batteryLevel":
            return bleSDK.getDeviceBatteryLevel()
        case "version":
            return bleSDK.getDeviceVersion()
        case "getDeviceMac":
            return bleSDK.getDeviceMacAddress()
        case "deviceTime":
            return bleSDK.getDeviceTime()
        case "getHRVDataWithMode":
            return bleSDK.getHRVData(withMode: 0, withStart: nil)
        case "getTemperatureData":
            return bleSDK.getTemperatureData(withMode: 0, withStart: nil)
        case "getSleepData":
            return bleSDK.getDetailSleepData(withMode: 0, withStart: nil)
        case "getBloodOxygen":
            return bleSDK.getAutomaticSpo2Data(withMode: 0, withStart: nil)
        case "totalActivityDataWithMode":
            return bleSDK.getDetailActivityData(withMode: 0, withStart: nil)
        case "deleteHRVData":
            return bleSDK.getHRVData(withMode: 0x99, withStart: nil)
        case "deleteTemperatureData":
            return bleSDK.getTemperatureData(withMode: 0x99, withStart: nil)
        case "deleteSleepData":
            return bleSDK.getDetailSleepData(withMode: 0x99, withStart: nil)
        case "deleteBloodOxygen":
            return bleSDK.getAutomaticSpo2Data(withMode: 0x99, withStart: nil)
        case "deleteTotalActivityData":
            return bleSDK.getDetailActivityData(withMode: 0x99, withStart: nil)
        case "postDateTime":
            let dateTime = DateTimeV2()
            return bleSDK.setDeviceTime(dateTime.deviceTime)
        case "startPPGWave":
            return bleSDK.ppg(withMode: 1, ppgStatus: 0)
        case "stopPPGWave":
            return bleSDK.ppg(withMode: 3, ppgStatus: 0)
        case "setAutomaticHRMonitoringOne",
             "setAutomaticHRMonitoringTwo",
             "setAutomaticHRMonitoringThree":
            return createMonitoringConfigData(type: type, monitoringParams: monitoringParams)
        default:
            return nil
        }   
    }
    
    private func createMonitoringConfigData(type: String, monitoringParams: MonitoringParams?) -> NSMutableData? {
        guard let params = monitoringParams else { return nil }
        
        let weeks = MyWeeks_J2301A(
            sunday: params.weeks["sunday"] ?? true,
            monday: params.weeks["monday"] ?? true,
            Tuesday: params.weeks["tuesday"] ?? true,
            Wednesday: params.weeks["wednesday"] ?? true,
            Thursday: params.weeks["thursday"] ?? true,
            Friday: params.weeks["friday"] ?? true,
            Saturday: params.weeks["saturday"] ?? true
        )
        
        let dataType: Int32
        switch type {
        case "setAutomaticHRMonitoringOne": dataType = 2
        case "setAutomaticHRMonitoringTwo": dataType = 3
        case "setAutomaticHRMonitoringThree": dataType = 4
        default: return nil
        }
        
        let monitoringConfig = MyAutomaticMonitoring_J2301A(
            mode: Int32(2),
            startTime_Hour: Int32(params.startHour),
            startTime_Minutes: Int32(params.startMinute),
            endTime_Hour: Int32(params.endHour),
            endTime_Minutes: Int32(params.endMinute),
            weeks: weeks,
            intervalTime: Int32(params.intervalTime),
            dataType: dataType
        )
        
        return bleSDK.setAutomaticHRMonitoring(monitoringConfig)
    }
    
    // Optimize command queue processing
    private func processNextCommand() {
        // If there are no commands in the queue or we're already processing one, return
        if commandQueue.isEmpty || currentCommand != nil {
            return
        }
        
        // Get the next command
        let command = commandQueue.removeFirst()
        currentCommand = command
        
        // Set a timeout
        command.timer = Timer.scheduledTimer(withTimeInterval: BleServiceV2.COMMAND_TIMEOUT, repeats: false) { [weak self] _ in
            print("[ERROR] Command timeout: \(command.type)")
            self?.currentCommand?.completion("[ERROR] Command timed out")
            self?.currentCommand = nil
            // Process the next command after a minimal delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                self?.processNextCommand()
            }
        }
        
        // Execute the command
        guard let peripheral = connectedPeripheral else {
            command.timer?.invalidate()
            command.completion("[ERROR] No device connected")
            currentCommand = nil
            // Process the next command after a minimal delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                self.processNextCommand()
            }
            return
        }
        
        // Send the command to the device
        writeValue(
            serviceUUID: BLEConstantObjC.serviceUUID.uuidString,
            characteristicUUID: BLEConstantObjC.sendCharacteristicUUID.uuidString,
            peripheral: peripheral,
            data: command.data
        )
    }
}

// MARK: - CBCentralManagerDelegate
extension BleServiceV2: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            print("Bluetooth is powered on")
        case .poweredOff:
            print("Bluetooth is powered off")
            // Notify Flutter that Bluetooth is turned off
            delegate?.bluetoothTurnedOff()
        case .unsupported:
            print("Bluetooth is unsupported")
        case .unauthorized:
            print("Bluetooth is unauthorized")
        case .resetting:
            print("Bluetooth is resetting")
        case .unknown:
            print("Bluetooth state is unknown")
        @unknown default:
            print("Unknown Bluetooth state")
        }
    }
    
    func centralManager(_ central: CBCentralManager,
                        didDiscover peripheral: CBPeripheral,
                        advertisementData: [String: Any],
                        rssi RSSI: NSNumber) {
        if !discoveredPeripherals.contains(peripheral) {
            discoveredPeripherals.append(peripheral)
            delegate?.scanWithPeripheral(peripheral, advertisementData: advertisementData, rssi: RSSI)
        }
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        connectedPeripheral = peripheral
        peripheral.delegate = self
        peripheral.discoverServices(nil)
        delegate?.connectSuccessfully()
        
        if let completion = connectionCompletion,
           let targetUid = targetUUIDString,
           peripheral.identifier.uuidString == targetUid {
            completion(true, nil)
            connectionCompletion = nil
            targetUUIDString = nil
        }
    }
    
    func centralManager(_ central: CBCentralManager,
                        didFailToConnect peripheral: CBPeripheral,
                        error: Error?) {
        delegate?.connectFailed(error: error)
        
        if let completion = connectionCompletion {
            completion(false, error ?? NSError(domain: "BleServiceV2",
                                             code: 103,
                                             userInfo: [NSLocalizedDescriptionKey: "Failed to connect to device"]))
            connectionCompletion = nil
            targetUUIDString = nil
        }
    }
    
    func centralManager(_ central: CBCentralManager,
                           didDisconnectPeripheral peripheral: CBPeripheral,
                           error: Error?) {
           connectedPeripheral = nil
           delegate?.disconnect(error: error)
           
           // Complete disconnection callback
           disconnectionCompletion?(error == nil, error)
           disconnectionCompletion = nil
           
           currentCommand = nil
           commandQueue.removeAll()
       }
}

// MARK: - CBPeripheralDelegate
extension BleServiceV2: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        guard error == nil else {
            print("Error discovering services: \(error!.localizedDescription)")
            return
        }
        
        peripheral.services?.forEach { service in
            peripheral.discoverCharacteristics(nil, for: service)
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral,
                    didDiscoverCharacteristicsFor service: CBService,
                    error: Error?) {
        guard error == nil else {
            print("Error discovering characteristics: \(error!.localizedDescription)")
            return
        }
        
        delegate?.enableCommunicate()
    }
}

struct BLEConstantObjC {
    static let serviceUUID = CBUUID(string: "FFF0")
    static let sendCharacteristicUUID = CBUUID(string: "FFF6")
    static let receiveCharacteristicUUID = CBUUID(string: "FFF7")
    
    static let allowedDeviceIdCharacters = CharacterSet(charactersIn: "ABCDEFabcdef0123456789")
    static let allowedDeviceNameCharacters = CharacterSet(charactersIn: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ")
    static let allowedTemperatureCharacters = CharacterSet(charactersIn: "0123456789-")
}

extension BleServiceV2 {
    func writeValue(serviceUUID: String,
                    characteristicUUID: String,
                    peripheral: CBPeripheral,
                    data: NSMutableData) {
        guard let service = findService(fromUUID: serviceUUID, in: peripheral),
              let characteristic = findCharacteristic(fromUUID: characteristicUUID, in: service) else {
            print("[ERROR] Failed to find service/characteristic")
            return
        }
        
        // Enable notifications for relevant characteristics
        [BLEConstantObjC.receiveCharacteristicUUID.uuidString,
         BLEConstantObjC.sendCharacteristicUUID.uuidString].forEach { uuid in
            if let characteristic = findCharacteristic(fromUUID: uuid, in: service) {
                peripheral.setNotifyValue(true, for: characteristic)
            }
        }
        
        peripheral.writeValue(data as Data, for: characteristic, type: .withResponse)
    }
    
    func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            print("Write failed: \(error.localizedDescription)")
            currentCommand?.timer?.invalidate()
            currentCommand?.completion("[ERROR] Write failed: \(error.localizedDescription)")
            currentCommand = nil
            processNextCommand()
            return
        }
        //peripheral.readValue(for: characteristic)
    }
    
    // Safely call the delegate on the main thread
    private func safelyNotifyDelegateWithPPGData(_ data: [Int]) {
        // Ensure we have valid data
        guard !data.isEmpty else { return }
        
        // Ensure delegate calls happen on the main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let delegate = self.delegate else { return }
            delegate.ppgWaveDataReceived(data: data)
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?) {
        guard error == nil else {
            print("[ERROR] Error updating value for characteristic: \(error!.localizedDescription)")
            return
        }
        
        guard let data = characteristic.value else {
            print("[ERROR] No data received from characteristic: \(characteristic.uuid.uuidString)")
            return
        }
        
        print("[INFO] Received data from \(characteristic.uuid.uuidString): \(data.hexString)")
        
        // Parse data using the same approach as demo code - EXACT match
        if characteristic.uuid.uuidString == BLEConstantObjC.receiveCharacteristicUUID.uuidString {
            if let deviceData = bleSDK.dataParsing(with: data) {
                print("[DEBUG] Received data type: \(deviceData.dataType.rawValue)")

                // Check for multiple possible PPG data types to match Android behavior
                // Android uses Blood_glucose_data (type 119), but iOS might use different types
                if deviceData.dataType.rawValue == 70 || deviceData.dataType.rawValue == 119 {
                    print("[DEBUG] PPG data detected (type \(deviceData.dataType.rawValue))")
                    if let dicData = deviceData.dicData {
                        print("[DEBUG] Available keys in dicData: \(dicData.keys)")

                        // Try multiple possible keys to match Android behavior
                        var ppgArray: [NSNumber]? = nil

                        // First try "PPG" key (same as Android)
                        if let ppgData = dicData["PPG"] as? [NSNumber] {
                            ppgArray = ppgData
                            print("[DEBUG] Found PPG data using 'PPG' key")
                        }
                        // Try "arrayPPGData" key
                        else if let ppgData = dicData["arrayPPGData"] as? [NSNumber] {
                            ppgArray = ppgData
                            print("[DEBUG] Found PPG data using 'arrayPPGData' key")
                        }
                        // Try other possible PPG keys
                        else if let ppgData = dicData["ppgData"] as? [NSNumber] {
                            ppgArray = ppgData
                            print("[DEBUG] Found PPG data using 'ppgData' key")
                        }

                        if let ppgArray = ppgArray {
                            // Convert NSNumber array to Int array for Swift
                            let ppgValues = ppgArray.map { $0.intValue }
                            print("[DEBUG] Extracted \(ppgValues.count) PPG values using SDK")
                            print("[DEBUG] Sample PPG values: \(Array(ppgValues.prefix(5)))")
                            print("[DEBUG] PPG value range: min=\(ppgValues.min() ?? 0), max=\(ppgValues.max() ?? 0)")

                            // Forward data to delegate
                            safelyNotifyDelegateWithPPGData(ppgValues)
                        } else {
                            print("[DEBUG] No PPG array found in data with keys: \(dicData.keys)")
                            // Print all values to help debug
                            for (key, value) in dicData {
                                print("[DEBUG] Key: \(key), Value type: \(type(of: value))")
                                if let arrayValue = value as? [Any] {
                                    print("[DEBUG] Array value with \(arrayValue.count) elements: \(Array(arrayValue.prefix(3)))")
                                } else {
                                    print("[DEBUG] Non-array value: \(value)")
                                }
                            }
                        }
                    } else {
                        print("[DEBUG] No dicData found in deviceData")
                    }
                } else {
                    // Log other data types to help identify the correct PPG data type
                    print("[DEBUG] Non-PPG data type received: \(deviceData.dataType.rawValue)")
                }
            }
        }
        
        // Process command-related data
        if [BLEConstantObjC.receiveCharacteristicUUID.uuidString,
            BLEConstantObjC.sendCharacteristicUUID.uuidString,
            BLEConstantObjC.serviceUUID.uuidString].contains(characteristic.uuid.uuidString) {
            handleReceivedData(data)
        }
    }
    
    private func handleReceivedData(_ data: Data) {
        guard let deviceData = bleSDK.dataParsing(with: data) else {
            print("[ERROR] Failed to parse device data")
            self.currentCommand?.timer?.invalidate()
            self.currentCommand?.completion("[ERROR] Data parsing failed")
            self.currentCommand = nil
            processNextCommand()
            return
        }
       
        guard var command = self.currentCommand else {
            print("[WARNING] Received data with no active command")
            return
        }
        
        let end = deviceData.dataEnd
        var responseString = ""
        
        switch command.type {
        case "batteryLevel":
            responseString = "\(deviceData.dicData?["batteryLevel"] ?? "")"
        case "version":
            responseString = "\(deviceData.dicData?["deviceVersion"] ?? "")"
        case "getDeviceMac":
            responseString = "\(deviceData.dicData?["macAddress"] ?? "")"
        case "deviceTime":
            responseString = "\(deviceData.dicData?["deviceTime"] ?? "")"
        case "postDateTime":
            responseString = "\(deviceData.dataEnd)"
        case "stopPPGWave":
            responseString = "stopped"
        case "deleteHRVData",
             "deleteTemperatureData",
             "deleteSleepData",
             "deleteBloodOxygen",
             "deleteTotalActivityData":
            responseString = end ? "success" : "failed"
        case "setAutomaticHRMonitoringOne",
             "setAutomaticHRMonitoringTwo",
             "setAutomaticHRMonitoringThree":
            responseString = "\(deviceData.dicData ?? [:])"
        default:
            handlePaginatedData(currentCommand: &command, deviceData: deviceData, end: end)
            return
        }
        
        command.timer?.invalidate()
        command.completion(responseString)
        self.currentCommand = nil
        processNextCommand()
    }
    
    private func handlePaginatedData(currentCommand: inout Command, deviceData: DeviceData_J2301A, end: Bool) {
        let arrayKey: String
        let nextMode: Int32?
        
        switch currentCommand.type {
        case "totalActivityDataWithMode":
            arrayKey = "arrayDetailActivityData"
            nextMode = 2
        case "getHRVDataWithMode":
            arrayKey = "arrayHrvData"
            nextMode = 2
        case "getTemperatureData":
            arrayKey = "arrayemperatureData"
            nextMode = 2
        case "getSleepData":
            arrayKey = "arrayDetailSleepData"
            nextMode = 2
        case "getBloodOxygen":
            arrayKey = "arrayAutomaticSpo2Data"
            nextMode = 2
        default:
            return
        }
        
        currentCommand.count += 1
        if let newData = deviceData.dicData?[arrayKey] as? [[String: Any]] {
            currentCommand.activityArray.append(contentsOf: newData)
        }
        
        if currentCommand.count == 50 && !end {
            guard let nextData = getNextPageData(type: currentCommand.type, mode: nextMode) else {
                completeCommand(currentCommand, data: currentCommand.activityArray)
                return
            }
            currentCommand.data = nextData
            self.currentCommand = currentCommand
            writeValue(
                serviceUUID: BLEConstantObjC.serviceUUID.uuidString,
                characteristicUUID: BLEConstantObjC.sendCharacteristicUUID.uuidString,
                peripheral: connectedPeripheral!,
                data: nextData
            )
        } else if end {
            completeCommand(currentCommand, data: currentCommand.activityArray)
        } else {
            self.currentCommand = currentCommand
        }
    }
    
    private func getNextPageData(type: String, mode: Int32?) -> NSMutableData? {
        switch type {
        case "totalActivityDataWithMode":
            return bleSDK.getDetailActivityData(withMode: mode ?? 0, withStart: nil)
        case "getHRVDataWithMode":
            return bleSDK.getHRVData(withMode: mode ?? 0, withStart: nil)
        case "getTemperatureData":
            return bleSDK.getTemperatureData(withMode: mode ?? 0, withStart: nil)
        case "getSleepData":
            return bleSDK.getDetailSleepData(withMode: mode ?? 0, withStart: nil)
        case "getBloodOxygen":
            return bleSDK.getAutomaticSpo2Data(withMode: mode ?? 0, withStart: nil)
        default:
            return nil
        }
    }
    
    private func completeCommand(_ command: Command, data: Any) {
        command.completion("\(data)")
        self.currentCommand = nil
        processNextCommand()
    }
    
    // Helper functions
    private func findService(fromUUID serviceUUID: String, in peripheral: CBPeripheral) -> CBService? {
        peripheral.services?.first { $0.uuid.uuidString.lowercased() == serviceUUID.lowercased() }
    }
    
    private func findCharacteristic(fromUUID UUID: String, in service: CBService) -> CBCharacteristic? {
        service.characteristics?.first { $0.uuid.uuidString.lowercased() == UUID.lowercased() }
    }
}


extension Data {
    var hexString: String {
        map { String(format: "%02x", $0) }.joined(separator: " ")
    }
}
