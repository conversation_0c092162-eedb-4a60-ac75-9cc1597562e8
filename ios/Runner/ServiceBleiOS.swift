import UIKit
import Y<PERSON>roductSDK
import CoreBluetooth


class ServiceBle {
    static let shared = ServiceBle()
    private init() {}
    private var discoveredPeripherals: [CBPeripheral] = []
    func getScannedDevices(completion: @escaping ([[String: Any]]?, Error?) -> Void) {
        YCProduct.scanningDevice(delayTime: 6.0) { devices, error in
            if let error = error {
                completion(nil, error)  
            } else {
                self.discoveredPeripherals = devices
                let devicesList = devices.map { device in
                    self.convertPeripheralToMap(device)
                }
                print(devicesList)
                completion(devicesList, nil)
                
            }
        }
    }
    
    private func convertPeripheralToMap(_ peripheral: CBPeripheral) -> [String: Any] {
        return [
            "name": peripheral.name ?? "Unknown",  
            "macId": peripheral.identifier.uuidString
        ]
    }

private func convertPeripheralArrayToMap(_ peripherals: [CBPeripheral]) -> [[String: Any]] {
    
    
    
    return peripherals.map { peripheral in
        return [
            "name": peripheral.name ?? "Unknown",
            "macId": peripheral.identifier.uuidString
        ]
    }
}


    func connectDevice(macId: String, completion: @escaping(_ connected: Bool, _ error: NSError?) -> Void) {
        print("peripherals: ",discoveredPeripherals);
        if let peripheral = discoveredPeripherals.first(where: { $0.identifier.uuidString == macId }) {
            YCProduct.connectDevice(peripheral) { state, error in
                RingConnectivity.shared.updateConnectionStatus(isConnected: state == .connected)
                completion(RingConnectivity.shared.isConnected, error)
            }
        } else {
            completion(false, NSError(domain: "ServiceBle", code: 404, userInfo: [NSLocalizedDescriptionKey: "Device not found"]))
        }
    }
    
    
func currentConnectedDevice(completion: @escaping ([[String: Any]]?, Error?) -> Void) {
    let connectedPeripherals = YCProduct.shared.connectedPeripherals
    if !connectedPeripherals.isEmpty {
        let peripheralMaps = convertPeripheralArrayToMap(connectedPeripherals)
        completion(peripheralMaps, nil)
    } else {
        completion(nil, NSError(domain: "ServiceBle",
                              code: 404,
                              userInfo: [NSLocalizedDescriptionKey: "No connected device found"]))
    }
}
    
    func getHealthData(type: String, completion: @escaping ([ Any]?, Error?) -> Void) {
        if type == "sleep" {
            YCProduct.queryHealthData(dataType: YCQueryHealthDataType.sleep) { state, response in
                if state == .succeed, let datas = response as? [YCHealthDataSleep] {
                    var arr = [Any]()
                    for info in datas {
                        if let obj = self.convertToDictionary(object: info), let toMutCopy = obj.mutableCopy() as? [String: Any] {
                            var obj1 = toMutCopy
                            if let details = obj["sleepDetailDatas"] as? [YCHealthDataSleepDetail] {
                                var slpArr = [Any]()
                                for slp in details {

                                    let slpDict = self.convertSleepDetailToDictionary(slp)
                                    slpArr.append(slpDict)
                                }
                                obj1["sleepDetailDatas"] = slpArr
                            } else {
                                obj1["sleepDetailDatas"] = []
                            }
                            arr.append(obj1)
                        }
                    }
                    
                    completion(arr,nil)
                } else {
                    completion([],nil)
                }
            }
        }
        else{
            let dataType = HealthDataType.fromString(type);
            YCProduct.queryHealthData(dataType: dataType) { state, response in
                if state == .succeed, let datas = response as? [NSObject] {
                    print(datas)
                    let arr = datas.compactMap { self.convertToDictionary(object: $0) }
                    completion(arr, nil)
                } else {
                    completion([], nil)
                }
            }
        }
    }

    
    func convertSleepDetailToDictionary(_ detail: YCHealthDataSleepDetail) -> [String: Any] {
        return [
            "sleepType": detail.sleepType.rawValue,
            "startTimeStamp": detail.startTimeStamp,
            "duration": detail.duration,
        ]
    }
    
    func convertToDictionary(object: NSObject) -> NSDictionary? {
        let mirror = Mirror(reflecting: object)
        var dict = [String: Any]()
        
        for child in mirror.children {
            if let key = child.label {
                dict[key] = child.value
            }
        }
        return dict as NSDictionary
        
    }
    func getDeviceInfo(completion: @escaping ([String: Any]?, Error?) -> Void) {
        var toData      = [String: Any]()
        
        YCProduct.queryDeviceMacAddress { state, response in
            if state == YCProductState.succeed, let macaddress = response as? String {
                toData["macId"] = macaddress
            }
            
            YCProduct.queryDeviceBasicInfo { state, response in
                if state == YCProductState.succeed, let info = response as? YCDeviceBasicInfo {
                    toData["deviceId"] = info.deviceID
                    toData["deviceBatteryValue"] = info.batteryPower
                    toData["deviceVersion"] = info.mcuFirmware.version
                }
                
                YCProduct.queryDeviceModel { state, response in
                    if state == YCProductState.succeed,
                       let name = response as? String {
                        toData["deviceModel"] = name
                    }
                    completion(toData, nil)
                }
            }
        }
    }

func deleteHealthData(type: String, completion: @escaping(_ connected: Bool, _ error: NSError?) -> Void) {
        let dataType = HealthDataDeleteType.fromString(type);
            YCProduct.deleteHealthData(dataType: dataType) { state, response in
            completion(true, nil)  
        }
    }

}


enum HealthDataType: String {
    case sport = "sport"
    case sleep = "sleep"
    case combinedData = "combined_data"
    
    static func fromString(_ type: String) -> YCQueryHealthDataType {
        switch type {
        case HealthDataType.sport.rawValue:
            return YCQueryHealthDataType.step  
        case HealthDataType.sleep.rawValue:
            return YCQueryHealthDataType.sleep  
        case HealthDataType.combinedData.rawValue:
            return YCQueryHealthDataType.combinedData
        default:
            return YCQueryHealthDataType.step  
        }
    }
}

enum HealthDataDeleteType: String {
    case sport = "sport"
    case sleep = "sleep"
    case combinedData = "combined_data"
    
    static func fromString(_ type: String) -> YCDeleteHealthDataType {
        switch type {
        case HealthDataType.sport.rawValue:
            return YCDeleteHealthDataType.step  
        case HealthDataType.sleep.rawValue:
            return YCDeleteHealthDataType.sleep  
        case HealthDataType.combinedData.rawValue:
            return YCDeleteHealthDataType.combinedData
        default:
            return YCDeleteHealthDataType.step  
        }
    }
}
