PODS:
  - app_links (0.0.2):
    - Flutter
  - app_settings (5.1.1):
    - Flutter
  - arkit_plugin (0.0.1):
    - Flutter
    - GLTFSceneKit
  - audio_waveforms (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (5.4.4):
    - Firebase/Firestore (= 11.10.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - external_app_launcher (0.0.1):
    - Flutter
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Auth (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.10.0)
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Crashlytics (11.10.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.10.0)
  - Firebase/Firestore (11.10.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.10.0)
  - Firebase/Messaging (11.10.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.10.0)
  - Firebase/RemoteConfig (11.10.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.10.0)
  - firebase_analytics (11.4.5):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.5.3):
    - Firebase/Auth (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_crashlytics (4.3.5):
    - Firebase/Crashlytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.0):
    - Firebase/Messaging (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.3.0):
    - Firebase/RemoteConfig (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.12.0)
  - FirebaseAuth (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.12.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseFirestore (11.10.0):
    - FirebaseFirestoreBinary (= 11.10.0)
  - FirebaseFirestoreAbseilBinary (1.2024072200.0)
  - FirebaseFirestoreBinary (11.10.0):
    - FirebaseCore (= 11.10.0)
    - FirebaseCoreExtension (= 11.10.0)
    - FirebaseFirestoreInternalBinary (= 11.10.0)
    - FirebaseSharedSwift (= 11.10.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.69.0)
  - FirebaseFirestoreGRPCCoreBinary (1.69.0):
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.69.0)
  - FirebaseFirestoreGRPCCPPBinary (1.69.0):
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.69.0)
  - FirebaseFirestoreInternalBinary (11.10.0):
    - FirebaseCore (= 11.10.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.10.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseRemoteConfigInterop (11.12.0)
  - FirebaseSessions (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.10.0)
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - GLTFSceneKit (0.3.0)
  - google_mlkit_commons (0.11.0):
    - Flutter
    - MLKitVision
  - google_mlkit_face_detection (0.13.1):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/FaceDetection (~> 7.0.0)
  - google_mlkit_face_mesh_detection (0.4.1):
    - Flutter
    - google_mlkit_commons
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/FaceDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitFaceDetection (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - health (12.2.0):
    - Flutter
  - leveldb-library (1.22.6)
  - location (0.0.1):
    - Flutter
  - MLImage (1.0.0-beta6)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitFaceDetection (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - native_device_orientation (0.0.1):
    - Flutter
  - network_info_plus (0.0.1):
    - Flutter
  - open_file_manager (0.1.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - Protobuf (3.29.4)
  - reactive_ble_mobile (0.0.1):
    - Flutter
    - FlutterMacOS
    - Protobuf (~> 3.5)
    - SwiftProtobuf (~> 1.0)
  - RecaptchaInterop (101.0.0)
  - record_darwin (1.0.0):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftProtobuf (1.29.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - workmanager (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - arkit_plugin (from `.symlinks/plugins/arkit_plugin/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - external_app_launcher (from `.symlinks/plugins/external_app_launcher/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `11.10.0`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_face_detection (from `.symlinks/plugins/google_mlkit_face_detection/ios`)
  - google_mlkit_face_mesh_detection (from `.symlinks/plugins/google_mlkit_face_mesh_detection/ios`)
  - health (from `.symlinks/plugins/health/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - native_device_orientation (from `.symlinks/plugins/native_device_orientation/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - open_file_manager (from `.symlinks/plugins/open_file_manager/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - reactive_ble_mobile (from `.symlinks/plugins/reactive_ble_mobile/darwin`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GLTFSceneKit
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - leveldb-library
    - MLImage
    - MLKitCommon
    - MLKitFaceDetection
    - MLKitVision
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - Protobuf
    - RecaptchaInterop
    - SwiftProtobuf

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  arkit_plugin:
    :path: ".symlinks/plugins/arkit_plugin/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  external_app_launcher:
    :path: ".symlinks/plugins/external_app_launcher/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.10.0
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_face_detection:
    :path: ".symlinks/plugins/google_mlkit_face_detection/ios"
  google_mlkit_face_mesh_detection:
    :path: ".symlinks/plugins/google_mlkit_face_mesh_detection/ios"
  health:
    :path: ".symlinks/plugins/health/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  native_device_orientation:
    :path: ".symlinks/plugins/native_device_orientation/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  open_file_manager:
    :path: ".symlinks/plugins/open_file_manager/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  reactive_ble_mobile:
    :path: ".symlinks/plugins/reactive_ble_mobile/darwin"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.10.0

SPEC CHECKSUMS:
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  app_settings: 58017cd26b604ae98c3e65acbdd8ba173703cc82
  arkit_plugin: c08c4e6f334a922154c2caf2f6fadfaefb51c181
  audio_waveforms: cd736909ebc6b6a164eb74701d8c705ce3241e1c
  camera_avfoundation: adb0207d868b2d873e895371d88448399ab78d87
  cloud_firestore: d314324d46ae559abb7711230571c41ad3e8d891
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  external_app_launcher: ad55ac844aa21f2d2197d7cec58ff0d0dc40bbc0
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 4b8609ce8d2e0c8928472bec8d9753a8f1835eb6
  firebase_auth: 3f532201cbdc7cd6dfc3bfa89affc0c294111e20
  firebase_core: 432718558359a8c08762151b5f49bb0f093eb6e0
  firebase_crashlytics: f3b2649ae73f440895f285021bf70bdf4d36dec4
  firebase_messaging: ab76f0c1569e08acd26aa2f595a0597d3dbf1458
  firebase_remote_config: 1f12424f6914dfe832ae0c4460ef7c6aa586aa90
  FirebaseABTesting: dfc10eb6cc08fe3b391ac9e5aa40396d43ea6675
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseAppCheckInterop: 73b173e5ec45192e2d522ad43f526a82ad10b852
  FirebaseAuth: c4146bdfdc87329f9962babd24dae89373f49a32
  FirebaseAuthInterop: b583210c039a60ed3f1e48865e1f3da44a796595
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseCrashlytics: 84b073c997235740e6a951b7ee49608932877e5c
  FirebaseFirestore: e5c3d65fef6c6e07c47af2130aaadfb89dd07ea7
  FirebaseFirestoreAbseilBinary: 4cfa8823cedc1b774843e04fe578ad279b387f97
  FirebaseFirestoreBinary: 6cf15472267bbb89ce9ac5e645eb0abae29208ce
  FirebaseFirestoreGRPCBoringSSLBinary: c3dfef3ff448ae2c1c85f9baf9fac5afc4db99fa
  FirebaseFirestoreGRPCCoreBinary: 565534e160a0415d12185f7f171c52a567382fbd
  FirebaseFirestoreGRPCCPPBinary: 6c0134e8d230ee58b9d51dec2a30a48efd6d5dc7
  FirebaseFirestoreInternalBinary: 96b309279c4efdf00b83ab80e8af4d0a73d30258
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseMessaging: 2b9f56aa4ed286e1f0ce2ee1d413aabb8f9f5cb9
  FirebaseRemoteConfig: 10695bc0ce3b103e3706a5578c43f2a9f69d5aaa
  FirebaseRemoteConfigInterop: 82b81fd06ee550cbeff40004e2c106daedf73e38
  FirebaseSessions: 9b3b30947b97a15370e0902ee7a90f50ef60ead6
  FirebaseSharedSwift: 1baacae75939499b5def867cbe34129464536a38
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_native_splash: df59bb2e1421aa0282cb2e95618af4dcb0c56c29
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  GLTFSceneKit: d1b437c514ba2aed5555afb0fddd64e030612d57
  google_mlkit_commons: 2544377e5503bf78ee7412ccfce0394c2dcb8a6b
  google_mlkit_face_detection: 111b3ac2f2df6ad5f464bafed9ba380ac8366d37
  google_mlkit_face_mesh_detection: 0bf0802f4d6e26942b6992d578fc86a72f20e21b
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  health: e739f88e8030f47660f93bf7af2ae775b01766dd
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitFaceDetection: 2a593db4837db503ad3426b565e7aab045cefea5
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  native_device_orientation: 3b4cfc9565a7b879cc4fde282b3e27745e852d0d
  network_info_plus: 6613d9d7cdeb0e6f366ed4dbe4b3c51c52d567a9
  open_file_manager: 1917fdd1bae7afc9236da9bd1a4b0c8cb5b81d0c
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  Protobuf: 2e6de032ba12b9efb390ae550d1a243a5b19ddfc
  reactive_ble_mobile: 856183aeda09f2a676bbaecbea8915c3cb1d33df
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  record_darwin: 3b1a8e7d5c0cbf45ad6165b4d83a6ca643d929c3
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  SwiftProtobuf: b7aa08087e2ab6d162862d143020091254095f69
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  workmanager: 0afdcf5628bbde6924c21af7836fed07b42e30e6

PODFILE CHECKSUM: e4cc3d1f665b576544470cbb427ecd00a97bf8a5

COCOAPODS: 1.16.2
